<template>
  <div :class="prefixCls" class="login-background-img">
    <AppLocalePicker class="absolute top-4 right-4 enter-x xl:text-gray-600" :showText="false"/>
    <AppDarkModeToggle class="absolute top-3 right-7 enter-x" />
    <div class="aui-logo" v-if="!getIsMobile">
      <div>
        <h3>
          <img :src="logoImg" alt="jeecg" />
        </h3>
      </div>
    </div>
    <div v-else class="aui-phone-logo">
      <img :src="logoImg" alt="jeecg" />
    </div>
    <div v-show="type === 'login'">
      <div class="aui-content">
        <div class="aui-container">
          <div class="aui-form">
            <div class="aui-image">
              <div class="aui-image-text">
                <img :src="adTextImg" />
              </div>
            </div>
            <div class="aui-formBox">
              <div class="aui-formWell">
                <div class="aui-flex aui-form-nav investment_title">
                  <div class="aui-flex-box" :class="activeIndex === 'accountLogin' ? 'activeNav on' : ''" @click="loginClick('accountLogin')"
                    >{{ t('sys.login.signInFormTitle') }}
                  </div>
                  <div class="aui-flex-box" :class="activeIndex === 'phoneLogin' ? 'activeNav on' : ''" @click="loginClick('phoneLogin')"
                    >{{ t('sys.login.mobileSignInFormTitle') }}
                  </div>
                  <div class="aui-flex-box" :class="activeIndex === 'emailLogin' ? 'activeNav on' : ''" @click="loginClick('emailLogin')"
                    >{{ t('sys.login.emailSignInFormTitle') }}
                  </div>
                </div>
                <div class="aui-form-box" style="height: 180px">
                  <a-form ref="loginRef" :model="formData" v-if="activeIndex === 'accountLogin'" @keyup.enter="loginHandleClick">
                    <div class="aui-account">
                      <!-- 用户名和密码输入框 -->
                      <div class="aui-inputClear">
                        <i class="icon icon-code"></i>
                        <a-form-item>
                          <a-input class="fix-auto-fill" :placeholder="t('sys.login.userName')" v-model:value="formData.username" />
                        </a-form-item>
                      </div>
                      <div class="aui-inputClear">
                        <i class="icon icon-password"></i>
                        <a-form-item>
                          <a-input class="fix-auto-fill" type="password" :placeholder="t('sys.login.password')" v-model:value="formData.password" />
                        </a-form-item>
                      </div>
                      <!-- 验证码区域及切换方式，放在用户名/密码下方 -->
                      <div style="margin-top: 12px;">
                        <div class="aui-inputClear" style="display: flex; align-items: center; gap: 8px; margin-bottom: 0;">
                          <template v-if="formData.captchaType === 'turnstile'">
                            <div id="turnstile-container-mini"></div>
                          </template>
                          <template v-else>
                            <a-form-item style="margin-bottom: 0; margin-left: 0;">
                              <a-input
                                class="fix-auto-fill"
                                type="text"
                                :placeholder="t('sys.login.inputCode')"
                                v-model:value="formData.inputCode"
                                style="width: 100px"
                              />
                            </a-form-item>
                            <div class="aui-code" style="margin-left: 8px;">
                              <img
                                v-if="randCodeData.requestCodeSuccess"
                                :src="randCodeData.randCodeImage"
                                @click="handleChangeCheckCode"
                                style="height: 32px"
                              />
                              <img v-else style="margin-top: 2px; max-width: initial; height: 32px;" :src="codeImg" @click="handleChangeCheckCode" />
                            </div>
                          </template>
                        </div>
                        <!-- 验证码切换方式文字，右下角 -->
                        <!-- <div style="display: flex; justify-content: flex-end; align-items: center; margin-bottom: 0; margin-top: 2px;">
                          <span
                            style="color: #409eff; cursor: pointer; font-size: 13px"
                            @click="formData.captchaType = formData.captchaType === 'turnstile' ? 'image' : 'turnstile'"
                          >
                            {{ formData.captchaType === 'turnstile' ? t('sys.login.switchToImage') : t('sys.login.switchToTurnstile') }}
                          </span>
                        </div> -->
                      </div>
                      <!-- 登录按钮 -->
                      <div class="aui-formButton" style="margin-top: 18px;">
                        <div class="aui-flex">
                          <a-button :loading="loginLoading" class="aui-link-login" type="primary" @click="loginHandleClick">
                            {{ t('sys.login.loginButton') }}
                          </a-button>
                        </div>
                      </div>
                      <!-- 记住我/忘记密码 -->
                      <div class="aui-flex" style="margin-top: 8px;">
                        <div class="aui-flex-box">
                          <div class="aui-choice">
                            <a-input class="fix-auto-fill" type="checkbox" v-model:value="rememberMe" />
                            <span style="margin-left: 5px">{{ t('sys.login.rememberMe') }}</span>
                          </div>
                        </div>
                        <div class="aui-forget">
                          <a @click="forgetHandelClick"> {{ t('sys.login.forgetPassword') }}</a>
                        </div>
                      </div>
                      <!-- 账号登录的Turnstile -->
                      <div v-if="formData.captchaType === 'turnstile'" class="aui-input-line">
                        <div id="turnstile-container-mini"></div>
                        <!-- 注释掉重新加载按钮，但保留代码
                        <a-button type="link" @click="reloadTurnstileMini" style="padding: 0; margin-top: 8px;">
                          <ReloadOutlined /> {{ t('sys.login.reloadCaptcha') }}
                        </a-button>
                        -->
                      </div>
                    </div>
                  </a-form>
                  <a-form v-else-if="activeIndex === 'phoneLogin'" ref="phoneFormRef" :model="phoneFormData" @keyup.enter="loginHandleClick">
                    <div class="aui-account phone">
                      <div class="aui-inputClear phoneClear">
                        <a-input class="fix-auto-fill" :placeholder="t('sys.login.mobile')" v-model:value="phoneFormData.mobile" />
                      </div>
                      <div class="aui-inputClear">
                        <a-input class="fix-auto-fill" :maxlength="6" :placeholder="t('sys.login.smsCode')" v-model:value="phoneFormData.smscode" />
                        <div v-if="showInterval" class="aui-code" @click="getLoginCode">
                          <a>{{ t('component.countdown.normalText') }}</a>
                        </div>
                        <div v-else class="aui-code">
                          <span class="aui-get-code code-shape">{{ t('component.countdown.sendText', [unref(timeRuning)]) }}</span>
                        </div>
                      </div>
                      <!-- 验证码区域 -->
                      <div style="margin-top: 12px;">
                        <div class="aui-inputClear" style="display: flex; align-items: center; gap: 8px; margin-bottom: 0;">
                          <template v-if="phoneFormData.captchaType === 'turnstile'">
                            <div id="turnstile-container-phone"></div>
                          </template>
                          <template v-else>
                            <a-form-item style="margin-bottom: 0; margin-left: 0;">
                              <a-input
                                class="fix-auto-fill"
                                type="text"
                                :placeholder="t('sys.login.inputCode')"
                                v-model:value="phoneFormData.inputCode"
                                style="width: 100px"
                              />
                            </a-form-item>
                            <div class="aui-code" style="margin-left: 8px;">
                              <img
                                v-if="randCodeData.requestCodeSuccess"
                                :src="randCodeData.randCodeImage"
                                @click="handleChangeCheckCode"
                                style="height: 32px"
                              />
                              <img v-else style="margin-top: 2px; max-width: initial; height: 32px;" :src="codeImg" @click="handleChangeCheckCode" />
                            </div>
                          </template>
                        </div>
                        <!-- 验证码切换方式文字 -->
                        <!-- <div style="display: flex; justify-content: flex-end; align-items: center; margin-bottom: 0; margin-top: 2px;">
                          <span
                            style="color: #409eff; cursor: pointer; font-size: 13px"
                            @click="phoneFormData.captchaType = phoneFormData.captchaType === 'turnstile' ? 'image' : 'turnstile'"
                          >
                            {{ phoneFormData.captchaType === 'turnstile' ? t('sys.login.switchToImage') : t('sys.login.switchToTurnstile') }}
                          </span>
                        </div> -->
                      </div>
                      <!-- 登录按钮 -->
                      <div class="aui-formButton" style="margin-top: 18px;">
                        <div class="aui-flex">
                          <a-button :loading="loginLoading" class="aui-link-login" type="primary" @click="loginHandleClick">
                            {{ t('sys.login.loginButton') }}
                          </a-button>
                        </div>
                      </div>
                      <!-- 手机登录的Turnstile -->
                      <div v-if="phoneFormData.captchaType === 'turnstile'" class="aui-input-line">
                        <div id="turnstile-container-phone"></div>
                        <!-- 注释掉重新加载按钮，但保留代码
                        <a-button type="link" @click="reloadTurnstilePhone" style="padding: 0; margin-top: 8px;">
                          <ReloadOutlined /> {{ t('sys.login.reloadCaptcha') }}
                        </a-button>
                        -->
                      </div>
                    </div>
                  </a-form>
                  <a-form v-else-if="activeIndex === 'emailLogin'" ref="emailFormRef" :model="emailFormData" @keyup.enter="loginHandleClick">
                    <div class="aui-account email">
                      <div class="aui-inputClear emailClear">
                        <a-input 
                          class="fix-auto-fill email-input" 
                          placeholder="Please Input Email here" 
                          v-model:value="emailFormData.email"
                          @keyup.enter="handleEmailEnterPress"
                        />
                      </div>
                      <div class="aui-inputClear">
                        <a-input 
                          class="fix-auto-fill email-code-input" 
                          :maxlength="6" 
                          placeholder="Email Verification code" 
                          v-model:value="emailFormData.emailcode" 
                          :disabled="emailCodeDisabled"
                        />
                        <button 
                          v-if="showEmailInterval" 
                          type="button" 
                          class="email-code-button" 
                          @click="getEmailLoginCode"
                        >
                          Get Email code
                        </button>
                        <div v-else class="aui-code">
                          <span class="aui-get-code code-shape">{{ t('component.countdown.sendText', [unref(emailTimeRuning)]) }}</span>
                        </div>
                      </div>
                      <!-- 验证码区域 -->
                      <div style="margin-top: 12px;">
                        <div class="aui-inputClear" style="display: flex; align-items: center; gap: 8px; margin-bottom: 0;">
                          <template v-if="emailFormData.captchaType === 'turnstile'">
                            <div id="turnstile-container-email"></div>
                          </template>
                          <template v-else>
                            <a-form-item style="margin-bottom: 0; margin-left: 0;">
                              <a-input
                                class="fix-auto-fill"
                                type="text"
                                :placeholder="t('sys.login.inputCode')"
                                v-model:value="emailFormData.inputCode"
                                style="width: 100px"
                              />
                            </a-form-item>
                            <div class="aui-code" style="margin-left: 8px;">
                              <img
                                v-if="randCodeData.requestCodeSuccess"
                                :src="randCodeData.randCodeImage"
                                @click="handleChangeCheckCode"
                                style="height: 32px"
                              />
                              <img v-else style="margin-top: 2px; max-width: initial; height: 32px;" :src="codeImg" @click="handleChangeCheckCode" />
                            </div>
                          </template>
                        </div>
                        <!-- 验证码切换方式文字 -->
                        <!-- <div style="display: flex; justify-content: flex-end; align-items: center; margin-bottom: 0; margin-top: 2px;">
                          <span
                            style="color: #409eff; cursor: pointer; font-size: 13px"
                            @click="emailFormData.captchaType = emailFormData.captchaType === 'turnstile' ? 'image' : 'turnstile'"
                          >
                            {{ emailFormData.captchaType === 'turnstile' ? t('sys.login.switchToImage') : t('sys.login.switchToTurnstile') }}
                          </span>
                        </div> -->
                      </div>
                      <!-- 登录按钮 -->
                      <div class="aui-formButton" style="margin-top: 18px;">
                        <div class="aui-flex">
                          <a-button :loading="loginLoading" class="aui-link-login" type="primary" @click="loginHandleClick">
                            {{ t('sys.login.loginButton') }}
                          </a-button>
                        </div>
                      </div>
                      <!-- 邮箱登录的Turnstile -->
                      <div v-if="emailFormData.captchaType === 'turnstile'" class="aui-input-line">
                        <div id="turnstile-container-email"></div>
                        <!-- 注释掉重新加载按钮，但保留代码
                        <a-button type="link" @click="reloadTurnstileEmail" style="padding: 0; margin-top: 8px;">
                          <ReloadOutlined /> {{ t('sys.login.reloadCaptcha') }}
                        </a-button>
                        -->
                      </div>
                    </div>
                  </a-form>
                </div>
              </div>
              <a-form @keyup.enter="loginHandleClick">
                <!-- <div class="aui-flex aui-third-text">
                  <div class="aui-flex-box aui-third-border">
                    <span>{{ t('sys.login.otherSignIn') }}</span>
                  </div>
                </div>
                
                <div class="aui-flex" :class="`${prefixCls}-sign-in-way`">
                   <div class="aui-flex-box">
                    <div class="aui-third-login">
                      <a title="github" @click="onThirdLogin('github')"><GithubFilled /></a>
                    </div>
                  </div>
                  <div class="aui-flex-box">
                    <div class="aui-third-login">
                      <a title="企业微信" @click="onThirdLogin('wechat_enterprise')"><icon-font class="item-icon" type="icon-qiyeweixin3" /></a>
                    </div>
                  </div>
                  <div class="aui-flex-box">
                    <div class="aui-third-login">
                      <a title="钉钉" @click="onThirdLogin('dingtalk')"><DingtalkCircleFilled /></a>
                    </div>
                  </div>
                  <div class="aui-flex-box">
                    <div class="aui-third-login">
                      <a title="微信" @click="onThirdLogin('wechat_open')"><WechatFilled /></a>
                    </div>
                  </div>  
                </div> -->
              </a-form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-show="type === 'forgot'" :class="`${prefixCls}-form`">
      <MiniForgotpad ref="forgotRef" @go-back="goBack" @success="handleSuccess" />
    </div>
    <div v-show="type === 'register'" :class="`${prefixCls}-form`">
      <MiniRegister ref="registerRef" @go-back="goBack" @success="handleSuccess" />
    </div>
    <div v-show="type === 'codeLogin'" :class="`${prefixCls}-form`">
      <MiniCodelogin ref="codeRef" @go-back="goBack" @success="handleSuccess" />
    </div>
    <!-- 第三方登录相关弹框 -->
    <ThirdModal ref="thirdModalRef"></ThirdModal>
    
    <!-- 图片验证码弹窗 -->
    <CaptchaModal @register="captchaRegisterModal" @ok="getLoginCode" />
  </div>
</template>
<script lang="ts" setup name="login-mini">
  import { getCaptcha, getCodeInfo, getEmailCaptcha, checkOnlyUser } from '/@/api/sys/user';
  import { computed, onMounted, reactive, ref, toRaw, unref, watch, nextTick } from 'vue';
  import codeImg from '/@/assets/images/checkcode.png';
  import { Rule } from '/@/components/Form';
  import { useUserStore } from '/@/store/modules/user';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { SmsEnum, LoginStateEnum, useLoginState } from '/@/views/sys/login/useLogin';
import ThirdModal from '/@/views/sys/login/ThirdModal.vue';
import MiniForgotpad from './MiniForgotpad.vue';
import MiniRegister from './MiniRegister.vue';
import MiniCodelogin from './MiniCodelogin.vue';
import logoImg from '/@/assets/loginmini/icon/jeecg_logo.png';
import adTextImg from '/@/assets/loginmini/icon/jeecg_ad_text.png';
import { AppLocalePicker, AppDarkModeToggle } from '/@/components/Application';
import { useLocaleStore } from '/@/store/modules/locale';
import { useDesign } from "/@/hooks/web/useDesign";
import { useAppInject } from "/@/hooks/web/useAppInject";
import { GithubFilled, WechatFilled, DingtalkCircleFilled, createFromIconfontCN, ReloadOutlined } from '@ant-design/icons-vue';
import CaptchaModal from '@/components/jeecg/captcha/CaptchaModal.vue';
import { useModal } from "@/components/Modal";
import { ExceptionEnum } from "@/enums/exceptionEnum";

  const IconFont = createFromIconfontCN({
    scriptUrl: '//at.alicdn.com/t/font_2316098_umqusozousr.js',
  });
  const { prefixCls } = useDesign('mini-login');
  const { notification, createMessage } = useMessage();
  const userStore = useUserStore();
  const { t } = useI18n();
  const localeStore = useLocaleStore();
  const showLocale = localeStore.getShowPicker;
  const randCodeData = reactive<any>({
    randCodeImage: '',
    requestCodeSuccess: false,
    checkKey: null,
  });
  const rememberMe = ref<string>('0');
  //手机号登录还是账号登录
  const activeIndex = ref('accountLogin');
  const type = ref<string>('login');
  //账号登录表单字段
  const formData = reactive<any>({
    inputCode: '',
    username: 'admin',
    password: '123456',
    captchaType: 'turnstile', // 默认Turnstile
    turnstileToken: '',
  });
  //手机号登录表单字段
  const phoneFormData = reactive<any>({
    mobile: '',
    smscode: '',
    captchaType: 'turnstile', // 默认Turnstile
    turnstileToken: '',
  });

  // 邮箱登录表单字段
  const emailFormData = reactive<any>({
    email: '',
    emailcode: '',
    captchaType: 'turnstile', // 默认Turnstile
    turnstileToken: '',
  });
  // 邮箱验证码输入框禁用状态
  const emailCodeDisabled = ref<boolean>(true);
  // 上次发送验证码的时间戳
  const lastEmailCodeSentTime = ref<number>(0);
  // 验证码发送间隔时间（毫秒）
  const EMAIL_CODE_INTERVAL = 5000; // 5秒
  const loginRef = ref();
  const emailFormRef = ref();
  //第三方登录弹窗
  const thirdModalRef = ref();
  //扫码登录
  const codeRef = ref();
  //是否显示获取验证码
  const showInterval = ref<boolean>(true);
  //60s
  const timeRuning = ref<number>(60);
  //定时器
  const timer = ref<any>(null);
  
  //是否显示获取邮箱验证码
  const showEmailInterval = ref<boolean>(true);
  //邮箱验证码60s
  const emailTimeRuning = ref<number>(60);
  //邮箱验证码定时器
  const emailTimer = ref<any>(null);
  //忘记密码
  const forgotRef = ref();
  //注册
  const registerRef = ref();
  const loginLoading = ref<boolean>(false);
  const { getIsMobile } = useAppInject();
  const [captchaRegisterModal, { openModal: openCaptchaModal }] = useModal();
  const loginState = useLoginState();

  /**
   * 获取验证码
   */
  function handleChangeCheckCode() {
    formData.inputCode = '';
    //update-begin---author:chenrui ---date:2025/1/7  for：[QQYUN-10775]验证码可以复用 #7674------------
    randCodeData.checkKey = new Date().getTime() + Math.random().toString(36).slice(-4); // 1629428467008;
    //update-end---author:chenrui ---date:2025/1/7  for：[QQYUN-10775]验证码可以复用 #7674------------
    getCodeInfo(randCodeData.checkKey).then((res) => {
      randCodeData.randCodeImage = res;
      randCodeData.requestCodeSuccess = true;
    });
  }

  /**
   * 切换登录方式
   */
  function loginClick(type) {
    activeIndex.value = type;
    formData.inputCode = '';
    handleChangeCheckCode();
    
    // 切换标签时加载对应的验证码
    nextTick(() => {
      if (type === 'accountLogin' && formData.captchaType === 'turnstile') {
        loadTurnstileMini();
      } else if (type === 'phoneLogin' && phoneFormData.captchaType === 'turnstile') {
        loadTurnstile('turnstile-container-phone', phoneFormData);
      } else if (type === 'emailLogin' && emailFormData.captchaType === 'turnstile') {
        loadTurnstile('turnstile-container-email', emailFormData);
      }
    });
  }

  /**
   * 账号或者手机或者邮箱登录
   */
  async function loginHandleClick() {
    if (unref(activeIndex) === 'accountLogin') {
      accountLogin();
    } else if (unref(activeIndex) === 'phoneLogin') {
      //手机号登录
      phoneLogin();
    } else if (unref(activeIndex) === 'emailLogin') {
      //邮箱登录
      emailLogin();
    }
  }

  async function accountLogin() {
    if (!formData.username) {
      createMessage.warn(t('sys.login.accountPlaceholder'));
      return;
    }
    if (!formData.password) {
      createMessage.warn(t('sys.login.passwordPlaceholder'));
      return;
    }
    try {
      loginLoading.value = true;
      const loginParams: any = {
        password: formData.password,
        username: formData.username,
        captchaType: formData.captchaType,
        turnstileToken: formData.captchaType === 'turnstile' ? formData.turnstileToken : undefined,
        captcha: formData.captchaType === 'image' ? formData.inputCode : undefined,
        checkKey: formData.captchaType === 'image' ? randCodeData.checkKey : undefined,
        mode: 'none',
      };
      const { userInfo } = await userStore.login(toRaw(loginParams));
      if (userInfo) {
        notification.success({
          message: t('sys.login.loginSuccessTitle'),
          description: `${t('sys.login.loginSuccessDesc')}: ${userInfo.realname}`,
          duration: 3,
        });
      }
    } catch (error) {
      notification.error({
        message: t('sys.api.errorTip'),
        description: error.message || t('sys.login.networkExceptionMsg'),
        duration: 3,
      });
      
      // 登录失败时，根据错误类型自动重新加载验证码
      if (formData.captchaType === 'turnstile') {
        // 如果错误信息包含验证码相关的关键词，自动重新加载
        if (error.message && (
            error.message.toLowerCase().includes('turnstile') || 
            error.message.toLowerCase().includes('captcha') || 
            error.message.toLowerCase().includes('验证码')
          )) {
          console.log('检测到验证码错误，自动重新加载Turnstile验证码');
          reloadTurnstileMini();
        }
      } else {
        handleChangeCheckCode();
      }
    } finally {
      loginLoading.value = false;
    }
  }

  /**
   * 手机号登录
   */
  async function phoneLogin() {
    if (!phoneFormData.mobile) {
      createMessage.warn(t('sys.login.mobilePlaceholder'));
      return;
    }
    if (!phoneFormData.smscode) {
      createMessage.warn(t('sys.login.smsPlaceholder'));
      return;
    }
    try {
      loginLoading.value = true;
      const loginParams: any = {
        mobile: phoneFormData.mobile,
        captcha: phoneFormData.smscode,
        captchaType: phoneFormData.captchaType,
        turnstileToken: phoneFormData.captchaType === 'turnstile' ? phoneFormData.turnstileToken : undefined,
        mode: 'none', //不要默认的错误提示
      };
      const { userInfo }: any = await userStore.phoneLogin(loginParams);
      if (userInfo) {
        notification.success({
          message: t('sys.login.loginSuccessTitle'),
          description: `${t('sys.login.loginSuccessDesc')}: ${userInfo.realname}`,
          duration: 3,
        });
      }
    } catch (error) {
      notification.error({
        message: t('sys.api.errorTip'),
        description: error.message || t('sys.login.networkExceptionMsg'),
        duration: 3,
      });
      
      // 登录失败时，根据错误类型自动重新加载验证码
      if (phoneFormData.captchaType === 'turnstile') {
        // 如果错误信息包含验证码相关的关键词，自动重新加载
        if (error.message && (
            error.message.toLowerCase().includes('turnstile') || 
            error.message.toLowerCase().includes('captcha') || 
            error.message.toLowerCase().includes('验证码')
          )) {
          console.log('检测到验证码错误，自动重新加载Turnstile验证码');
          reloadTurnstilePhone();
        }
      } else {
        handleChangeCheckCode();
      }
    } finally {
      loginLoading.value = false;
    }
  }

  /**
   * 邮箱登录
   */
  async function emailLogin() {
    if (!emailFormData.email) {
      createMessage.warn(t('sys.login.emailPlaceholder'));
      return;
    }
    if (!emailFormData.emailcode) {
      createMessage.warn(t('sys.login.emailCodePlaceholder'));
      return;
    }
    try {
      loginLoading.value = true;
      const loginParams: any = {
        email: emailFormData.email,
        captcha: emailFormData.emailcode,
        captchaType: emailFormData.captchaType,
        turnstileToken: emailFormData.captchaType === 'turnstile' ? emailFormData.turnstileToken : undefined,
        mode: 'none', //不要默认的错误提示
      };
      const { userInfo }: any = await userStore.emailLogin(loginParams);
      if (userInfo) {
        notification.success({
          message: t('sys.login.loginSuccessTitle'),
          description: `${t('sys.login.loginSuccessDesc')}: ${userInfo.realname}`,
          duration: 3,
        });
      }
    } catch (error) {
      notification.error({
        message: t('sys.api.errorTip'),
        description: error.message || t('sys.login.networkExceptionMsg'),
        duration: 3,
      });
      
      // 登录失败时，根据错误类型自动重新加载验证码
      if (emailFormData.captchaType === 'turnstile') {
        // 如果错误信息包含验证码相关的关键词，自动重新加载
        if (error.message && (
            error.message.toLowerCase().includes('turnstile') || 
            error.message.toLowerCase().includes('captcha') || 
            error.message.toLowerCase().includes('验证码')
          )) {
          console.log('检测到验证码错误，自动重新加载Turnstile验证码');
          reloadTurnstileEmail();
        }
      } else {
        handleChangeCheckCode();
      }
    } finally {
      loginLoading.value = false;
    }
  }

  /**
   * 处理邮箱输入框回车事件
   */
  function handleEmailEnterPress(e) {
    // 防止事件冲突
    e.stopPropagation();
    e.preventDefault();
    
    // 如果按钮可用，则发送验证码
    if (showEmailInterval.value) {
      getEmailLoginCode();
    }
  }

  /**
   * 获取邮箱验证码
   */
  async function getEmailLoginCode() {
    if (!emailFormData.email) {
      createMessage.warn(t('sys.login.emailPlaceholder'));
      return;
    }
    
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailFormData.email)) {
      createMessage.warn(t('sys.login.emailFormatError') || '邮箱格式不正确');
      return;
    }
    
    // 检查是否超过限制时间
    const now = Date.now();
    const timeSinceLastRequest = now - lastEmailCodeSentTime.value;
    if (timeSinceLastRequest < EMAIL_CODE_INTERVAL) {
      const remainingSeconds = Math.ceil((EMAIL_CODE_INTERVAL - timeSinceLastRequest) / 1000);
      createMessage.warn(`操作过于频繁，请等待 ${remainingSeconds} 秒后再试`);
      return;
    }
    
    // 检查邮箱是否已注册
    try {
      const checkResult = await checkOnlyUser({ email: emailFormData.email });
      // checkOnlyUser返回的是用户是否已存在
      // 如果success为false，说明用户已存在，可以发送验证码
      // 如果success为true，说明用户不存在，不能发送验证码
      if (checkResult && checkResult.success === true) {
        // 邮箱不存在
        createMessage.error('该邮箱未注册，请先注册账号');
        return;
      }
    } catch (error) {
      console.error('Check email existence error:', error);
      createMessage.error('检查邮箱失败，请重试');
      return;
    }
    
    // 更新发送时间戳
    lastEmailCodeSentTime.value = now;
    
    // 立即开始倒计时
    const TIME_COUNT = 60;
    if (!unref(emailTimer)) {
      emailTimeRuning.value = TIME_COUNT;
      showEmailInterval.value = false;
      emailTimer.value = setInterval(() => {
        if (unref(emailTimeRuning) > 0 && unref(emailTimeRuning) <= TIME_COUNT) {
          emailTimeRuning.value = emailTimeRuning.value - 1;
        } else {
          showEmailInterval.value = true;
          clearInterval(unref(emailTimer));
          emailTimer.value = null;
        }
      }, 1000);
    }
    
    try {
      const result = await getEmailCaptcha({ email: emailFormData.email, smsmode: SmsEnum.FORGET_PASSWORD }).catch((res) => {
        if(res.code === ExceptionEnum.EMAIL_SMS_FAIL_CODE){
          openCaptchaModal(true, {});
        } else if(res.code === 'EMAIL_NOT_FOUND') { // 假设后端返回此错误码
          createMessage.error('该邮箱未注册，请先注册账号');
          // 重置倒计时
          showEmailInterval.value = true;
          clearInterval(unref(emailTimer));
          emailTimer.value = null;
        }
        return false;
      });
      
      if (result) {
        // 显示发送成功提示
        createMessage.success('验证码已发送到您的邮箱，请查收');
        // 启用验证码输入框
        emailCodeDisabled.value = false;
      } else {
        // 显示发送失败提示
        createMessage.error('验证码发送失败，请重试');
        // 如果失败，重置倒计时
        showEmailInterval.value = true;
        clearInterval(unref(emailTimer));
        emailTimer.value = null;
      }
    } catch (error) {
      createMessage.error('验证码发送失败，请重试');
      console.error('Email captcha error:', error);
      // 如果出错，重置倒计时
      showEmailInterval.value = true;
      clearInterval(unref(emailTimer));
      emailTimer.value = null;
    }
  }

  /**
   * 获取手机验证码
   */
  async function getLoginCode() {
    if (!phoneFormData.mobile) {
      createMessage.warn(t('sys.login.mobilePlaceholder'));
      return;
    }
    //update-begin---author:wangshuai---date:2024-04-18---for:【QQYUN-9005】同一个IP，1分钟超过5次短信，则提示需要验证码---
    const result = await getCaptcha({ mobile: phoneFormData.mobile, smsmode: SmsEnum.FORGET_PASSWORD }).catch((res) =>{
      if(res.code === ExceptionEnum.PHONE_SMS_FAIL_CODE){
        openCaptchaModal(true, {});
      }
    });
    //update-end---author:wangshuai---date:2024-04-18---for:【QQYUN-9005】同一个IP，1分钟超过5次短信，则提示需要验证码---
    if (result) {
      // 显示发送成功提示
      createMessage.success(t('sys.login.smsSendSuccess'));
      
      const TIME_COUNT = 120; // 2分钟倒计时
      if (!unref(timer)) {
        timeRuning.value = TIME_COUNT;
        showInterval.value = false;
        timer.value = setInterval(() => {
          if (unref(timeRuning) > 0 && unref(timeRuning) <= TIME_COUNT) {
            timeRuning.value = timeRuning.value - 1;
          } else {
            showInterval.value = true;
            clearInterval(unref(timer));
            timer.value = null;
          }
        }, 1000);
      }
    }
  }

  /**
   * 第三方登录
   * @param type
   */
  function onThirdLogin(type) {
    thirdModalRef.value.onThirdLogin(type);
  }

  /**
   * 忘记密码
   */
  function forgetHandelClick() {
    type.value = 'forgot';
    setTimeout(() => {
      forgotRef.value.initForm();
    }, 300);
  }

  /**
   * 返回登录页面
   */
  function goBack() {
    activeIndex.value = 'accountLogin';
    type.value = 'login';
  }

  /**
   * 忘记密码/注册账号回调事件
   * @param value
   */
  function handleSuccess(value) {
    Object.assign(formData, value);
    Object.assign(phoneFormData, { mobile: "", smscode: "" });
    type.value = 'login';
    activeIndex.value = 'accountLogin';
    handleChangeCheckCode();
  }

  /**
   * 注册
   */
  function registerHandleClick() {
    type.value = 'register';
    setTimeout(() => {
      registerRef.value.initForm();
    }, 300);
  }

  /**
   * 注册
   */
  function codeHandleClick() {
    type.value = 'codeLogin';
    setTimeout(() => {
      codeRef.value.initFrom();
    }, 300);
  }

  function loadTurnstileMini() {
  nextTick(() => {
    const el = document.getElementById('turnstile-container-mini');
    if (el) el.innerHTML = '';
    
    if (!(window as any).turnstile) {
      const script = document.createElement('script');
      script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
      script.async = true;
      script.onload = () => {
        if ((window as any).turnstile && document.getElementById('turnstile-container-mini')) {
          (window as any).turnstile.render('#turnstile-container-mini', {
            sitekey: '0x4AAAAAABhBBIww9F9MG1Ov',
            callback: (token) => {
              formData.turnstileToken = token;
            },
            'expired-callback': () => {
              formData.turnstileToken = '';
            },
            'error-callback': () => {
              formData.turnstileToken = '';
            },
          });
        }
      };
      document.body.appendChild(script);
    } else if ((window as any).turnstile && document.getElementById('turnstile-container-mini')) {
      (window as any).turnstile.render('#turnstile-container-mini', {
        sitekey: '0x4AAAAAABhBBIww9F9MG1Ov',
        callback: (token) => {
          formData.turnstileToken = token;
        },
        'expired-callback': () => {
          formData.turnstileToken = '';
        },
        'error-callback': () => {
          formData.turnstileToken = '';
        },
      });
    }
  });
}

  // 删除clearTurnstileContainer函数，直接内联到各个调用处

  watch(() => formData.captchaType, (val) => {
    if (val === 'turnstile') {
      formData.inputCode = '';
      formData.turnstileToken = '';
      const el = document.getElementById('turnstile-container-mini');
      if (el) el.innerHTML = '';
      setTimeout(() => {
        if (!(window as any).turnstile) {
          const script = document.createElement('script');
          script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
          script.async = true;
          script.onload = loadTurnstileMini;
          document.body.appendChild(script);
        } else {
          loadTurnstileMini();
        }
      }, 0);
    } else {
      formData.turnstileToken = '';
      formData.inputCode = '';
      const el = document.getElementById('turnstile-container-mini');
      if (el) el.innerHTML = '';
      handleChangeCheckCode();
    }
  });

  watch(activeIndex, (val) => {
    if (val === 'accountLogin' && formData.captchaType === 'turnstile') {
      nextTick(() => {
        if (!(window as any).turnstile) {
          const script = document.createElement('script');
          script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
          script.async = true;
          script.onload = loadTurnstileMini;
          document.body.appendChild(script);
        } else {
          loadTurnstileMini();
        }
      });
    }
  });

  onMounted(() => {
    handleChangeCheckCode();
    nextTick(() => {
      if (activeIndex.value === 'accountLogin' && formData.captchaType === 'turnstile') {
        loadTurnstileMini();
      } else if (activeIndex.value === 'phoneLogin' && phoneFormData.captchaType === 'turnstile') {
        loadTurnstile('turnstile-container-phone', phoneFormData);
      } else if (activeIndex.value === 'emailLogin' && emailFormData.captchaType === 'turnstile') {
        loadTurnstile('turnstile-container-email', emailFormData);
      }
    });
  });

  // 添加loadTurnstile函数
  function loadTurnstile(containerId, formDataObj) {
    nextTick(() => {
      const el = document.getElementById(containerId);
      if (el) el.innerHTML = '';
      
      if (!(window as any).turnstile) {
        const script = document.createElement('script');
        script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
        script.async = true;
        script.onload = () => {
          if ((window as any).turnstile && document.getElementById(containerId)) {
            (window as any).turnstile.render(`#${containerId}`, {
              sitekey: '0x4AAAAAABhBBIww9F9MG1Ov',
              callback: (token) => {
                formDataObj.turnstileToken = token;
              },
              'expired-callback': () => {
                formDataObj.turnstileToken = '';
              },
              'error-callback': () => {
                formDataObj.turnstileToken = '';
              },
            });
          }
        };
        document.body.appendChild(script);
      } else if ((window as any).turnstile && document.getElementById(containerId)) {
        (window as any).turnstile.render(`#${containerId}`, {
          sitekey: '0x4AAAAAABhBBIww9F9MG1Ov',
          callback: (token) => {
            formDataObj.turnstileToken = token;
          },
          'expired-callback': () => {
            formDataObj.turnstileToken = '';
          },
          'error-callback': () => {
            formDataObj.turnstileToken = '';
          },
        });
      }
    });
  }

  // 修复手机登录验证码类型监听器
  watch(() => phoneFormData.captchaType, (val) => {
    if (val === 'turnstile') {
      phoneFormData.inputCode = '';
      phoneFormData.turnstileToken = '';
      const el = document.getElementById('turnstile-container-phone');
      if (el) el.innerHTML = '';
      setTimeout(() => {
        if (!(window as any).turnstile) {
          const script = document.createElement('script');
          script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
          script.async = true;
          script.onload = () => loadTurnstile('turnstile-container-phone', phoneFormData);
          document.body.appendChild(script);
        } else {
          loadTurnstile('turnstile-container-phone', phoneFormData);
        }
      }, 0);
    } else {
      phoneFormData.turnstileToken = '';
      phoneFormData.inputCode = '';
      const el = document.getElementById('turnstile-container-phone');
      if (el) el.innerHTML = '';
      handleChangeCheckCode();
    }
  });

  // 修复邮箱登录验证码类型监听器
  watch(() => emailFormData.captchaType, (val) => {
    if (val === 'turnstile') {
      emailFormData.inputCode = '';
      emailFormData.turnstileToken = '';
      const el = document.getElementById('turnstile-container-email');
      if (el) el.innerHTML = '';
      setTimeout(() => {
        if (!(window as any).turnstile) {
          const script = document.createElement('script');
          script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
          script.async = true;
          script.onload = () => loadTurnstile('turnstile-container-email', emailFormData);
          document.body.appendChild(script);
        } else {
          loadTurnstile('turnstile-container-email', emailFormData);
        }
      }, 0);
    } else {
      emailFormData.turnstileToken = '';
      emailFormData.inputCode = '';
      const el = document.getElementById('turnstile-container-email');
      if (el) el.innerHTML = '';
      handleChangeCheckCode();
    }
  });

  // 添加重新加载Turnstile验证码的方法
  function reloadTurnstileMini() {
    formData.turnstileToken = '';
    loadTurnstileMini();
    createMessage.success(t('sys.login.captchaReloaded'));
  }
  
  function reloadTurnstilePhone() {
    phoneFormData.turnstileToken = '';
    loadTurnstile('turnstile-container-phone', phoneFormData);
    createMessage.success(t('sys.login.captchaReloaded'));
  }
  
  function reloadTurnstileEmail() {
    emailFormData.turnstileToken = '';
    loadTurnstile('turnstile-container-email', emailFormData);
    createMessage.success(t('sys.login.captchaReloaded'));
  }
</script>

<style lang="less" scoped>
  @import '/@/assets/loginmini/style/home.less';
  @import '/@/assets/loginmini/style/base.less';

  :deep(.ant-input:focus) {
    box-shadow: none;
  }
  .aui-get-code {
    float: right;
    position: relative;
    z-index: 3;
    background: #ffffff;
    color: #1573e9;
    border-radius: 100px;
    padding: 5px 16px;
    margin: 7px;
    border: 1px solid #1573e9;
    top: 12px;
  }

  .aui-get-code:hover {
    color: #1573e9;
  }

  .code-shape {
    border-color: #dadada !important;
    color: #aaa !important;
  }

  :deep(.jeecg-dark-switch){
    position:absolute;
    margin-right: 10px;
  }
  .aui-link-login{
    height: 42px;
    padding: 10px 15px;
    font-size: 14px;
    border-radius: 8px;
    margin-top: 15px;
    margin-bottom: 8px;
    flex: 1;
    color: #fff;
  }
  .aui-phone-logo{
    position: absolute;
    margin-left: 10px;
    width: 60px;
    top:2px;
    z-index: 4;
  }
  .top-3{
    top: 0.45rem;
  }
  
  /* Email login styles */
  .aui-email-tip {
    font-size: 12px;
    color: #52c41a;
    margin-top: 4px;
    padding-left: 2px;
  }
  
  .aui-account.email .aui-inputClear {
    margin-bottom: 16px;
  }
  
  .aui-account.email .aui-code {
    cursor: pointer;
    color: #1890ff;
  }
  
  .aui-account.email .aui-code:hover {
    color: #40a9ff;
  }
  
  .email-code-button {
    border: 1px solid #1890ff;
    border-radius: 4px;
    padding: 4px 15px;
    background-color: #fff;
    text-align: center;
    font-size: 14px;
    color: #1890ff;
    position: absolute;
    right: 10px;
    top: 1%;
    width: auto;
    min-width: 120px;
    z-index: 2;
    cursor: pointer;
    outline: none;
    height: 32px;
    line-height: 1.5;
  }
  
  .email-code-button:hover {
    background-color: rgba(24, 144, 255, 0.1);
  }
  
  /* Disabled input styling */
  .aui-account.email .aui-inputClear .ant-input[disabled] {
    color: #********;
  }
  
  /* Email input styling */
  .email-input {
    border: none;
    border-bottom: 1px solid #d9d9d9;
    border-radius: 0;
    padding: 8px 0;
    font-size: 14px;
  }
  
  .email-input:focus, .email-input:hover {
    border-color: #1890ff;
    box-shadow: none;
  }
  
  /* Email code input styling */
  .email-code-input {
    border: none;
    border-bottom: 1px solid #d9d9d9;
    border-radius: 0;
    padding: 8px 0;
    font-size: 14px;
  }
  
  .email-code-input:focus, .email-code-input:hover {
    border-color: #1890ff;
    box-shadow: none;
  }
  
  /* Adjust input container spacing */
  .aui-account.email {
    padding: 0 10px;
  }
</style>

<style lang="less">
@prefix-cls: ~'@{namespace}-mini-login';
@dark-bg: #293146;

html[data-theme='dark'] {
  .@{prefix-cls} {
    background-color: @dark-bg !important;
    background-image: none;

    &::before {
      background-image: url(/@/assets/svg/login-bg-dark.svg);
    }
    .aui-inputClear{
      background-color: #232a3b !important;
    }
    .ant-input,
    .ant-input-password {
      background-color: #232a3b !important;
    }

    .ant-btn:not(.ant-btn-link):not(.ant-btn-primary) {
      border: 1px solid #4a5569 !important;
    }

    &-form {
      background: @dark-bg !important;
    }

    .app-iconify {
      color: #fff !important;
    }
    .aui-inputClear input,.aui-input-line input,.aui-choice{
      color: #c9d1d9 !important;
    }

    .aui-formBox{
      background-color: @dark-bg !important;
    }
    .aui-third-text span{
      background-color: @dark-bg !important;
    }
    .aui-form-nav .aui-flex-box{
      color: #c9d1d9 !important;
    }

    .aui-formButton .aui-linek-code{
      background:  @dark-bg !important;
      color: white !important;
    }
    .aui-code-line{
      border-left: none !important;
    }
    .ant-checkbox-inner,.aui-success h3{
      border-color: #c9d1d9;
    }
    //update-begin---author:wangshuai ---date:20230828  for：【QQYUN-6363】这个样式代码有问题，不在里面，导致表达式有问题------------
    &-sign-in-way {
      .anticon {
        font-size: 22px !important;
        color: #888 !important;
        cursor: pointer !important;

        &:hover {
          color: @primary-color !important;
        }
      }
    }
    //update-end---author:wangshuai ---date:20230828  for：【QQYUN-6363】这个样式代码有问题，不在里面，导致表达式有问题------------
  }

  input.fix-auto-fill,
  .fix-auto-fill input {
    -webkit-text-fill-color: #c9d1d9 !important;
    box-shadow: inherit !important;
  }
  
  .ant-divider-inner-text {
    font-size: 12px !important;
    color: @text-color-secondary !important;
  }
  .aui-third-login a{
    background: transparent;
  }
}
</style>
