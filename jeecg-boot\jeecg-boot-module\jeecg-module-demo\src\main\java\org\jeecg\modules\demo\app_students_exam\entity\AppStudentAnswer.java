package org.jeecg.modules.demo.app_students_exam.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Entity for app_student_answer table.
 */
@Data
@TableName("app_student_answer")
public class AppStudentAnswer implements Serializable {
    @TableId
    private String id;

    private String paperId;
    private String questionId;
    private String studentId;
    private String answerContent;
    private Integer isCorrect;
    private BigDecimal score;
    private BigDecimal maxScore;
    private Integer markingType;
    private String markingComment;
    private Date markingTime;
    private String markingUser;
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
}