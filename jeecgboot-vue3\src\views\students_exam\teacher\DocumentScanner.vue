<template>
  <div class="document-scanner">
    <a-card title="📄 智能文档处理" class="scanner-card">
      <template #extra>
        <a-space>
          <a-button @click="resetScanner" size="small">重置</a-button>
          <a-button type="link" @click="openOnlineScanner" size="small">在线工具</a-button>
        </a-space>
      </template>

      <!-- 上传区域 -->
      <div class="upload-section">
        <a-upload-dragger
          v-model:file-list="fileList"
          :before-upload="beforeUpload"
          :multiple="false"
          accept="image/*"
          @change="handleUpload"
          class="upload-dragger"
        >
          <p class="ant-upload-drag-icon">
            <InboxOutlined style="font-size: 48px; color: #1890ff" />
          </p>
          <p class="ant-upload-text">点击或拖拽图片到此区域上传</p>
          <p class="ant-upload-hint">支持 JPG、PNG、GIF 格式的图片文件</p>
        </a-upload-dragger>
      </div>

      <!-- 处理选项 -->
      <div v-if="currentImage" class="process-section">
        <a-divider>快速处理</a-divider>
        
        <a-row :gutter="16">
          <a-col :span="8">
            <a-card size="small" hoverable @click="quickEnhance" :loading="processing">
              <div class="quick-action">
                <h4>📝 文档优化</h4>
                <p>自动调整亮度对比度，适合文字扫描</p>
              </div>
            </a-card>
          </a-col>
          
          <a-col :span="8">
            <a-card size="small" hoverable @click="convertToGrayscale" :loading="processing">
              <div class="quick-action">
                <h4>⚫ 黑白转换</h4>
                <p>转换为黑白图片，减少文件大小</p>
              </div>
            </a-card>
          </a-col>
          
          <a-col :span="8">
            <a-card size="small" hoverable @click="cropToA4" :loading="processing">
              <div class="quick-action">
                <h4>📄 智能裁剪</h4>
                <p>按A4比例裁剪，去除多余边框</p>
              </div>
            </a-card>
          </a-col>
        </a-row>
        
        <a-divider>高级处理</a-divider>
        <a-space wrap>
          <a-button @click="adjustBrightness" :loading="processing">调整亮度</a-button>
          <a-button @click="adjustContrast" :loading="processing">增强对比度</a-button>
          <a-button @click="sharpenImage" :loading="processing">锐化图像</a-button>
          <a-button @click="removeBackground" :loading="processing">背景优化</a-button>
        </a-space>
      </div>

      <!-- 图片预览 -->
      <div v-if="currentImage" class="preview-section">
        <a-divider>预览对比</a-divider>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card size="small" title="原图">
              <img :src="originalImage" alt="原图" class="preview-image" />
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card size="small" title="处理后">
              <canvas 
                ref="processedCanvas" 
                class="preview-canvas"
                v-show="processedImage"
              ></canvas>
              <div v-if="!processedImage" class="no-processed">
                选择上方的处理选项开始
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 下载区域 -->
      <div v-if="processedImage" class="download-section">
        <a-divider>保存结果</a-divider>
        <a-space size="large">
          <a-button @click="downloadImage" type="primary" size="large">
            💾 下载图片
          </a-button>
          <a-button @click="copyToClipboard" size="large">
            📋 复制到剪贴板
          </a-button>
        </a-space>
      </div>

      <!-- 推荐工具 -->
      <div class="tools-section">
        <a-divider>专业工具推荐</a-divider>
        <a-alert
          message="提示"
          description="如需更专业的文档扫描功能，推荐使用以下在线工具"
          type="info"
          show-icon
          style="margin-bottom: 16px"
        />
        
        <a-row :gutter="16">
          <a-col :span="6">
            <a-button block @click="openTool('adobe')" size="large">
              🔴 Adobe Scan
            </a-button>
          </a-col>
          <a-col :span="6">
            <a-button block @click="openTool('camscanner')" size="large">
              📱 CamScanner
            </a-button>
          </a-col>
          <a-col :span="6">
            <a-button block @click="openTool('officelens')" size="large">
              📄 Office Lens
            </a-button>
          </a-col>
          <a-col :span="6">
            <a-button block @click="openTool('smartscan')" size="large">
              🤖 智能扫描
            </a-button>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { InboxOutlined } from '@ant-design/icons-vue'

// 响应式数据
const fileList = ref([])
const currentImage = ref(null)
const originalImage = ref('')
const processedImage = ref(null)
const processing = ref(false)
const processedCanvas = ref(null)

// 文件上传前处理
const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    message.error('只能上传图片文件!')
    return false
  }
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('图片大小不能超过 10MB!')
    return false
  }
  return false // 阻止自动上传
}

// 处理文件上传
const handleUpload = (info) => {
  if (info.file && info.file.originFileObj) {
    const file = info.file.originFileObj
    const reader = new FileReader()
    
    reader.onload = (e) => {
      originalImage.value = e.target.result
      
      const img = new Image()
      img.onload = () => {
        currentImage.value = img
        message.success('图片上传成功!')
      }
      img.src = e.target.result
    }
    
    reader.readAsDataURL(file)
  }
}

// 创建Canvas的通用方法
const createCanvas = (img, processor) => {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  
  canvas.width = img.width
  canvas.height = img.height
  
  ctx.drawImage(img, 0, 0)
  
  if (processor) {
    processor(ctx, canvas)
  }
  
  return canvas
}

// 快速文档优化
const quickEnhance = async () => {
  if (!currentImage.value) return
  
  processing.value = true
  try {
    await nextTick()
    
    const canvas = createCanvas(currentImage.value, (ctx, canvas) => {
      // 综合处理：亮度+对比度+锐化
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
      const data = imageData.data
      
      for (let i = 0; i < data.length; i += 4) {
        // 增加亮度
        let r = data[i] + 20
        let g = data[i + 1] + 20
        let b = data[i + 2] + 20
        
        // 增强对比度
        const contrast = 1.3
        const factor = (259 * (contrast * 255 + 255)) / (255 * (259 - contrast * 255))
        r = factor * (r - 128) + 128
        g = factor * (g - 128) + 128
        b = factor * (b - 128) + 128
        
        data[i] = Math.max(0, Math.min(255, r))
        data[i + 1] = Math.max(0, Math.min(255, g))
        data[i + 2] = Math.max(0, Math.min(255, b))
      }
      
      ctx.putImageData(imageData, 0, 0)
    })
    
    displayProcessedImage(canvas)
    message.success('文档优化完成！')
  } catch (error) {
    message.error('处理失败: ' + error.message)
  } finally {
    processing.value = false
  }
}

// 调整亮度
const adjustBrightness = async () => {
  if (!currentImage.value) return
  
  processing.value = true
  try {
    await nextTick()
    
    const canvas = createCanvas(currentImage.value, (ctx, canvas) => {
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
      const data = imageData.data
      
      for (let i = 0; i < data.length; i += 4) {
        data[i] = Math.min(255, data[i] + 30)
        data[i + 1] = Math.min(255, data[i + 1] + 30)
        data[i + 2] = Math.min(255, data[i + 2] + 30)
      }
      
      ctx.putImageData(imageData, 0, 0)
    })
    
    displayProcessedImage(canvas)
    message.success('亮度调整完成!')
  } catch (error) {
    message.error('处理失败: ' + error.message)
  } finally {
    processing.value = false
  }
}

// 调整对比度
const adjustContrast = async () => {
  if (!currentImage.value) return
  
  processing.value = true
  try {
    await nextTick()
    
    const canvas = createCanvas(currentImage.value, (ctx, canvas) => {
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
      const data = imageData.data
      
      const contrast = 1.2
      const factor = (259 * (contrast * 255 + 255)) / (255 * (259 - contrast * 255))
      
      for (let i = 0; i < data.length; i += 4) {
        data[i] = Math.max(0, Math.min(255, factor * (data[i] - 128) + 128))
        data[i + 1] = Math.max(0, Math.min(255, factor * (data[i + 1] - 128) + 128))
        data[i + 2] = Math.max(0, Math.min(255, factor * (data[i + 2] - 128) + 128))
      }
      
      ctx.putImageData(imageData, 0, 0)
    })
    
    displayProcessedImage(canvas)
    message.success('对比度调整完成!')
  } catch (error) {
    message.error('处理失败: ' + error.message)
  } finally {
    processing.value = false
  }
}

// 转换为灰度
const convertToGrayscale = async () => {
  if (!currentImage.value) return
  
  processing.value = true
  try {
    await nextTick()
    
    const canvas = createCanvas(currentImage.value, (ctx, canvas) => {
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
      const data = imageData.data
      
      for (let i = 0; i < data.length; i += 4) {
        const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114
        data[i] = gray
        data[i + 1] = gray
        data[i + 2] = gray
      }
      
      ctx.putImageData(imageData, 0, 0)
    })
    
    displayProcessedImage(canvas)
    message.success('黑白转换完成!')
  } catch (error) {
    message.error('处理失败: ' + error.message)
  } finally {
    processing.value = false
  }
}

// A4裁剪
const cropToA4 = async () => {
  if (!currentImage.value) return
  
  processing.value = true
  try {
    await nextTick()
    
    const img = currentImage.value
    const a4Ratio = 210 / 297
    
    let newWidth, newHeight, x, y
    
    if (img.width / img.height > a4Ratio) {
      newHeight = img.height
      newWidth = newHeight * a4Ratio
      x = (img.width - newWidth) / 2
      y = 0
    } else {
      newWidth = img.width
      newHeight = newWidth / a4Ratio
      x = 0
      y = (img.height - newHeight) / 2
    }
    
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    canvas.width = newWidth
    canvas.height = newHeight
    
    ctx.drawImage(img, x, y, newWidth, newHeight, 0, 0, newWidth, newHeight)
    
    displayProcessedImage(canvas)
    message.success('智能裁剪完成!')
  } catch (error) {
    message.error('处理失败: ' + error.message)
  } finally {
    processing.value = false
  }
}

// 锐化图像
const sharpenImage = async () => {
  if (!currentImage.value) return
  
  processing.value = true
  try {
    await nextTick()
    
    const canvas = createCanvas(currentImage.value, (ctx) => {
      ctx.filter = 'contrast(120%) brightness(110%)'
      ctx.drawImage(currentImage.value, 0, 0)
    })
    
    displayProcessedImage(canvas)
    message.success('图像锐化完成!')
  } catch (error) {
    message.error('处理失败: ' + error.message)
  } finally {
    processing.value = false
  }
}

// 背景优化
const removeBackground = async () => {
  if (!currentImage.value) return
  
  processing.value = true
  try {
    await nextTick()
    
    const canvas = createCanvas(currentImage.value, (ctx, canvas) => {
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
      const data = imageData.data
      
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i]
        const g = data[i + 1]
        const b = data[i + 2]
        
        const brightness = (r + g + b) / 3
        if (brightness > 200) {
          data[i] = 255
          data[i + 1] = 255
          data[i + 2] = 255
        }
      }
      
      ctx.putImageData(imageData, 0, 0)
    })
    
    displayProcessedImage(canvas)
    message.success('背景优化完成!')
  } catch (error) {
    message.error('处理失败: ' + error.message)
  } finally {
    processing.value = false
  }
}

// 显示处理后的图片
const displayProcessedImage = async (canvas) => {
  await nextTick()
  if (processedCanvas.value) {
    const ctx = processedCanvas.value.getContext('2d')
    processedCanvas.value.width = canvas.width
    processedCanvas.value.height = canvas.height
    ctx.drawImage(canvas, 0, 0)
    processedImage.value = canvas
  }
}

// 下载图片
const downloadImage = () => {
  if (!processedImage.value) return
  
  const link = document.createElement('a')
  link.download = 'processed-document.png'
  link.href = processedImage.value.toDataURL()
  link.click()
  message.success('图片下载成功!')
}

// 复制到剪贴板
const copyToClipboard = async () => {
  if (!processedImage.value) return
  
  try {
    const canvas = processedImage.value
    canvas.toBlob(async (blob) => {
      if (blob) {
        await navigator.clipboard.write([
          new ClipboardItem({ 'image/png': blob })
        ])
        message.success('图片已复制到剪贴板!')
      }
    })
  } catch (error) {
    message.warning('复制失败，请使用下载功能')
  }
}

// 重置扫描器
const resetScanner = () => {
  fileList.value = []
  currentImage.value = null
  originalImage.value = ''
  processedImage.value = null
  message.info('已重置')
}

// 打开在线扫描器
const openOnlineScanner = () => {
  window.open('https://www.adobe.com/acrobat/online/pdf-scanner.html', '_blank')
}

// 打开推荐工具
const openTool = (tool) => {
  const urls = {
    adobe: 'https://www.adobe.com/acrobat/online/pdf-scanner.html',
    camscanner: 'https://www.camscanner.com/',
    officelens: 'https://www.microsoft.com/en-us/microsoft-365/microsoft-lens',
    smartscan: 'https://online.smartscan.io/'
  }
  
  if (urls[tool]) {
    window.open(urls[tool], '_blank')
  }
}
</script>

<style scoped>
.document-scanner {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.scanner-card {
  max-width: 1200px;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.upload-section {
  margin-bottom: 20px;
}

.upload-dragger {
  background: #fafafa;
}

.process-section {
  margin: 20px 0;
}

.quick-action {
  text-align: center;
  padding: 20px 10px;
  cursor: pointer;
}

.quick-action h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
}

.quick-action p {
  margin: 0;
  color: #666;
  font-size: 12px;
  line-height: 1.4;
}

.preview-section {
  margin: 20px 0;
}

.preview-image {
  width: 100%;
  max-height: 300px;
  object-fit: contain;
  border-radius: 6px;
}

.preview-canvas {
  width: 100%;
  max-height: 300px;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.no-processed {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  background: #fafafa;
  border-radius: 6px;
}

.download-section {
  margin: 20px 0;
  text-align: center;
}

.tools-section {
  margin-top: 30px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .document-scanner {
    padding: 10px;
  }
  
  .preview-image,
  .preview-canvas {
    max-height: 200px;
  }
  
  .quick-action {
    padding: 15px 8px;
  }
  
  .quick-action h4 {
    font-size: 14px;
  }
  
  .quick-action p {
    font-size: 11px;
  }
}
</style>