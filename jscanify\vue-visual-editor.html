<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue 3 Visual Text Editor</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
        }

        .editor-container {
            display: flex;
            height: 100vh;
        }

        .toolbar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 20px;
            overflow-y: auto;
        }

        .toolbar h3 {
            margin-bottom: 15px;
            color: #ecf0f1;
        }

        .tool-group {
            margin-bottom: 20px;
        }

        .tool-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            text-transform: uppercase;
            color: #bdc3c7;
        }

        .btn {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn.danger {
            background: #e74c3c;
        }

        .btn.danger:hover {
            background: #c0392b;
        }

        .btn-icon {
            font-size: 14px;
        }

        .tooltip {
            position: relative;
        }

        .tooltip:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            white-space: nowrap;
            z-index: 1000;
            margin-bottom: 5px;
        }

        .tooltip:hover::before {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 4px solid transparent;
            border-top-color: rgba(0,0,0,0.8);
            margin-bottom: 1px;
        }

        .canvas {
            flex: 1;
            background: white;
            position: relative;
            overflow: hidden;
            margin: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        .text-element {
            position: absolute;
            cursor: move;
            padding: 8px;
            border: 2px solid transparent;
            border-radius: 4px;
            transition: border-color 0.2s;
            min-width: 50px;
            min-height: 30px;
            user-select: none;
        }

        .shape-element {
            position: absolute;
            cursor: move;
            border: 2px solid transparent;
            transition: border-color 0.2s;
            user-select: none;
        }

        .shape-element.rectangle {
            background: white;
            border: 2px solid black;
            border-radius: 4px;
        }

        .shape-element.circle {
            background: white;
            border: 2px solid black;
            border-radius: 50%;
        }

        .shape-element.oval {
            background: white;
            border: 2px solid black;
            border-radius: 50%;
        }

        .shape-element.triangle {
            background: transparent;
            border: none;
            position: relative;
        }

        .shape-element.triangle::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: calc(var(--width) / 2) solid transparent;
            border-right: calc(var(--width) / 2) solid transparent;
            border-bottom: var(--height) solid var(--border-color);
        }

        .shape-element.triangle::after {
            content: '';
            position: absolute;
            top: 3px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: calc(var(--width) / 2 - 3px) solid transparent;
            border-right: calc(var(--width) / 2 - 3px) solid transparent;
            border-bottom: calc(var(--height) - 3px) solid var(--bg-color);
        }

        .shape-element.diamond {
            background: white;
            border: 2px solid black;
            transform: rotate(45deg);
        }

        .shape-element.line {
            background: black;
            border: none;
            height: 2px !important;
        }

        .shape-element.dotted-line {
            background: transparent;
            border: none;
            border-top: 2px dotted black;
            height: 2px !important;
        }

        .shape-element.curve {
            background: transparent;
            border: 2px solid black;
            border-radius: 50px 50px 0 0;
            border-bottom: none;
        }

        .shape-element.arrow-right {
            background: var(--bg-color);
            border: 2px solid var(--border-color);
            position: relative;
        }

        .shape-element.arrow-right::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -8px;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-top: calc(var(--height) / 3) solid transparent;
            border-bottom: calc(var(--height) / 3) solid transparent;
            border-left: 8px solid var(--border-color);
        }

        .shape-element.arrow-left {
            background: var(--bg-color);
            border: 2px solid var(--border-color);
            position: relative;
        }

        .shape-element.arrow-left::after {
            content: '';
            position: absolute;
            top: 50%;
            left: -8px;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-top: calc(var(--height) / 3) solid transparent;
            border-bottom: calc(var(--height) / 3) solid transparent;
            border-right: 8px solid var(--border-color);
        }

        .shape-element.arrow-up {
            background: var(--bg-color);
            border: 2px solid var(--border-color);
            position: relative;
        }

        .shape-element.arrow-up::after {
            content: '';
            position: absolute;
            top: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: calc(var(--width) / 3) solid transparent;
            border-right: calc(var(--width) / 3) solid transparent;
            border-bottom: 8px solid var(--border-color);
        }

        .shape-element.arrow-down {
            background: var(--bg-color);
            border: 2px solid var(--border-color);
            position: relative;
        }

        .shape-element.arrow-down::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: calc(var(--width) / 3) solid transparent;
            border-right: calc(var(--width) / 3) solid transparent;
            border-top: 8px solid var(--border-color);
        }

        .media-element {
            position: absolute;
            cursor: move;
            border: 2px solid transparent;
            border-radius: 4px;
            transition: border-color 0.2s;
            user-select: none;
        }

        .media-element img,
        .media-element video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
        }

        .media-element audio {
            width: 100%;
        }

        .icon-element {
            position: absolute;
            cursor: move;
            padding: 4px;
            border: 2px solid transparent;
            border-radius: 4px;
            transition: border-color 0.2s;
            user-select: none;
            font-size: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .text-element:hover,
        .shape-element:hover,
        .icon-element:hover,
        .media-element:hover {
            border-color: #3498db !important;
        }

        .text-element.selected,
        .shape-element.selected,
        .icon-element.selected,
        .media-element.selected {
            border-color: #e74c3c !important;
            border-style: solid !important;
        }

        .text-element.editing {
            border-color: #27ae60 !important;
            background: rgba(39, 174, 96, 0.1);
            border-style: solid !important;
        }

        .multi-selected {
            border-color: #9b59b6 !important;
            border-style: solid !important;
        }

        .background-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 3000;
        }

        .background-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            min-width: 300px;
        }

        .url-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }

        .color-picker-popup {
            position: fixed;
            z-index: 3000;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            padding: 15px;
            min-width: 250px;
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 5px;
            margin-bottom: 15px;
        }

        .color-swatch {
            width: 25px;
            height: 25px;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.2s;
        }

        .color-swatch:hover {
            border-color: #3498db;
            transform: scale(1.1);
        }

        .color-swatch.selected {
            border-color: #e74c3c;
            transform: scale(1.1);
        }

        .custom-color-section {
            border-top: 1px solid #eee;
            padding-top: 15px;
        }

        .custom-color-input {
            width: 100%;
            height: 40px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 10px;
        }

        .hex-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            text-transform: uppercase;
        }

        .color-picker-row {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .color-preview {
            width: 30px;
            height: 30px;
            border: 2px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: transform 0.2s;
        }

        .color-preview:hover {
            transform: scale(1.1);
            border-color: #3498db;
        }

        .color-value {
            font-family: monospace;
            font-size: 12px;
            color: #666;
        }

        .icon-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 3000;
        }

        .icon-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            min-width: 400px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .icon-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }

        .icon-tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .icon-tab.active {
            border-bottom-color: #3498db;
            color: #3498db;
        }

        .icon-search {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
            gap: 10px;
            max-height: 300px;
            overflow-y: auto;
        }

        .icon-item {
            width: 60px;
            height: 60px;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 24px;
            transition: all 0.2s;
        }

        .icon-item:hover {
            border-color: #3498db;
            background: #f8f9fa;
        }

        .markdown-input {
            width: 100%;
            height: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            resize: vertical;
            font-size: 12px;
            line-height: 1.4;
        }

        .quick-examples {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .example-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
        }

        .example-icon {
            width: 40px;
            height: 40px;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.2s;
            background: white;
        }

        .example-icon:hover {
            border-color: #3498db;
            background: #e3f2fd;
            transform: scale(1.05);
        }

        .editable-text {
            outline: none;
            background: transparent;
            border: none;
            width: 100%;
            resize: none;
            font-family: inherit;
            font-size: inherit;
            color: inherit;
            font-weight: inherit;
        }

        .properties-panel {
            position: absolute;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            min-width: 200px;
            z-index: 1000;
            border: 1px solid #ddd;
        }

        .property-group {
            margin-bottom: 15px;
        }

        .property-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
        }

        .property-input {
            width: 100%;
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
        }

        .color-input {
            width: 100%;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .coordinates {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-family: monospace;
        }

        .context-menu {
            position: absolute;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 2000;
            min-width: 220px;
            max-width: 300px;
        }

        .context-menu-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            font-size: 12px;
        }

        .context-menu-item:hover {
            background: #f5f5f5;
        }

        .context-actions {
            border-bottom: 2px solid #ddd;
            padding-bottom: 5px;
        }

        .context-properties {
            padding: 10px;
            max-height: 400px;
            overflow-y: auto;
        }

        .context-properties h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 14px;
        }

        .selection-box {
            position: absolute;
            border: 2px solid #3498db;
            background: rgba(52, 152, 219, 0.2);
            pointer-events: none;
            z-index: 100;
            box-shadow: 0 0 0 1px rgba(52, 152, 219, 0.3);
        }

        .multi-selected {
            border-color: #9b59b6 !important;
            background: rgba(155, 89, 182, 0.1) !important;
        }

        .library-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 5px;
            margin-bottom: 10px;
        }

        .lib-btn {
            width: 40px;
            height: 40px;
            background: #34495e;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s;
        }

        .lib-btn:hover {
            background: #2c3e50;
        }

        .resize-handle {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #e74c3c;
            border: 1px solid white;
            border-radius: 2px;
            cursor: nw-resize;
            z-index: 10;
        }

        .resize-handle.top-left {
            top: -4px;
            left: -4px;
            cursor: nw-resize;
        }

        .resize-handle.top-right {
            top: -4px;
            right: -4px;
            cursor: ne-resize;
        }

        .resize-handle.bottom-left {
            bottom: -4px;
            left: -4px;
            cursor: sw-resize;
        }

        .resize-handle.bottom-right {
            bottom: -4px;
            right: -4px;
            cursor: se-resize;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="editor-container">
            <!-- Toolbar -->
            <div class="toolbar">
                <h3>Visual Editor</h3>
                
                <div class="tool-group">
                    <label>Add Elements</label>
                    <button class="btn tooltip" @click="addTextElement('heading')" data-tooltip="Add Heading">
                        <span class="btn-icon">📝</span> H1
                    </button>
                    <button class="btn tooltip" @click="addTextElement('paragraph')" data-tooltip="Add Paragraph">
                        <span class="btn-icon">📄</span> P
                    </button>
                    <button class="btn tooltip" @click="addTextElement('label')" data-tooltip="Add Label">
                        <span class="btn-icon">🏷️</span> Label
                    </button>
                </div>

                <div class="tool-group">
                    <label>Actions</label>
                    <button class="btn tooltip" @click="duplicateSelected" :disabled="selectedElements.length === 0" data-tooltip="Duplicate Selected">
                        <span class="btn-icon">📋</span> Duplicate
                    </button>
                    <button class="btn danger tooltip" @click="deleteSelected" :disabled="selectedElements.length === 0" data-tooltip="Delete Selected">
                        <span class="btn-icon">🗑️</span> Delete
                    </button>
                    <button class="btn tooltip" @click="clearAll" data-tooltip="Clear All Elements">
                        <span class="btn-icon">🧹</span> Clear
                    </button>
                </div>

                <div class="tool-group">
                    <label>History</label>
                    <button class="btn tooltip" @click="undo" :disabled="!canUndo" data-tooltip="Undo Last Action">
                        <span class="btn-icon">↶</span> Undo
                    </button>
                    <button class="btn tooltip" @click="redo" :disabled="!canRedo" data-tooltip="Redo Last Action">
                        <span class="btn-icon">↷</span> Redo
                    </button>
                </div>

                <div class="tool-group">
                    <label>Shapes</label>
                    <div class="library-grid">
                        <button class="lib-btn" @click="addShape('rectangle')" title="Rectangle">▭</button>
                        <button class="lib-btn" @click="addShape('circle')" title="Circle">○</button>
                        <button class="lib-btn" @click="addShape('oval')" title="Oval">⬭</button>
                        <button class="lib-btn" @click="addShape('line')" title="Line">─</button>
                        <button class="lib-btn" @click="addShape('dotted-line')" title="Dotted Line">┄</button>
                        <button class="lib-btn" @click="addShape('curve')" title="Curve">⌒</button>
                        <button class="lib-btn" @click="addShape('arrow-right')" title="Arrow Right">→</button>
                        <button class="lib-btn" @click="addShape('arrow-left')" title="Arrow Left">←</button>
                        <button class="lib-btn" @click="addShape('arrow-up')" title="Arrow Up">↑</button>
                        <button class="lib-btn" @click="addShape('arrow-down')" title="Arrow Down">↓</button>
                        <button class="lib-btn" @click="addShape('triangle')" title="Triangle">△</button>
                        <button class="lib-btn" @click="addShape('diamond')" title="Diamond">◇</button>
                    </div>
                </div>

                <div class="tool-group">
                    <label>Icons</label>
                    <div class="library-grid">
                        <button class="lib-btn" @click="addIcon('star')" title="Star">⭐</button>
                        <button class="lib-btn" @click="addIcon('heart')" title="Heart">❤️</button>
                        <button class="lib-btn" @click="addIcon('check')" title="Check">✅</button>
                        <button class="lib-btn" @click="addIcon('cross')" title="Cross">❌</button>
                        <button class="lib-btn" @click="addIcon('warning')" title="Warning">⚠️</button>
                        <button class="lib-btn" @click="addIcon('info')" title="Info">ℹ️</button>
                        <button class="lib-btn" @click="addIcon('home')" title="Home">🏠</button>
                        <button class="lib-btn" @click="addIcon('phone')" title="Phone">📞</button>
                        <button class="lib-btn" @click="addIcon('email')" title="Email">📧</button>
                        <button class="lib-btn" @click="addIcon('user')" title="User">👤</button>
                        <button class="lib-btn" @click="addIcon('settings')" title="Settings">⚙️</button>
                        <button class="lib-btn" @click="showIconCustomizer" title="Add Custom Icon">➕</button>
                    </div>
                </div>

                <div class="tool-group">
                    <label>Media</label>
                    <button class="btn tooltip" @click="addMediaElement('image')" data-tooltip="Add Image from URL">
                        <span class="btn-icon">🖼️</span> Image URL
                    </button>
                    <button class="btn tooltip" @click="triggerFileUpload('image')" data-tooltip="Upload Image File">
                        <span class="btn-icon">📁</span> Upload Image
                    </button>
                    <button class="btn tooltip" @click="addMediaElement('audio')" data-tooltip="Add Audio from URL">
                        <span class="btn-icon">🎵</span> Audio URL
                    </button>
                    <button class="btn tooltip" @click="triggerFileUpload('audio')" data-tooltip="Upload Audio File">
                        <span class="btn-icon">📁</span> Upload Audio
                    </button>
                    <button class="btn tooltip" @click="addMediaElement('video')" data-tooltip="Add Video from URL">
                        <span class="btn-icon">🎬</span> Video URL
                    </button>
                    <button class="btn tooltip" @click="triggerFileUpload('video')" data-tooltip="Upload Video File">
                        <span class="btn-icon">📁</span> Upload Video
                    </button>
                    <input type="file" ref="mediaFileInput" @change="handleFileUpload" accept="image/*,audio/*,video/*" style="display: none;">
                </div>

                <div class="tool-group">
                    <label>Background</label>
                    <button class="btn tooltip" @click="showBackgroundOptions" data-tooltip="Customize Canvas Background">
                        <span class="btn-icon">🎨</span> Background
                    </button>
                </div>

                <div class="tool-group">
                    <label>File</label>
                    <button class="btn tooltip" @click="saveProject" data-tooltip="Save Project as JSON">
                        <span class="btn-icon">💾</span> Save
                    </button>
                    <button class="btn tooltip" @click="triggerLoadProject" data-tooltip="Load Project from JSON">
                        <span class="btn-icon">📁</span> Load
                    </button>
                    <input type="file" ref="fileInput" @change="loadProject" accept=".json" style="display: none;">
                </div>
            </div>

            <!-- Canvas -->
            <div class="canvas"
                 @click="handleCanvasClick"
                 @mousemove="updateMousePosition"
                 @mousedown="startSelection"
                 @keydown="handleKeyDown"
                 tabindex="0"
                 :style="canvasStyle"
                 ref="canvas">
                
                <!-- Selection Box -->
                <div v-if="selectionBox.visible"
                     class="selection-box"
                     :style="getSelectionBoxStyle()">
                </div>

                <!-- Text Elements -->
                <div v-for="element in elements.filter(el => el.type === 'text' || el.type === 'heading' || el.type === 'paragraph' || el.type === 'label')"
                     :key="element.id"
                     class="text-element"
                     :class="{
                         selected: selectedElements.includes(element.id),
                         'multi-selected': selectedElements.length > 1 && selectedElements.includes(element.id),
                         editing: editingElement?.id === element.id
                     }"
                     :style="getElementStyle(element)"
                     @click.stop="selectElement(element, $event)"
                     @dblclick="startEditing(element)"
                     @mousedown="startDragging(element, $event)"
                     @contextmenu.prevent="showElementContextMenu(element, $event)">

                    <textarea v-if="editingElement?.id === element.id"
                             class="editable-text"
                             v-model="element.text"
                             @blur="stopEditing"
                             @keydown.enter.exact="stopEditing"
                             @keydown.escape="stopEditing"
                             ref="editInput"
                             :style="{ fontSize: element.fontSize + 'px' }">
                    </textarea>

                    <span v-else>{{ element.text }}</span>

                    <!-- Resize handles for text elements -->
                    <template v-if="selectedElements.includes(element.id) && selectedElements.length === 1 && !editingElement">
                        <div class="resize-handle top-left" @mousedown.stop="startResizing(element, 'top-left', $event)"></div>
                        <div class="resize-handle top-right" @mousedown.stop="startResizing(element, 'top-right', $event)"></div>
                        <div class="resize-handle bottom-left" @mousedown.stop="startResizing(element, 'bottom-left', $event)"></div>
                        <div class="resize-handle bottom-right" @mousedown.stop="startResizing(element, 'bottom-right', $event)"></div>
                    </template>
                </div>

                <!-- Shape Elements -->
                <div v-for="element in elements.filter(el => el.type === 'shape')"
                     :key="element.id"
                     class="shape-element"
                     :class="[element.shape, {
                         selected: selectedElements.includes(element.id),
                         'multi-selected': selectedElements.length > 1 && selectedElements.includes(element.id)
                     }]"
                     :style="getShapeStyle(element)"
                     @click.stop="selectElement(element, $event)"
                     @dblclick="showElementContextMenu(element, $event)"
                     @mousedown="startDragging(element, $event)"
                     @contextmenu.prevent="showElementContextMenu(element, $event)">

                    <!-- Resize handles for shapes -->
                    <template v-if="selectedElements.includes(element.id) && selectedElements.length === 1">
                        <div class="resize-handle top-left" @mousedown.stop="startResizing(element, 'top-left', $event)"></div>
                        <div class="resize-handle top-right" @mousedown.stop="startResizing(element, 'top-right', $event)"></div>
                        <div class="resize-handle bottom-left" @mousedown.stop="startResizing(element, 'bottom-left', $event)"></div>
                        <div class="resize-handle bottom-right" @mousedown.stop="startResizing(element, 'bottom-right', $event)"></div>
                    </template>
                </div>

                <!-- Media Elements -->
                <div v-for="element in elements.filter(el => el.type === 'media')"
                     :key="element.id"
                     class="media-element"
                     :class="{
                         selected: selectedElements.includes(element.id),
                         'multi-selected': selectedElements.length > 1 && selectedElements.includes(element.id)
                     }"
                     :style="getMediaStyle(element)"
                     @click.stop="selectElement(element, $event)"
                     @dblclick="showElementContextMenu(element, $event)"
                     @mousedown="startDragging(element, $event)"
                     @contextmenu.prevent="showElementContextMenu(element, $event)">

                    <img v-if="element.mediaType === 'image'" :src="element.url" :alt="element.alt || 'Image'">
                    <video v-else-if="element.mediaType === 'video'" :src="element.url" controls></video>
                    <audio v-else-if="element.mediaType === 'audio'" :src="element.url" controls></audio>

                    <!-- Resize handles for media -->
                    <template v-if="selectedElements.includes(element.id) && selectedElements.length === 1">
                        <div class="resize-handle top-left" @mousedown.stop="startResizing(element, 'top-left', $event)"></div>
                        <div class="resize-handle top-right" @mousedown.stop="startResizing(element, 'top-right', $event)"></div>
                        <div class="resize-handle bottom-left" @mousedown.stop="startResizing(element, 'bottom-left', $event)"></div>
                        <div class="resize-handle bottom-right" @mousedown.stop="startResizing(element, 'bottom-right', $event)"></div>
                    </template>
                </div>

                <!-- Icon Elements -->
                <div v-for="element in elements.filter(el => el.type === 'icon')"
                     :key="element.id"
                     class="icon-element"
                     :class="{
                         selected: selectedElements.includes(element.id),
                         'multi-selected': selectedElements.length > 1 && selectedElements.includes(element.id)
                     }"
                     :style="getIconStyle(element)"
                     @click.stop="selectElement(element, $event)"
                     @dblclick="showElementContextMenu(element, $event)"
                     @mousedown="startDragging(element, $event)"
                     @contextmenu.prevent="showElementContextMenu(element, $event)">

                    <div v-if="element.icon && element.icon.includes('<svg')"
                         v-html="element.icon"
                         style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">
                    </div>
                    <span v-else>{{ element.icon }}</span>

                    <!-- Resize handles for icons -->
                    <template v-if="selectedElements.includes(element.id) && selectedElements.length === 1">
                        <div class="resize-handle top-left" @mousedown.stop="startResizing(element, 'top-left', $event)"></div>
                        <div class="resize-handle top-right" @mousedown.stop="startResizing(element, 'top-right', $event)"></div>
                        <div class="resize-handle bottom-left" @mousedown.stop="startResizing(element, 'bottom-left', $event)"></div>
                        <div class="resize-handle bottom-right" @mousedown.stop="startResizing(element, 'bottom-right', $event)"></div>
                    </template>
                </div>



                <!-- Context Menu with Integrated Properties -->
                <div v-if="contextMenu.visible"
                     class="context-menu"
                     :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }">

                    <!-- Quick Actions -->
                    <div class="context-actions">
                        <div class="context-menu-item" @click="duplicateFromContext">📋 Duplicate</div>
                        <div class="context-menu-item" @click="deleteFromContext">🗑️ Delete</div>
                        <div class="context-menu-item" @click="copyFromContext">📄 Copy</div>
                        <div class="context-menu-item" @click="hideContextMenu">❌ Close</div>
                    </div>

                    <!-- Properties Section -->
                    <div class="context-properties" v-if="primarySelectedElement">
                        <h4>Properties {{ selectedElements.length > 1 ? `(${selectedElements.length} selected)` : '' }}</h4>

                        <div class="property-group" v-if="primarySelectedElement.text !== undefined">
                            <label>Text</label>
                            <input type="text"
                                   class="property-input"
                                   v-model="primarySelectedElement.text">
                        </div>

                        <div class="property-group" v-if="primarySelectedElement.url !== undefined">
                            <label>URL</label>
                            <input type="text"
                                   class="property-input"
                                   v-model="primarySelectedElement.url"
                                   placeholder="Enter media URL">
                        </div>

                        <div class="property-group" v-if="primarySelectedElement.fontSize !== undefined">
                            <label>Font Size</label>
                            <input type="range"
                                   class="property-input"
                                   v-model.number="primarySelectedElement.fontSize"
                                   min="8" max="72">
                            <span>{{ primarySelectedElement.fontSize }}px</span>
                        </div>

                        <div class="property-group" v-if="primarySelectedElement.color !== undefined">
                            <label>Foreground Color</label>
                            <div class="color-picker-row">
                                <div class="color-preview"
                                     :style="{ backgroundColor: primarySelectedElement.color }"
                                     @click.stop="showColorPicker($event, 'foreground')"
                                     title="Click to change foreground color">
                                    🎨
                                </div>
                                <span class="color-value">{{ primarySelectedElement.color }}</span>
                            </div>
                        </div>

                        <div class="property-group">
                            <label>Background Color</label>
                            <div class="color-picker-row">
                                <div class="color-preview"
                                     :style="{ backgroundColor: primarySelectedElement.backgroundColor || '#ffffff' }"
                                     @click.stop="showColorPicker($event, 'background')"
                                     title="Click to change background color">
                                    🎨
                                </div>
                                <span class="color-value">{{ primarySelectedElement.backgroundColor || '#ffffff' }}</span>
                            </div>
                        </div>

                        <div class="property-group" v-if="primarySelectedElement.width !== undefined">
                            <label>Width</label>
                            <input type="range"
                                   class="property-input"
                                   v-model.number="primarySelectedElement.width"
                                   min="20" max="500">
                            <span>{{ primarySelectedElement.width }}px</span>
                        </div>

                        <div class="property-group" v-if="primarySelectedElement.height !== undefined">
                            <label>Height</label>
                            <input type="range"
                                   class="property-input"
                                   v-model.number="primarySelectedElement.height"
                                   min="20" max="500">
                            <span>{{ primarySelectedElement.height }}px</span>
                        </div>

                        <div class="property-group" v-if="primarySelectedElement.fontWeight !== undefined">
                            <label>Font Weight</label>
                            <select class="property-input" v-model="primarySelectedElement.fontWeight">
                                <option value="normal">Normal</option>
                                <option value="bold">Bold</option>
                                <option value="lighter">Lighter</option>
                            </select>
                        </div>


                    </div>
                </div>

                <!-- Background Modal -->
                <div v-if="showBackgroundModal" class="background-modal" @click="hideBackgroundModal">
                    <div class="background-panel" @click.stop>
                        <h4>Customize Background</h4>

                        <div class="property-group">
                            <label>Background Color</label>
                            <input type="color"
                                   class="color-input"
                                   v-model="canvasBackground.color">
                        </div>

                        <div class="property-group">
                            <label>Background Image URL</label>
                            <input type="text"
                                   class="url-input"
                                   v-model="canvasBackground.imageUrl"
                                   placeholder="Enter image URL (optional)">
                        </div>

                        <div style="margin-top: 15px;">
                            <button class="btn" @click="applyBackground" style="margin-right: 10px;">Apply</button>
                            <button class="btn" @click="clearBackground" style="margin-right: 10px;">Clear</button>
                            <button class="btn" @click="hideBackgroundModal">Cancel</button>
                        </div>
                    </div>
                </div>

                <!-- Media URL Modal -->
                <div v-if="showMediaModal" class="background-modal" @click="hideMediaModal">
                    <div class="background-panel" @click.stop>
                        <h4>Add {{ mediaModalType }}</h4>

                        <div class="property-group">
                            <label>{{ mediaModalType }} URL</label>
                            <input type="text"
                                   class="url-input"
                                   v-model="mediaUrl"
                                   :placeholder="'Enter ' + mediaModalType + ' URL'">
                        </div>

                        <div style="margin-top: 15px;">
                            <button class="btn" @click="confirmAddMedia" style="margin-right: 10px;">Add</button>
                            <button class="btn" @click="hideMediaModal">Cancel</button>
                        </div>
                    </div>
                </div>

                <!-- Icon Customizer Modal -->
                <div v-if="showIconModal" class="icon-modal" @click="hideIconModal">
                    <div class="icon-panel" @click.stop>
                        <h4>Add Custom Icon</h4>

                        <div class="icon-tabs">
                            <div class="icon-tab"
                                 :class="{ active: iconTab === 'markdown' }"
                                 @click="iconTab = 'markdown'">
                                Markdown/Emoji
                            </div>
                            <div class="icon-tab"
                                 :class="{ active: iconTab === 'iconify' }"
                                 @click="iconTab = 'iconify'">
                                Iconify
                            </div>
                        </div>

                        <!-- Markdown/Emoji Tab -->
                        <div v-if="iconTab === 'markdown'">
                            <div class="property-group">
                                <label>Enter Icon (Emoji, Unicode, SVG, or Text)</label>
                                <textarea class="markdown-input"
                                         v-model="customIconText"
                                         placeholder="EMOJIS: 🚀 ⭐ 💡 🎯 🔥 ❤️ ✅ ❌ ⚙️ 🔍&#10;SYMBOLS: ★ → ← ↑ ↓ ♠ ♥ ♦ ♣ ✓ ✗&#10;&#10;SVG EXAMPLES:&#10;&#10;Home:&#10;&lt;svg width='24' height='24' viewBox='0 0 24 24'&gt;&lt;path fill='currentColor' d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/&gt;&lt;/svg&gt;&#10;&#10;Star:&#10;&lt;svg width='24' height='24' viewBox='0 0 24 24'&gt;&lt;path fill='currentColor' d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/&gt;&lt;/svg&gt;&#10;&#10;User:&#10;&lt;svg width='24' height='24' viewBox='0 0 24 24'&gt;&lt;path fill='currentColor' d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/&gt;&lt;/svg&gt;&#10;&#10;Settings:&#10;&lt;svg width='24' height='24' viewBox='0 0 24 24'&gt;&lt;path fill='currentColor' d='M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.43-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z'/&gt;&lt;/svg&gt;"></textarea>
                            </div>

                            <div class="quick-examples">
                                <label style="font-size: 12px; margin-bottom: 8px; display: block;">Quick Pick (Click to Use):</label>
                                <div class="example-grid">
                                    <div class="example-icon" @click="customIconText = '🚀'" title="Rocket">🚀</div>
                                    <div class="example-icon" @click="customIconText = '⭐'" title="Star">⭐</div>
                                    <div class="example-icon" @click="customIconText = '💡'" title="Lightbulb">💡</div>
                                    <div class="example-icon" @click="customIconText = '❤️'" title="Heart">❤️</div>
                                    <div class="example-icon" @click="customIconText = '✅'" title="Check">✅</div>
                                    <div class="example-icon" @click="customIconText = '❌'" title="Cross">❌</div>
                                    <div class="example-icon" @click="customIconText = '⚙️'" title="Settings">⚙️</div>
                                    <div class="example-icon" @click="customIconText = '🔍'" title="Search">🔍</div>
                                    <div class="example-icon" @click="customIconText = '★'" title="Star Symbol">★</div>
                                    <div class="example-icon" @click="customIconText = '→'" title="Arrow">→</div>
                                    <div class="example-icon" @click="customIconText = '♠'" title="Spade">♠</div>
                                    <div class="example-icon" @click="customIconText = '♥'" title="Heart Symbol">♥</div>
                                    <div class="example-icon" @click="customIconText = '✓'" title="Check Mark">✓</div>
                                    <div class="example-icon" @click="customIconText = '●'" title="Circle">●</div>
                                    <div class="example-icon" @click="customIconText = '■'" title="Square">■</div>
                                    <div class="example-icon" @click="customIconText = '▲'" title="Triangle">▲</div>
                                </div>
                            </div>

                            <div class="property-group" v-if="customIconText">
                                <label>Preview</label>
                                <div class="icon-item" style="margin: 10px 0;">
                                    <div v-if="customIconText.includes('<svg')" v-html="customIconText" style="width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;"></div>
                                    <span v-else>{{ customIconText }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Iconify Tab -->
                        <div v-if="iconTab === 'iconify'">
                            <div class="property-group">
                                <label>Search Icons</label>
                                <input type="text"
                                       class="icon-search"
                                       v-model="iconifySearch"
                                       placeholder="Search icons (e.g., home, star, user)"
                                       @input="searchIconify">
                            </div>

                            <div class="icon-grid" v-if="iconifyResults.length > 0">
                                <div v-for="icon in iconifyResults"
                                     :key="icon.name"
                                     class="icon-item"
                                     @click="selectIconifyIcon(icon)"
                                     :title="icon.name">
                                    <div v-html="icon.svg" style="width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;"></div>
                                </div>
                            </div>

                            <div v-if="iconifySearch && iconifyResults.length === 0" style="text-align: center; color: #666; margin: 20px 0;">
                                No icons found. Try different keywords.
                            </div>
                        </div>

                        <div style="margin-top: 20px; text-align: right;">
                            <button class="btn" @click="addCustomIcon" style="margin-right: 10px;" :disabled="!customIconText">Add Icon</button>
                            <button class="btn" @click="hideIconModal">Cancel</button>
                        </div>
                    </div>
                </div>

                <!-- Color Picker Popup -->
                <div v-if="colorPicker.visible"
                     class="color-picker-popup"
                     :style="{ left: colorPicker.x + 'px', top: colorPicker.y + 'px' }">

                    <h5 style="margin: 0 0 10px 0;">Choose Color</h5>

                    <!-- Color Palette -->
                    <div class="color-palette">
                        <div v-for="color in colorPalette"
                             :key="color"
                             class="color-swatch"
                             :class="{ selected: colorPicker.currentColor === color }"
                             :style="{ backgroundColor: color }"
                             @click="selectColor(color)"
                             :title="color">
                        </div>
                    </div>

                    <!-- Custom Color Section -->
                    <div class="custom-color-section">
                        <label style="font-size: 12px; margin-bottom: 5px; display: block;">Custom Color</label>
                        <input type="color"
                               class="custom-color-input"
                               :value="colorPicker.currentColor"
                               @change="selectColor($event.target.value)">

                        <input type="text"
                               class="hex-input"
                               :value="colorPicker.currentColor"
                               @input="selectColor($event.target.value)"
                               placeholder="#000000"
                               maxlength="7">
                    </div>

                    <div style="margin-top: 15px; text-align: right;">
                        <button class="btn" @click="hideColorPicker" style="font-size: 12px; padding: 5px 10px;">Close</button>
                    </div>
                </div>

                <!-- Status Display -->
                <div class="coordinates" v-if="selectedElements.length > 0">
                    Selected: {{ selectedElements.length }} item(s)
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, reactive, nextTick, computed } = Vue;

        createApp({
            setup() {
                const elements = ref([]);
                const selectedElements = ref([]);
                const editingElement = ref(null);
                const dragging = ref(false);
                const dragOffset = ref({ x: 0, y: 0 });
                const mouseX = ref(0);
                const mouseY = ref(0);
                const canvas = ref(null);
                const editInput = ref(null);
                const fileInput = ref(null);
                const showProperties = ref(false);
                const propertiesPosition = ref({ x: 10, y: 10 });
                const resizing = ref(false);
                const resizeData = ref({ element: null, handle: '', startX: 0, startY: 0, startWidth: 0, startHeight: 0, startFontSize: 0 });
                const contextMenu = ref({ visible: false, x: 0, y: 0, element: null });
                const selectionBox = ref({ visible: false, startX: 0, startY: 0, endX: 0, endY: 0 });
                const selecting = ref(false);
                const history = ref([]);
                const historyIndex = ref(-1);
                const clipboard = ref([]);
                const showBackgroundModal = ref(false);
                const showMediaModal = ref(false);
                const mediaModalType = ref('');
                const mediaUrl = ref('');
                const canvasBackground = ref({ color: '#ffffff', imageUrl: '' });
                const colorPicker = ref({ visible: false, x: 0, y: 0, currentColor: '#000000', type: 'foreground' });
                const mediaFileInput = ref(null);
                const currentUploadType = ref('');
                const showIconModal = ref(false);
                const iconTab = ref('markdown');
                const customIconText = ref('');
                const iconifySearch = ref('');
                const iconifyResults = ref([]);

                // Color palette for quick selection
                const colorPalette = [
                    '#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF',
                    '#800000', '#808080', '#800080', '#008000', '#000080', '#808000', '#C0C0C0', '#FF8000',
                    '#FF4444', '#44FF44', '#4444FF', '#FFFF44', '#FF44FF', '#44FFFF', '#884444', '#448844',
                    '#444488', '#888844', '#884488', '#448888', '#FF8844', '#88FF44', '#4488FF', '#8844FF',
                    '#2C3E50', '#3498DB', '#E74C3C', '#27AE60', '#F39C12', '#9B59B6', '#1ABC9C', '#34495E',
                    '#95A5A6', '#E67E22', '#E91E63', '#8E44AD', '#16A085', '#2980B9', '#C0392B', '#D35400'
                ];

                let elementIdCounter = 1;

                // Computed properties
                const primarySelectedElement = computed(() => {
                    if (selectedElements.value.length === 0) return null;
                    return elements.value.find(el => el.id === selectedElements.value[0]);
                });

                const canUndo = computed(() => historyIndex.value > 0);
                const canRedo = computed(() => historyIndex.value < history.value.length - 1);

                const canvasStyle = computed(() => {
                    const style = {
                        backgroundColor: canvasBackground.value.color
                    };
                    if (canvasBackground.value.imageUrl) {
                        style.backgroundImage = `url(${canvasBackground.value.imageUrl})`;
                    }
                    return style;
                });

                // History management
                const saveState = () => {
                    const state = JSON.parse(JSON.stringify(elements.value));
                    history.value = history.value.slice(0, historyIndex.value + 1);
                    history.value.push(state);
                    historyIndex.value = history.value.length - 1;

                    // Limit history size
                    if (history.value.length > 50) {
                        history.value.shift();
                        historyIndex.value--;
                    }
                };

                const undo = () => {
                    if (canUndo.value) {
                        historyIndex.value--;
                        elements.value = JSON.parse(JSON.stringify(history.value[historyIndex.value]));
                        selectedElements.value = [];
                        editingElement.value = null;
                        showProperties.value = false;
                    }
                };

                const redo = () => {
                    if (canRedo.value) {
                        historyIndex.value++;
                        elements.value = JSON.parse(JSON.stringify(history.value[historyIndex.value]));
                        selectedElements.value = [];
                        editingElement.value = null;
                        showProperties.value = false;
                    }
                };

                const addTextElement = (type) => {
                    saveState();
                    const newElement = {
                        id: elementIdCounter++,
                        type: type,
                        text: type === 'heading' ? 'New Heading' :
                              type === 'paragraph' ? 'New paragraph text here...' :
                              'New Label',
                        x: 50 + (elements.value.length * 20),
                        y: 50 + (elements.value.length * 20),
                        fontSize: type === 'heading' ? 24 : type === 'label' ? 14 : 16,
                        color: '#2c3e50',
                        fontWeight: type === 'heading' ? 'bold' : 'normal'
                    };

                    elements.value.push(newElement);
                    selectedElements.value = [newElement.id];

                    // Start editing immediately
                    nextTick(() => {
                        startEditing(newElement);
                    });
                };

                const selectElement = (element, event) => {
                    event.stopPropagation();

                    if (event.ctrlKey || event.metaKey) {
                        // Multi-select with Ctrl/Cmd
                        if (selectedElements.value.includes(element.id)) {
                            selectedElements.value = selectedElements.value.filter(id => id !== element.id);
                        } else {
                            selectedElements.value = [...selectedElements.value, element.id];
                        }
                    } else {
                        // Single select
                        selectedElements.value = [element.id];
                    }
                    editingElement.value = null;
                    hideContextMenu();
                    hideColorPicker();
                };

                const handleCanvasClick = (event) => {
                    // Don't interfere with selection operations
                    if (selecting.value || dragging.value) {
                        return;
                    }

                    // Only add label if clicking on empty canvas (not during multi-select)
                    if (event.target === canvas.value && !event.ctrlKey && !event.metaKey && selectedElements.value.length === 0) {
                        // Add label at click position
                        const rect = canvas.value.getBoundingClientRect();
                        const x = event.clientX - rect.left;
                        const y = event.clientY - rect.top;

                        saveState();
                        const newElement = {
                            id: elementIdCounter++,
                            type: 'label',
                            text: 'New Label',
                            x: x,
                            y: y,
                            fontSize: 14,
                            color: '#2c3e50',
                            fontWeight: 'normal'
                        };

                        elements.value.push(newElement);
                        selectedElements.value = [newElement.id];
                        hideContextMenu();
                        hideColorPicker();

                        // Start editing immediately
                        nextTick(() => {
                            startEditing(newElement);
                        });
                    } else if (event.target === canvas.value) {
                        // Only deselect if not in the middle of a selection operation
                        setTimeout(() => {
                            if (!selecting.value) {
                                selectedElements.value = [];
                                editingElement.value = null;
                                hideContextMenu();
                                hideColorPicker();
                            }
                        }, 50);
                    }
                };

                // Close property window when clicking elsewhere
                const handleDocumentClick = (event) => {
                    // Check if click is outside context menu and properties
                    const contextMenuEl = document.querySelector('.context-menu');
                    const colorPickerEl = document.querySelector('.color-picker-popup');
                    const colorPreviewEl = event.target.closest('.color-preview');

                    // Don't close if clicking on color preview icons
                    if (colorPreviewEl) {
                        return;
                    }

                    if (contextMenuEl && !contextMenuEl.contains(event.target)) {
                        hideContextMenu();
                    }

                    if (colorPickerEl && !colorPickerEl.contains(event.target)) {
                        hideColorPicker();
                    }
                };

                const showElementContextMenu = (element, event) => {
                    if (!selectedElements.value.includes(element.id)) {
                        selectedElements.value = [element.id];
                    }
                    editingElement.value = null;

                    const rect = canvas.value.getBoundingClientRect();
                    contextMenu.value = {
                        visible: true,
                        x: Math.min(event.clientX - rect.left, rect.width - 300),
                        y: Math.min(event.clientY - rect.top, rect.height - 400),
                        element: element
                    };
                };

                const hideContextMenu = () => {
                    contextMenu.value.visible = false;
                };

                // Color picker functions
                const showColorPicker = (event, type) => {
                    event.stopPropagation();
                    console.log('showColorPicker called with type:', type); // Debug log

                    const rect = event.target.getBoundingClientRect();
                    const canvasRect = canvas.value.getBoundingClientRect();

                    // Get current color from the selected element
                    let currentColor = '#000000';
                    if (primarySelectedElement.value) {
                        if (type === 'foreground') {
                            currentColor = primarySelectedElement.value.color || '#000000';
                        } else {
                            currentColor = primarySelectedElement.value.backgroundColor || '#ffffff';
                        }
                    }

                    colorPicker.value = {
                        visible: true,
                        x: rect.right - canvasRect.left + 10,
                        y: rect.top - canvasRect.top,
                        currentColor: currentColor,
                        type: type
                    };

                    console.log('Color picker state:', colorPicker.value); // Debug log
                };

                const hideColorPicker = () => {
                    colorPicker.value.visible = false;
                };

                const selectColor = (color) => {
                    // Validate hex color format
                    if (typeof color === 'string' && color.startsWith('#')) {
                        if (color.length === 7 || color.length === 4) {
                            colorPicker.value.currentColor = color;

                            // Apply color to selected elements
                            if (colorPicker.value.type === 'foreground') {
                                selectedElements.value.forEach(id => {
                                    const element = elements.value.find(el => el.id === id);
                                    if (element) {
                                        element.color = color;
                                    }
                                });
                            } else {
                                selectedElements.value.forEach(id => {
                                    const element = elements.value.find(el => el.id === id);
                                    if (element) {
                                        element.backgroundColor = color;
                                    }
                                });
                            }

                            // Force reactivity update
                            elements.value = [...elements.value];
                        }
                    }
                };

                const updateElementColor = (event) => {
                    selectedElements.value.forEach(id => {
                        const element = elements.value.find(el => el.id === id);
                        if (element) {
                            element.color = event.target.value;
                        }
                    });
                    elements.value = [...elements.value];
                };

                const updateBackgroundColor = (event) => {
                    selectedElements.value.forEach(id => {
                        const element = elements.value.find(el => el.id === id);
                        if (element) {
                            element.backgroundColor = event.target.value;
                        }
                    });
                    elements.value = [...elements.value];
                };

                const addShape = (shapeType) => {
                    saveState();
                    let width = 100, height = 60;

                    // Set default sizes for different shapes
                    switch(shapeType) {
                        case 'circle':
                            width = height = 80;
                            break;
                        case 'oval':
                            width = 120;
                            height = 80;
                            break;
                        case 'line':
                            width = 150;
                            height = 2;
                            break;
                        case 'dotted-line':
                            width = 150;
                            height = 2;
                            break;
                        case 'curve':
                            width = 100;
                            height = 50;
                            break;
                        case 'triangle':
                            width = 60;
                            height = 50;
                            break;
                        case 'diamond':
                            width = height = 60;
                            break;
                        case 'arrow-right':
                        case 'arrow-left':
                            width = 40;
                            height = 20;
                            break;
                        case 'arrow-up':
                        case 'arrow-down':
                            width = 20;
                            height = 40;
                            break;
                    }

                    const newElement = {
                        id: elementIdCounter++,
                        type: 'shape',
                        shape: shapeType,
                        x: 50 + (elements.value.length * 20),
                        y: 50 + (elements.value.length * 20),
                        width: width,
                        height: height,
                        backgroundColor: 'white',
                        borderColor: 'black'
                    };

                    elements.value.push(newElement);
                    selectedElements.value = [newElement.id];
                };

                const addIcon = (iconType) => {
                    saveState();
                    const icons = {
                        star: '⭐',
                        heart: '❤️',
                        check: '✅',
                        cross: '❌',
                        warning: '⚠️',
                        info: 'ℹ️',
                        home: '🏠',
                        phone: '📞',
                        email: '📧',
                        user: '👤',
                        settings: '⚙️',
                        search: '🔍'
                    };

                    const newElement = {
                        id: elementIdCounter++,
                        type: 'icon',
                        icon: icons[iconType],
                        x: 50 + (elements.value.length * 20),
                        y: 50 + (elements.value.length * 20),
                        fontSize: 24,
                        color: '#2c3e50',
                        width: 30,
                        height: 30
                    };

                    elements.value.push(newElement);
                    selectedElements.value = [newElement.id];
                };

                const addMediaElement = (mediaType) => {
                    mediaModalType.value = mediaType;
                    mediaUrl.value = '';
                    showMediaModal.value = true;
                };

                const confirmAddMedia = () => {
                    if (mediaUrl.value.trim()) {
                        saveState();
                        const newElement = {
                            id: elementIdCounter++,
                            type: 'media',
                            mediaType: mediaModalType.value,
                            url: mediaUrl.value.trim(),
                            x: 50 + (elements.value.length * 20),
                            y: 50 + (elements.value.length * 20),
                            width: mediaModalType.value === 'audio' ? 300 : 200,
                            height: mediaModalType.value === 'audio' ? 50 : 150,
                            alt: `${mediaModalType.value} element`
                        };

                        elements.value.push(newElement);
                        selectedElements.value = [newElement.id];
                        hideMediaModal();
                    }
                };

                const hideMediaModal = () => {
                    showMediaModal.value = false;
                    mediaUrl.value = '';
                };

                // File upload functions
                const triggerFileUpload = (mediaType) => {
                    currentUploadType.value = mediaType;

                    // Set appropriate file accept types
                    const acceptTypes = {
                        image: 'image/*',
                        audio: 'audio/*',
                        video: 'video/*'
                    };

                    mediaFileInput.value.accept = acceptTypes[mediaType];
                    mediaFileInput.value.click();
                };

                const handleFileUpload = (event) => {
                    const file = event.target.files[0];
                    if (!file) return;

                    // Create object URL for the file
                    const fileUrl = URL.createObjectURL(file);

                    saveState();
                    const newElement = {
                        id: elementIdCounter++,
                        type: 'media',
                        mediaType: currentUploadType.value,
                        url: fileUrl,
                        fileName: file.name,
                        x: 50 + (elements.value.length * 20),
                        y: 50 + (elements.value.length * 20),
                        width: currentUploadType.value === 'audio' ? 300 : 200,
                        height: currentUploadType.value === 'audio' ? 50 : 150,
                        alt: `${currentUploadType.value} element`
                    };

                    elements.value.push(newElement);
                    selectedElements.value = [newElement.id];

                    // Reset file input
                    event.target.value = '';
                };

                const startEditing = (element) => {
                    editingElement.value = element;
                    selectedElements.value = [element.id];
                    hideContextMenu();
                    hideColorPicker();

                    nextTick(() => {
                        if (editInput.value && editInput.value[0]) {
                            editInput.value[0].focus();
                            editInput.value[0].select();
                        }
                    });
                };

                const stopEditing = () => {
                    editingElement.value = null;
                };

                // Selection box methods
                const startSelection = (event) => {
                    if (event.target === canvas.value) {
                        event.preventDefault();
                        selecting.value = true;
                        const rect = canvas.value.getBoundingClientRect();
                        selectionBox.value = {
                            visible: true,
                            startX: event.clientX - rect.left,
                            startY: event.clientY - rect.top,
                            endX: event.clientX - rect.left,
                            endY: event.clientY - rect.top
                        };

                        // Clear existing selection if not holding Ctrl/Cmd
                        if (!event.ctrlKey && !event.metaKey) {
                            selectedElements.value = [];
                        }

                        const handleMouseMove = (e) => {
                            if (selecting.value) {
                                e.preventDefault();
                                const rect = canvas.value.getBoundingClientRect();
                                selectionBox.value.endX = e.clientX - rect.left;
                                selectionBox.value.endY = e.clientY - rect.top;

                                // Update selected elements based on selection box
                                updateSelectionFromBox();
                            }
                        };

                        const handleMouseUp = (e) => {
                            e.preventDefault();

                            // Final selection update
                            updateSelectionFromBox();

                            // Keep selection box visible briefly to show final selection
                            setTimeout(() => {
                                selectionBox.value.visible = false;
                                selecting.value = false;
                            }, 200);

                            document.removeEventListener('mousemove', handleMouseMove);
                            document.removeEventListener('mouseup', handleMouseUp);
                        };

                        document.addEventListener('mousemove', handleMouseMove);
                        document.addEventListener('mouseup', handleMouseUp);
                    }
                };

                const updateSelectionFromBox = () => {
                    const box = selectionBox.value;
                    const minX = Math.min(box.startX, box.endX);
                    const maxX = Math.max(box.startX, box.endX);
                    const minY = Math.min(box.startY, box.endY);
                    const maxY = Math.max(box.startY, box.endY);

                    // Only proceed if selection box has meaningful size
                    if (Math.abs(maxX - minX) < 5 && Math.abs(maxY - minY) < 5) {
                        return;
                    }

                    const newSelected = [];
                    elements.value.forEach(element => {
                        // Get element bounds more accurately
                        let elementWidth = element.width || 100;
                        let elementHeight = element.height || 30;

                        // For text elements, estimate size based on content
                        if (element.type === 'text' || element.type === 'heading' || element.type === 'paragraph' || element.type === 'label') {
                            elementWidth = Math.max(element.text ? element.text.length * (element.fontSize || 16) * 0.6 : 100, 50);
                            elementHeight = element.fontSize || 16;
                        }

                        const elementRight = element.x + elementWidth;
                        const elementBottom = element.y + elementHeight;

                        // Check if element intersects with selection box
                        if (element.x < maxX && elementRight > minX &&
                            element.y < maxY && elementBottom > minY) {
                            newSelected.push(element.id);
                        }
                    });

                    // Merge with existing selection if Ctrl/Cmd is held
                    if (event && (event.ctrlKey || event.metaKey)) {
                        const combined = [...new Set([...selectedElements.value, ...newSelected])];
                        selectedElements.value = combined;
                    } else {
                        selectedElements.value = newSelected;
                    }
                };

                const getSelectionBoxStyle = () => {
                    const box = selectionBox.value;
                    const left = Math.min(box.startX, box.endX);
                    const top = Math.min(box.startY, box.endY);
                    const width = Math.abs(box.endX - box.startX);
                    const height = Math.abs(box.endY - box.startY);

                    return {
                        left: left + 'px',
                        top: top + 'px',
                        width: width + 'px',
                        height: height + 'px'
                    };
                };

                const startDragging = (element, event) => {
                    if (editingElement.value) return;

                    dragging.value = true;
                    if (!selectedElements.value.includes(element.id)) {
                        selectedElements.value = [element.id];
                    }

                    const rect = canvas.value.getBoundingClientRect();
                    dragOffset.value = {
                        x: event.clientX - rect.left - element.x,
                        y: event.clientY - rect.top - element.y
                    };

                    const handleMouseMove = (e) => {
                        if (dragging.value && selectedElements.value.length > 0) {
                            const rect = canvas.value.getBoundingClientRect();
                            const deltaX = (e.clientX - rect.left - dragOffset.value.x) - element.x;
                            const deltaY = (e.clientY - rect.top - dragOffset.value.y) - element.y;

                            // Move all selected elements
                            selectedElements.value.forEach(id => {
                                const el = elements.value.find(el => el.id === id);
                                if (el) {
                                    el.x += deltaX;
                                    el.y += deltaY;
                                }
                            });
                        }
                    };

                    const handleMouseUp = () => {
                        dragging.value = false;
                        document.removeEventListener('mousemove', handleMouseMove);
                        document.removeEventListener('mouseup', handleMouseUp);
                    };

                    document.addEventListener('mousemove', handleMouseMove);
                    document.addEventListener('mouseup', handleMouseUp);
                };

                // Keyboard shortcuts
                const handleKeyDown = (event) => {
                    if (event.target !== canvas.value) return;

                    if ((event.ctrlKey || event.metaKey) && event.key === 'c') {
                        event.preventDefault();
                        copySelected();
                    } else if ((event.ctrlKey || event.metaKey) && event.key === 'v') {
                        event.preventDefault();
                        pasteSelected();
                    } else if ((event.ctrlKey || event.metaKey) && event.key === 'z' && !event.shiftKey) {
                        event.preventDefault();
                        undo();
                    } else if ((event.ctrlKey || event.metaKey) && (event.key === 'y' || (event.key === 'z' && event.shiftKey))) {
                        event.preventDefault();
                        redo();
                    } else if (event.key === 'Delete' || event.key === 'Backspace') {
                        event.preventDefault();
                        deleteSelected();
                    }
                };

                // Copy/Paste functionality
                const copySelected = () => {
                    if (selectedElements.value.length > 0) {
                        clipboard.value = selectedElements.value.map(id => {
                            const element = elements.value.find(el => el.id === id);
                            return JSON.parse(JSON.stringify(element));
                        });
                    }
                };

                const pasteSelected = () => {
                    if (clipboard.value.length > 0) {
                        saveState();
                        const newElements = clipboard.value.map(element => ({
                            ...element,
                            id: elementIdCounter++,
                            x: element.x + 20,
                            y: element.y + 20
                        }));

                        elements.value.push(...newElements);
                        selectedElements.value = newElements.map(el => el.id);
                    }
                };

                const updateMousePosition = (event) => {
                    const rect = canvas.value.getBoundingClientRect();
                    mouseX.value = Math.round(event.clientX - rect.left);
                    mouseY.value = Math.round(event.clientY - rect.top);
                };

                const getElementStyle = (element) => {
                    return {
                        left: element.x + 'px',
                        top: element.y + 'px',
                        fontSize: element.fontSize + 'px',
                        color: element.color,
                        fontWeight: element.fontWeight
                    };
                };

                const getShapeStyle = (element) => {
                    const style = {
                        left: element.x + 'px',
                        top: element.y + 'px',
                        width: element.width + 'px',
                        height: element.height + 'px',
                        backgroundColor: element.backgroundColor || 'white',
                        borderColor: element.borderColor || 'black',
                        '--bg-color': element.backgroundColor || 'white',
                        '--border-color': element.borderColor || 'black',
                        '--width': element.width + 'px',
                        '--height': element.height + 'px'
                    };

                    // Special handling for lines - force 2px height
                    if (element.shape === 'line' || element.shape === 'dotted-line') {
                        style.height = '2px';
                        if (element.shape === 'line') {
                            style.backgroundColor = element.borderColor || 'black';
                        } else {
                            style.borderTopColor = element.borderColor || 'black';
                        }
                    }

                    return style;
                };

                const getIconStyle = (element) => {
                    return {
                        left: element.x + 'px',
                        top: element.y + 'px',
                        fontSize: element.fontSize + 'px',
                        color: element.color
                    };
                };

                const getMediaStyle = (element) => {
                    return {
                        left: element.x + 'px',
                        top: element.y + 'px',
                        width: element.width + 'px',
                        height: element.height + 'px'
                    };
                };

                // Background customization methods
                const showBackgroundOptions = () => {
                    showBackgroundModal.value = true;
                };

                const hideBackgroundModal = () => {
                    showBackgroundModal.value = false;
                };

                const applyBackground = () => {
                    hideBackgroundModal();
                };

                const clearBackground = () => {
                    canvasBackground.value = { color: '#ffffff', imageUrl: '' };
                    hideBackgroundModal();
                };

                // Icon customizer functions
                const showIconCustomizer = () => {
                    showIconModal.value = true;
                    iconTab.value = 'markdown';
                    customIconText.value = '';
                    iconifySearch.value = '';
                    iconifyResults.value = [];
                };

                const hideIconModal = () => {
                    showIconModal.value = false;
                    customIconText.value = '';
                    iconifySearch.value = '';
                    iconifyResults.value = [];
                };

                const addCustomIcon = () => {
                    if (customIconText.value.trim()) {
                        saveState();
                        const newElement = {
                            id: elementIdCounter++,
                            type: 'icon',
                            icon: customIconText.value.trim(),
                            x: 50 + (elements.value.length * 20),
                            y: 50 + (elements.value.length * 20),
                            fontSize: 24,
                            color: '#2c3e50',
                            width: 30,
                            height: 30
                        };

                        elements.value.push(newElement);
                        selectedElements.value = [newElement.id];
                        hideIconModal();
                    }
                };

                const searchIconify = async () => {
                    if (!iconifySearch.value.trim()) {
                        iconifyResults.value = [];
                        return;
                    }

                    try {
                        // Expanded icon library with more options
                        const mockIcons = [
                            { name: 'home', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg>' },
                            { name: 'star', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>' },
                            { name: 'user', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>' },
                            { name: 'settings', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.43-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/></svg>' },
                            { name: 'search', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/></svg>' },
                            { name: 'heart', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>' },
                            { name: 'check', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg>' },
                            { name: 'close', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>' },
                            { name: 'arrow', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/></svg>' },
                            { name: 'menu', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/></svg>' },
                            { name: 'phone', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/></svg>' },
                            { name: 'email', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/></svg>' },
                            { name: 'calendar', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/></svg>' },
                            { name: 'clock', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/><path fill="currentColor" d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/></svg>' },
                            { name: 'folder', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"/></svg>' },
                            { name: 'file', svg: '<svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M6 2c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h10l6-6V4c0-1.1-.9-2-2-2H6zm9 16l-3-3h3v3z"/></svg>' }
                        ];

                        const filtered = mockIcons.filter(icon =>
                            icon.name.toLowerCase().includes(iconifySearch.value.toLowerCase())
                        );

                        iconifyResults.value = filtered;
                    } catch (error) {
                        console.error('Error searching icons:', error);
                        iconifyResults.value = [];
                    }
                };

                const selectIconifyIcon = (icon) => {
                    customIconText.value = icon.svg;
                    iconTab.value = 'markdown';
                };

                const duplicateSelected = () => {
                    if (selectedElements.value.length > 0) {
                        saveState();
                        const newElements = selectedElements.value.map(id => {
                            const element = elements.value.find(el => el.id === id);
                            return {
                                ...element,
                                id: elementIdCounter++,
                                x: element.x + 20,
                                y: element.y + 20
                            };
                        });
                        elements.value.push(...newElements);
                        selectedElements.value = newElements.map(el => el.id);
                    }
                };

                const deleteSelected = () => {
                    if (selectedElements.value.length > 0) {
                        saveState();
                        elements.value = elements.value.filter(el => !selectedElements.value.includes(el.id));
                        selectedElements.value = [];
                        editingElement.value = null;
                        showProperties.value = false;
                    }
                };

                // Context menu actions
                const duplicateFromContext = () => {
                    duplicateSelected();
                    hideContextMenu();
                };

                const deleteFromContext = () => {
                    deleteSelected();
                    hideContextMenu();
                };

                const copyFromContext = () => {
                    copySelected();
                    hideContextMenu();
                };

                const clearAll = () => {
                    if (confirm('Are you sure you want to clear all elements?')) {
                        saveState();
                        elements.value = [];
                        selectedElements.value = [];
                        editingElement.value = null;
                        showProperties.value = false;
                    }
                };

                const startResizing = (element, handle, event) => {
                    resizing.value = true;
                    resizeData.value = {
                        element: element,
                        handle: handle,
                        startX: event.clientX,
                        startY: event.clientY,
                        startWidth: element.width || 100,
                        startHeight: element.height || 30,
                        startFontSize: element.fontSize || 16
                    };

                    const handleMouseMove = (e) => {
                        if (!resizing.value) return;

                        const deltaX = e.clientX - resizeData.value.startX;
                        const deltaY = e.clientY - resizeData.value.startY;
                        const element = resizeData.value.element;

                        if (element.type === 'shape') {
                            // Resize shapes by width/height
                            if (resizeData.value.handle.includes('right')) {
                                element.width = Math.max(20, resizeData.value.startWidth + deltaX);
                            }
                            if (resizeData.value.handle.includes('left')) {
                                const newWidth = Math.max(20, resizeData.value.startWidth - deltaX);
                                element.x = element.x + (element.width - newWidth);
                                element.width = newWidth;
                            }
                            if (resizeData.value.handle.includes('bottom')) {
                                element.height = Math.max(20, resizeData.value.startHeight + deltaY);
                            }
                            if (resizeData.value.handle.includes('top')) {
                                const newHeight = Math.max(20, resizeData.value.startHeight - deltaY);
                                element.y = element.y + (element.height - newHeight);
                                element.height = newHeight;
                            }
                        } else {
                            // Resize text/icons by font size
                            const sizeDelta = Math.max(deltaX, deltaY);
                            element.fontSize = Math.max(8, Math.min(72, resizeData.value.startFontSize + sizeDelta / 4));
                        }
                    };

                    const handleMouseUp = () => {
                        resizing.value = false;
                        document.removeEventListener('mousemove', handleMouseMove);
                        document.removeEventListener('mouseup', handleMouseUp);
                    };

                    document.addEventListener('mousemove', handleMouseMove);
                    document.addEventListener('mouseup', handleMouseUp);
                };

                const saveProject = () => {
                    const projectData = {
                        elements: elements.value,
                        version: '1.0',
                        timestamp: new Date().toISOString()
                    };

                    const dataStr = JSON.stringify(projectData, null, 2);
                    const blob = new Blob([dataStr], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `visual-editor-project-${new Date().toISOString().split('T')[0]}.json`;
                    a.click();
                    URL.revokeObjectURL(url);
                };

                const triggerLoadProject = () => {
                    fileInput.value.click();
                };

                const loadProject = (event) => {
                    const file = event.target.files[0];
                    if (!file) return;

                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const projectData = JSON.parse(e.target.result);
                            if (projectData.elements && Array.isArray(projectData.elements)) {
                                elements.value = projectData.elements;
                                selectedElements.value = [];
                                editingElement.value = null;

                                // Update element counter to avoid ID conflicts
                                const maxId = Math.max(0, ...projectData.elements.map(el => el.id || 0));
                                elementIdCounter = maxId + 1;

                                // Reset history
                                history.value = [JSON.parse(JSON.stringify(elements.value))];
                                historyIndex.value = 0;

                                alert('Project loaded successfully!');
                            } else {
                                alert('Invalid project file format.');
                            }
                        } catch (error) {
                            alert('Error loading project file: ' + error.message);
                        }
                    };
                    reader.readAsText(file);

                    // Reset file input
                    event.target.value = '';
                };



                // Initialize history
                history.value = [[]];
                historyIndex.value = 0;

                // Add document click listener to close popups
                document.addEventListener('click', handleDocumentClick);

                return {
                    elements,
                    selectedElements,
                    primarySelectedElement,
                    editingElement,
                    mouseX,
                    mouseY,
                    canvas,
                    editInput,
                    fileInput,
                    mediaFileInput,
                    currentUploadType,

                    contextMenu,
                    selectionBox,
                    showBackgroundModal,
                    showMediaModal,
                    mediaModalType,
                    mediaUrl,
                    showIconModal,
                    iconTab,
                    customIconText,
                    iconifySearch,
                    iconifyResults,
                    canvasBackground,
                    canvasStyle,
                    colorPicker,
                    colorPalette,
                    canUndo,
                    canRedo,
                    addTextElement,
                    addShape,
                    addIcon,
                    addMediaElement,
                    confirmAddMedia,
                    hideMediaModal,
                    triggerFileUpload,
                    handleFileUpload,
                    selectElement,
                    handleCanvasClick,
                    handleDocumentClick,
                    startSelection,
                    getSelectionBoxStyle,
                    showElementContextMenu,
                    hideContextMenu,
                    showColorPicker,
                    hideColorPicker,
                    selectColor,
                    updateElementColor,
                    updateBackgroundColor,
                    startEditing,
                    stopEditing,
                    startDragging,
                    startResizing,
                    handleKeyDown,
                    updateMousePosition,
                    getElementStyle,
                    getShapeStyle,
                    getIconStyle,
                    getMediaStyle,
                    showBackgroundOptions,
                    hideBackgroundModal,
                    applyBackground,
                    clearBackground,
                    showIconCustomizer,
                    hideIconModal,
                    addCustomIcon,
                    searchIconify,
                    selectIconifyIcon,
                    duplicateSelected,
                    deleteSelected,
                    clearAll,
                    undo,
                    redo,
                    copySelected,
                    pasteSelected,
                    duplicateFromContext,
                    deleteFromContext,
                    copyFromContext,
                    saveProject,
                    triggerLoadProject,
                    loadProject
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
