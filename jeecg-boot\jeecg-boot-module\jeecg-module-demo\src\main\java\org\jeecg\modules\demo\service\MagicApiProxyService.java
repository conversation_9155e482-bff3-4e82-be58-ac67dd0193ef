package org.jeecg.modules.demo.service;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.modules.demo.config.MagicApiProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.util.StreamUtils;
import java.io.IOException;
import java.net.URI;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

@Service
public class MagicApiProxyService {
    private static final Logger log = LoggerFactory.getLogger(MagicApiProxyService.class);
    
    /** 敏感头列表，这些头不应该转发 */
    private static final Set<String> SENSITIVE_HEADERS = new HashSet<>(Arrays.asList(
        "cookie", "authorization", "proxy-authorization"
    ));
    
    /** SQL注入检测正则 */
    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
        "(?i)(\\b(select|update|delete|insert|drop|alter|exec|union|create)\\b.*\\b(from|into|where|table|database)\\b)", 
        Pattern.CASE_INSENSITIVE
    );
    
    /** XSS攻击检测正则 */
    private static final Pattern XSS_PATTERN = Pattern.compile(
        "(?i)(<script|javascript:|on\\w+\\s*=|alert\\s*\\(|eval\\s*\\(|document\\.cookie)", 
        Pattern.CASE_INSENSITIVE
    );

    @Autowired
    @Qualifier("restTemplate")
    private RestTemplate restTemplate;

    @Autowired
    private MagicApiProperties magicApiProperties;

    /**
     * 代理请求到Magic API
     * 
     * @param finalMagicApiPath 最终的Magic API路径
     * @param httpMethodName HTTP方法名称
     * @param request 原始HTTP请求
     * @return 代理响应结果
     */
    public Result<?> proxyRequest(String finalMagicApiPath, String httpMethodName, HttpServletRequest request) {
        String username = "未知用户";
        String requestUri = request.getRequestURI();
        
        try {
            // 获取用户名用于日志记录
            String token = request.getHeader("x-access-token");
            if (token != null && !token.isEmpty()) {
                try {
                    username = JwtUtil.getUsername(token);
                } catch (Exception e) {
                    log.warn("无法从token获取用户名", e);
                }
            }
            
            // 记录请求日志
            log.info("用户[{}]请求代理: {} {} -> {}", 
                username, 
                httpMethodName, 
                requestUri, 
                finalMagicApiPath);
            
            // 请求验证
            validateRequest(request, finalMagicApiPath);
            
            // 构建目标URI
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(magicApiProperties.getBaseUrl())
                                                                .path(finalMagicApiPath);

            if (request.getQueryString() != null) {
                // 验证查询参数
                validateQueryString(request.getQueryString());
                uriBuilder.query(request.getQueryString());
            }
            URI uri = uriBuilder.build().toUri();
            log.debug("代理请求到: {}", uri.toString());

            // 创建请求头
            HttpHeaders headers = new HttpHeaders();
            
            // 从原始请求复制安全的头
            request.getHeaderNames().asIterator().forEachRemaining(headerName -> {
                if (!headerName.equalsIgnoreCase(HttpHeaders.HOST) && 
                    !headerName.equalsIgnoreCase(HttpHeaders.CONTENT_LENGTH) &&
                    !headerName.equalsIgnoreCase(HttpHeaders.ACCEPT_ENCODING) &&
                    !headerName.equalsIgnoreCase("connection") &&
                    !SENSITIVE_HEADERS.contains(headerName.toLowerCase())) {
                    String headerValue = request.getHeader(headerName);
                    headers.add(headerName, headerValue);
                }
            });
            
            // 添加安全头
            headers.set("X-Forwarded-For", request.getRemoteAddr());
            headers.set("X-Forwarded-Proto", request.getScheme());
            headers.set("X-Forwarded-Host", request.getServerName());
            
            // 处理Token和用户名
            if (token != null && !token.isEmpty()) {
                try {
                    if (username == null || username.isEmpty()) {
                        log.warn("无效的token: 用户名为空");
                        return Result.error(401, "无效的token");
                    }
                    headers.set("username", username);
                    log.debug("设置username头: {}", username);
                } catch (Exception e) {
                    log.warn("Token验证失败: {}", e.getMessage());
                    return Result.error(401, "无效的token");
                }
            } else {
                log.warn("请求中未找到x-access-token头: {}", requestUri);
            }
            
            // 请求未压缩的响应
            headers.set(HttpHeaders.ACCEPT_ENCODING, "identity");
            
            // 处理请求体
            byte[] body = null;
            String contentType = request.getContentType();
            
            // 对于POST/PUT/PATCH请求，读取请求体
            if (httpMethodName.equalsIgnoreCase("POST") || 
                httpMethodName.equalsIgnoreCase("PUT") || 
                httpMethodName.equalsIgnoreCase("PATCH")) {
                try {
                    body = StreamUtils.copyToByteArray(request.getInputStream());
                    
                    // 验证请求体内容
                    if (body != null && body.length > 0) {
                        String bodyContent = new String(body);
                        validateRequestBody(bodyContent);
                    }
                    
                    log.debug("转发请求体，长度: {}", (body != null ? body.length : 0));
                } catch (IOException e) {
                    log.error("读取请求体失败: {}", e.getMessage());
                    return Result.error("读取请求体失败");
                }
            }
            
            // 创建请求实体
            HttpEntity<byte[]> entity = new HttpEntity<>(body, headers);
            
            // 解析HTTP方法
            HttpMethod resolvedHttpMethod = HttpMethod.resolve(httpMethodName.toUpperCase());
            if (resolvedHttpMethod == null) {
                log.error("不支持的HTTP方法: {}", httpMethodName);
                return Result.error("不支持的HTTP方法: " + httpMethodName);
            }

            // 发送请求到Magic API
            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                uri,
                resolvedHttpMethod,
                entity,
                new ParameterizedTypeReference<Map<String, Object>>() {}
            );
            
            log.info("代理请求成功: {} -> {}, 状态码: {}", requestUri, uri, response.getStatusCode());
            return Result.OK(response.getBody());
            
        } catch (SecurityException e) {
            // 安全异常，记录详细日志但返回通用错误消息
            log.error("安全验证失败: {} {} -> {}. 错误: {}", 
                httpMethodName, 
                requestUri, 
                finalMagicApiPath, 
                e.getMessage());
            return Result.error("请求被拒绝");
        } catch (Exception e) {
            // 其他异常，记录详细日志但返回通用错误消息
            log.error("代理请求失败: {} {} -> {}. 错误: {}", 
                httpMethodName, 
                requestUri, 
                finalMagicApiPath, 
                e.toString(), e);
            
            // 生产环境返回通用错误信息
            return Result.error("服务请求失败，请稍后再试");
        }
    }
    
    /**
     * 验证请求的安全性
     * 
     * @param request HTTP请求
     * @param finalMagicApiPath 目标API路径
     * @throws SecurityException 如果检测到安全问题
     */
    private void validateRequest(HttpServletRequest request, String finalMagicApiPath) throws SecurityException {
        // 检查路径穿越攻击
        if (finalMagicApiPath.contains("../") || finalMagicApiPath.contains("..\\")) {
            throw new SecurityException("检测到路径穿越攻击");
        }
        
        // 检查路径中的特殊字符
        if (finalMagicApiPath.contains(";") || finalMagicApiPath.contains("\\x")) {
            throw new SecurityException("路径中包含非法字符");
        }
    }
    
    /**
     * 验证查询字符串的安全性
     * 
     * @param queryString 查询字符串
     * @throws SecurityException 如果检测到安全问题
     */
    private void validateQueryString(String queryString) throws SecurityException {
        if (queryString == null || queryString.isEmpty()) {
            return;
        }
        
        // 检查SQL注入
        if (SQL_INJECTION_PATTERN.matcher(queryString).find()) {
            throw new SecurityException("查询参数中检测到可能的SQL注入");
        }
        
        // 检查XSS攻击
        if (XSS_PATTERN.matcher(queryString).find()) {
            throw new SecurityException("查询参数中检测到可能的XSS攻击");
        }
    }
    
    /**
     * 验证请求体的安全性
     * 
     * @param bodyContent 请求体内容
     * @throws SecurityException 如果检测到安全问题
     */
    private void validateRequestBody(String bodyContent) throws SecurityException {
        if (bodyContent == null || bodyContent.isEmpty()) {
            return;
        }
        
        // 检查SQL注入
        if (SQL_INJECTION_PATTERN.matcher(bodyContent).find()) {
            throw new SecurityException("请求体中检测到可能的SQL注入");
        }
        
        // 检查XSS攻击
        if (XSS_PATTERN.matcher(bodyContent).find()) {
            throw new SecurityException("请求体中检测到可能的XSS攻击");
        }
    }
}
