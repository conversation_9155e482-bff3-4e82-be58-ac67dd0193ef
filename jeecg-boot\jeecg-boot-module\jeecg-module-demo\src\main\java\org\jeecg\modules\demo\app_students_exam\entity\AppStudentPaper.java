package org.jeecg.modules.demo.app_students_exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 学生试卷
 * @Author: JeecgBoot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
@TableName("app_student_paper")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class AppStudentPaper implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    /**考试ID*/
    private String examId;
    
    /**学生ID*/
    private String studentId;
    
    /**开始时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    
    /**提交时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;
    
    /**用时(分钟)*/
    private Integer usedTime;
    
    /**总分*/
    private BigDecimal totalScore;
    
    /**自动评分*/
    private BigDecimal autoScore;
    
    /**人工评分*/
    private BigDecimal manualScore;
    
    /**状态*/
    private Integer status;
    
    /**创建人*/
    private String createBy;
    
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**更新人*/
    private String updateBy;
    
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
} 