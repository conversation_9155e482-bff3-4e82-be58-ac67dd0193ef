package org.jeecg.modules.demo.app_students_exam.controller;

import com.alibaba.fastjson.JSON;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.app_students_exam.service.IAppStudentsExamService;
import org.jeecg.modules.demo.app_students_exam.entity.AppStudentsCourse;
import org.jeecg.modules.demo.app_students_exam.entity.AppStudentsQuestion;
import org.jeecg.modules.demo.app_students_exam.vo.ExamInfoVO;
import org.jeecg.modules.demo.app_students_exam.vo.QuestionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;
import org.jeecg.modules.demo.app_students_exam.service.IAppStudentsMyExamsViewService;
import org.jeecg.modules.demo.app_students_exam.entity.AppStudentsMyExamsView;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.ResponseEntity;
import org.jeecg.modules.demo.annotation.MagicApiProxy;
import org.springframework.core.ParameterizedTypeReference;
import java.util.Map;
import org.apache.shiro.authz.annotation.RequiresRoles;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.util.JwtUtil;

/**
 * @Description: 学生考试控制器
 * @Author: jeecg-boot
 * @Date: 2023-06-01
 * @Version: V1.0
 */
@Tag(name = "学生考试")
@RestController
@RequestMapping("/appStudentsExam")
@Slf4j
public class AppStudentsExamController {

    @Autowired
    private IAppStudentsExamService appStudentsExamService;
    
    @Autowired
    private org.springframework.jdbc.core.JdbcTemplate jdbcTemplate;

    @Autowired
    private IAppStudentsMyExamsViewService appStudentsMyExamsViewService;

    @Autowired
    @Qualifier("restTemplate")
    private RestTemplate restTemplate;

    /**
     * 演示代理Magic-API 
     *  GET: http://127.0.0.1:8080/jeecg-boot/appStudentsExam/demo/proxy
     * GET: http://localhost:9999/demo/1
       import org.springframework.web.context.request.RequestAttributes
       import request;
       return header.username 
     
     * @return 代理返回的数据
     */
    @AutoLog(value = "演示代理API")
    @Operation(summary = "演示代理API", description = "演示代理到Magic API")
    @GetMapping(value = "/demo/proxy")
    @MagicApiProxy("/demo/1")  //  GET: http://localhost:9999/demo/1
    public Result<?> demoProxy() {
        // This method will be handled by the MagicApiProxyAspect
        return null;
    }


    @Operation(summary = "演示代理2", description = "演示代理到Magic API")
    @GetMapping(value = "/demo/2")
    @MagicApiProxy("/demo/2")  //  GET: http://localhost:9999/demo/2
    public Result<?> demoProxy2() {
        // This method will be handled by the MagicApiProxyAspect
        return null;
    }
    
    /**
     * 注意：教师API代理已移至MagicApiProxyController
     * 请使用 http://127.0.0.1:8080/jeecg-boot/magicapi/teacher/** 路径访问
     */
    
    /**
     * 学生API代理
     * http://127.0.0.1:8080/jeecg-boot/appStudentsExam/student/**
     * 代理到: http://localhost:9999/student/**
     * 需要student角色
     * 
     * @return 代理返回的数据
     */
    @AutoLog(value = "学生API代理")
    @Operation(summary = "学生API代理", description = "代理到Magic API的学生接口")
    @RequiresRoles("stu")  // 修复：改为正确的学生角色编码
    @RequestMapping(value = "/student/**", method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE})
    @MagicApiProxy("/student")  // 代理到Magic API的/student路径
    public Result<?> proxyStudentApis() {
        // 由MagicApiProxy处理
        return null;
    }

    /**
     * 获取考试课程列表
     *
     * @return
     */
    @AutoLog(value = "获取考试课程列表")
    @Operation(summary = "获取考试课程列表", description = "获取考试课程列表")
    @GetMapping(value = "/courses")
    public Result<?> getExamCourses() {
        try {
            List<?> courseList = appStudentsExamService.getExamCourses();
            return Result.OK(courseList);
        } catch (Exception e) {
            log.error("获取考试课程列表失败", e);
            return Result.error("获取考试课程列表失败: " + e.getMessage());
        }
    }

    /**
     * 单题提交学生答案
     *
     * @param payload 答案内容
     * @return
     */
    @AutoLog(value = "单题提交学生答案")
    @Operation(summary = "单题提交学生答案", description = "单题提交学生答案")
    @PostMapping(value = "/answer")
    public Result<?> submitStudentAnswer(@RequestBody Map<String, Object> payload) {
        try {
            appStudentsExamService.submitStudentAnswer(payload);
            return Result.OK("提交成功");
        } catch (Exception e) {
            log.error("单题提交学生答案失败", e);
            return Result.error("单题提交学生答案失败: " + e.getMessage());
        }
    }

    /**
     * 提交整个考试的答案
     *
     * @param courseId 课程ID
     * @param paperId 试卷ID（可选，如果没有提供则自动生成）
     * @param studentId 学生ID（可选，如果没有提供则从JWT中获取）
     * @param answers 答案数组（JSON字符串）
     * @return
     */
    @AutoLog(value = "提交考试答案")
    @Operation(summary = "提交考试答案", description = "提交整个考试的答案")
    @PostMapping(value = "/submit")
    public Result<?> submitExamAnswers(
            @RequestParam String courseId,
            @RequestParam(required = false) String paperId,
            @RequestParam(required = false) String studentId,
            @RequestParam String answers) {
        try {
            log.info("提交考试答案 - courseId: {}, paperId: {}, studentId: {}", courseId, paperId, studentId);
            log.debug("答案数据: {}", answers);
            
            // 如果没有提供paperId，生成一个临时的paperId
            if (paperId == null || paperId.isEmpty()) {
                paperId = "temp_" + System.currentTimeMillis() + "_" + java.util.UUID.randomUUID().toString().substring(0, 8);
                log.info("自动生成paperId: {}", paperId);
            }
            
            // 如果没有提供studentId，从JWT中获取当前用户信息
            if (studentId == null || studentId.isEmpty()) {
                try {
                    javax.servlet.http.HttpServletRequest request = org.jeecg.common.util.SpringContextUtils.getHttpServletRequest();
                    String username = org.jeecg.common.system.util.JwtUtil.getUserNameByToken(request);
                    studentId = username; // 使用用户名作为studentId，或者查询实际的studentId
                    log.info("从JWT获取studentId: {}", studentId);
                } catch (Exception e) {
                    log.error("从JWT获取用户信息失败", e);
                    studentId = "anonymous_" + System.currentTimeMillis();
                    log.warn("使用匿名studentId: {}", studentId);
                }
            }
            
            // 解析答案JSON字符串
            List<Object> answerList;
            try {
                answerList = JSON.parseArray(answers);
                log.info("解析到{}个答案", answerList.size());
            } catch (Exception e) {
                log.error("答案JSON解析失败", e);
                return Result.error("答案格式错误");
            }
            
            // 调用服务提交答案
            boolean success = appStudentsExamService.submitExamAnswers(courseId, paperId, studentId, answerList);
            
            if (success) {
                return Result.OK("考试提交成功");
            } else {
                return Result.error("考试提交失败");
            }
        } catch (Exception e) {
            log.error("提交考试答案失败", e);
            return Result.error("提交考试答案失败: " + e.getMessage());
        }
    }

    /**
     * 获取考题信息
     *
     * @param paperId 试卷ID
     * @param studentId 学生ID
     * @param courseId 课程ID
     * @return 考题信息
     */
    @AutoLog(value = "获取考题信息")
    @Operation(summary = "获取考题信息", description = "获取考题信息")
    @GetMapping(value = "/questions")
    public Result<?> getExamQuestions(
            @RequestParam(required = false) String paperId,
            @RequestParam(required = false) String studentId,
            @RequestParam(required = false) String courseId) {
        try {
            log.info("=== 获取考题信息请求开始 ===");
            log.info("接收到的参数 - paperId: {}, studentId: {}, courseId: {}", paperId, studentId, courseId);
            log.info("参数类型检查 - paperId类型: {}, studentId类型: {}, courseId类型: {}", 
                    paperId != null ? paperId.getClass().getSimpleName() : "null",
                    studentId != null ? studentId.getClass().getSimpleName() : "null",
                    courseId != null ? courseId.getClass().getSimpleName() : "null");
            
            // 参数验证
            if ((paperId == null || paperId.isEmpty()) && (courseId == null || courseId.isEmpty())) {
                String errorMsg = "参数错误，必须提供paperId或courseId";
                log.warn(errorMsg);
                return Result.error(errorMsg);
            }
            
            ExamInfoVO examInfo = null;
            
            // 优先通过paperId和studentId查询考题（这样可以加载已保存的答案）
            if (paperId != null && !paperId.isEmpty() && studentId != null && !studentId.isEmpty()) {
                log.info("通过paperId和studentId查询考题: paperId={}, studentId={}", paperId, studentId);
                try {
                    examInfo = appStudentsExamService.getExamQuestions(paperId, studentId);
                    log.info("通过paperId和studentId查询成功 - 标题: {}, 题目数量: {}, 总分: {}",
                            examInfo.getTitle(),
                            examInfo.getQuestions() != null ? examInfo.getQuestions().size() : 0,
                            examInfo.getTotalScore());
                } catch (Exception e) {
                    log.error("通过paperId和studentId查询考题失败", e);
                    // 如果查询失败，尝试其他方式
                }
            }
            
            // 如果paperId查询失败或没有提供paperId，尝试通过courseId查询（但不包含学生答案）
            if (examInfo == null && courseId != null && !courseId.isEmpty()) {
                log.info("通过courseId查询考题: {}", courseId);
                try {
                    examInfo = appStudentsExamService.getExamQuestionsByCourseIdRaw(courseId);
                    log.info("通过courseId查询成功 - 标题: {}, 题目数量: {}, 总分: {}",
                            examInfo.getTitle(),
                            examInfo.getQuestions() != null ? examInfo.getQuestions().size() : 0,
                            examInfo.getTotalScore());
                } catch (Exception e) {
                    log.error("通过courseId查询考题失败", e);
                    // 如果通过courseId查询失败，返回空结果
                    examInfo = new ExamInfoVO();
                    examInfo.setTitle("查询失败");
                    examInfo.setQuestions(new ArrayList<>());
                    examInfo.setTotalScore(0);
                }
            }
            
            // 如果两种方式都失败，返回错误
            if (examInfo == null) {
                String errorMsg = "未能通过提供的参数查询到考题";
                log.warn(errorMsg);
                return Result.error(errorMsg);
            }
            
            // 检查题目列表是否为空
            if (examInfo.getQuestions() == null || examInfo.getQuestions().isEmpty()) {
                log.warn("查询结果中题目列表为空");
                // 确保题目列表不为null
                if (examInfo.getQuestions() == null) {
                    examInfo.setQuestions(new ArrayList<>());
                }
            } else {
                log.info("返回{}个题目", examInfo.getQuestions().size());
                // 打印第一题信息用于调试
                if (!examInfo.getQuestions().isEmpty()) {
                    QuestionVO firstQuestion = examInfo.getQuestions().get(0);
                    log.info("第一题信息 - ID: {}, 标题: {}, 类型: {}, 选项数: {}",
                            firstQuestion.getId(),
                            firstQuestion.getTitle(),
                            firstQuestion.getType(),
                            firstQuestion.getOptions() != null ? firstQuestion.getOptions().size() : 0);
                }
            }
            
            return Result.OK(examInfo);
        } catch (Exception e) {
            log.error("获取考题信息失败", e);
            return Result.error("获取考题信息失败: " + e.getMessage());
        }
    }

    /**
     * 使用存储过程获取考题信息（便于MySQL端调试）
     *
     * @param paperId 试卷ID
     * @param studentId 学生ID
     * @param courseId 课程ID
     * @return 考题信息
     */
    @AutoLog(value = "存储过程获取考题信息")
    @Operation(summary = "存储过程获取考题信息", description = "直接调用存储过程，方便MySQL端调试")
    @GetMapping(value = "/questionsBySP")
    public Result<?> getExamQuestionsByStoredProcedure(
            @RequestParam(required = false) String paperId,
            @RequestParam(required = false) String studentId,
            @RequestParam(required = false) String courseId) {
        try {
            log.info("存储过程获取考题信息请求 - paperId: {}, studentId: {}, courseId: {}", paperId, studentId, courseId);
            // 调用存储过程
            String spName = null;
            Object[] params = null;
            if (paperId != null && studentId != null) {
                log.info("通过存储过程sp_get_exam_questions_by_paper查询考题");
                spName = "call sp_get_exam_questions_by_paper(?, ?)";
                params = new Object[]{paperId, studentId};
            } else if (courseId != null) {
                log.info("通过存储过程sp_get_exam_questions_by_course查询考题");
                spName = "call sp_get_exam_questions_by_course(?)";
                params = new Object[]{courseId};
            } else {
                log.warn("参数错误，必须提供(paperId和studentId)或courseId");
                return Result.error("参数错误，必须提供(paperId和studentId)或courseId");
            }
            // 这里假设存储过程会返回一个结果集，包含题目的所有信息
            List<Map<String, Object>> resultList = appStudentsExamService.executeStoredProcedure(spName, params);
            if (resultList != null && !resultList.isEmpty()) {
                log.info("存储过程查询成功，返回{}条结果", resultList.size());
                // 转换为前端需要的格式
                ExamInfoVO examInfo = new ExamInfoVO();
                String examTitle = "存储过程查询的考试";
                if (resultList.get(0).containsKey("exam_name")) {
                    examTitle = resultList.get(0).get("exam_name").toString();
                }
                examInfo.setTitle(examTitle);
                List<QuestionVO> questions = new ArrayList<>();
                for (Map<String, Object> row : resultList) {
                    QuestionVO vo = new QuestionVO();
                    vo.setId(row.get("id") != null ? row.get("id").toString() : null);
                    vo.setTitle(row.get("title") != null ? row.get("title").toString() : null);
                    vo.setType(row.get("type") != null ? row.get("type").toString() : null);
                    // 处理options字段
                    String optionsJson = row.get("options") != null ? row.get("options").toString() : null;
                    vo.setOptions(optionsJson != null ? JSON.parseArray(optionsJson) : null);
                    vo.setScore(row.get("score") != null ? new java.math.BigDecimal(row.get("score").toString()).intValue() : 0);
                    questions.add(vo);
                }
                examInfo.setQuestions(questions);
                examInfo.setTotalScore(questions.stream().mapToInt(QuestionVO::getScore).sum());
                return Result.OK(examInfo);
            } else {
                log.warn("存储过程查询无结果");
                ExamInfoVO emptyResult = new ExamInfoVO();
                emptyResult.setTitle("未知考试");
                emptyResult.setQuestions(new ArrayList<>());
                emptyResult.setTotalScore(0);
                return Result.OK(emptyResult);
            }
        } catch (Exception e) {
            log.error("调用存储过程获取考题信息失败", e);
            return Result.error("调用存储过程获取考题信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取我的考试列表
     *
     * @return
     */
    @AutoLog(value = "获取我的考试列表")
    @Operation(summary = "获取我的考试列表", description = "获取我的考试列表")
    @RequiresRoles(logical = org.apache.shiro.authz.annotation.Logical.OR, value = {"stu", "te"})  // 学生或教师都可以访问
    @GetMapping(value = "/myExams")
    public Result<?> getMyExams(HttpServletRequest request) {
        try {
            // 获取当前用户信息
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser == null) {
                log.warn("未获取到当前登录用户信息");
                return Result.error("用户未登录或登录已过期");
            }
            
            String currentUsername = sysUser.getUsername();
            log.info("获取用户[{}]的考试列表", currentUsername);
            
            // 基于当前用户获取考试列表
            List<AppStudentsMyExamsView> myExams = appStudentsMyExamsViewService.getMyExamsByUsername(currentUsername);
            log.info("用户[{}]共有{}个考试", currentUsername, myExams.size());
            
            // 调试：输出每个考试的详细信息
            for (int i = 0; i < myExams.size(); i++) {
                AppStudentsMyExamsView exam = myExams.get(i);
                log.info("考试[{}]: id={}, examId={}, examName={}, courseId={}, studentId={}, status={}", 
                        i, exam.getId(), exam.getExamId(), exam.getExamName(), 
                        exam.getCourseId(), exam.getStudentId(), exam.getStatus());
            }
            
            return Result.OK(myExams);
        } catch (Exception e) {
            log.error("获取我的考试列表失败", e);
            return Result.error("获取我的考试列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 开始考试
     * 
     * @param examId 考试ID
     * @return 考试信息，包含paperId
     */
    @AutoLog(value = "开始考试")
    @Operation(summary = "开始考试", description = "开始一场新的考试")
    @RequiresRoles("stu")  // 添加学生角色权限验证
    @PostMapping(value = "/startExam")
    public Result<?> startExam(@RequestParam(value = "examId", required = false) String examId, HttpServletRequest request) {
        log.info("Received start exam request. Headers: {}", getRequestHeaders(request));
        log.info("Request parameters: examId={}", examId);
        
        // 输出所有请求参数进行调试
        Map<String, String[]> parameterMap = request.getParameterMap();
        log.info("所有请求参数: {}", parameterMap);
        
        // 输出请求体信息
        log.info("Content-Type: {}", request.getContentType());
        log.info("Content-Length: {}", request.getContentLength());
        log.info("Method: {}", request.getMethod());
        
        if (examId == null || examId.trim().isEmpty()) {
            log.warn("Exam ID is empty or null");
            return Result.error("考试ID不能为空");
        }
        try {
            log.info("开始考试请求 - examId: {}", examId);
            
            // 调用服务层开始考试
            Map<String, Object> result = appStudentsExamService.startExam(examId);
            
            if (Boolean.TRUE.equals(result.get("success"))) {
                log.info("开始考试成功 - examId: {}, paperId: {}", examId, result.get("paperId"));
                return Result.OK("开始考试成功", result);
            } else {
                log.warn("开始考试失败 - examId: {}, message: {}", examId, result.get("message"));
                return Result.error(result.get("message").toString());
            }
        } catch (Exception e) {
            log.error("开始考试异常 - examId: " + examId, e);
            return Result.error("开始考试失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取请求头信息
     * @param request HttpServletRequest
     * @return 请求头信息Map
     */
    private Map<String, String> getRequestHeaders(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.put(headerName, request.getHeader(headerName));
        }
        return headers;
    }

    /**
     * 测试当前用户信息和代码版本
     *
     * @return
     */
    @AutoLog(value = "测试当前用户信息")
    @Operation(summary = "测试当前用户信息", description = "用于调试当前登录用户信息")
    @GetMapping(value = "/debug/currentUser")
    public Result<?> debugCurrentUser(HttpServletRequest request) {
        try {
            Map<String, Object> debugInfo = new HashMap<>();
            
            // 获取当前用户信息
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser != null) {
                debugInfo.put("currentUser", sysUser.getUsername());
                debugInfo.put("realname", sysUser.getRealname());
                debugInfo.put("userId", sysUser.getId());
            } else {
                debugInfo.put("currentUser", "未登录");
            }
            
            debugInfo.put("codeVersion", "v2.0-fixed");
            debugInfo.put("timestamp", System.currentTimeMillis());
            debugInfo.put("requestPath", request.getRequestURI());
            
            return Result.OK(debugInfo);
        } catch (Exception e) {
            log.error("调试当前用户信息失败", e);
            return Result.error("调试失败: " + e.getMessage());
        }
    }
    
    /**
     * 提交单题答案（自动保存）
     *
     * @param questionId 题目ID
     * @param answerContent 答案内容
     * @param paperId 试卷ID（可选）
     * @return
     */
    @AutoLog(value = "提交单题答案")
    @Operation(summary = "提交单题答案", description = "提交单个题目的答案（用于自动保存）")
    @PostMapping(value = "/submitSingleAnswer", consumes = {"application/x-www-form-urlencoded"})
    public Result<?> submitSingleAnswer(
            @RequestParam(required = false) String questionId,
            @RequestParam(required = false) String answerContent,
            @RequestParam(required = false) String paperId,
            HttpServletRequest request) {
        try {
            // 输出调试信息
            log.info("submitSingleAnswer API 被调用");
            log.info("请求参数: questionId={}, answerContent={}, paperId={}", questionId, answerContent, paperId);
            
            // 输出所有请求参数
            Map<String, String[]> parameterMap = request.getParameterMap();
            log.info("所有请求参数: {}", parameterMap);
            
            // 验证必需参数
            if (questionId == null || questionId.trim().isEmpty()) {
                return Result.error("缺少题目ID参数");
            }
            
            if (answerContent == null) {
                answerContent = ""; // 允许空答案
            }
            
            // 获取当前用户信息
            String username = JwtUtil.getUserNameByToken(request);
            if (username == null || username.isEmpty()) {
                return Result.error("用户未登录或登录已过期");
            }
            
            // 如果没有提供paperId，使用临时ID
            if (paperId == null || paperId.trim().isEmpty()) {
                paperId = "temp_" + System.currentTimeMillis();
                log.info("使用临时paperId: {}", paperId);
            }
            
            log.info("提交单题答案: username={}, paperId={}, questionId={}, answerContent={}", 
                    username, paperId, questionId, answerContent);
            
            // 调用服务方法
            java.util.Map<String, Object> result = appStudentsExamService.submitSingleAnswer(username, paperId, questionId, answerContent);
            
            if (Boolean.TRUE.equals(result.get("success"))) {
                return Result.OK("答案保存成功", result);
            } else {
                return Result.error(result.get("message").toString());
            }
        } catch (Exception e) {
            log.error("提交单题答案失败", e);
            return Result.error("提交答案失败: " + e.getMessage());
        }
    }
    
    /**
     * 完成考试
     *
     * @param paperId 试卷ID
     * @return
     */
    @AutoLog(value = "完成考试")
    @Operation(summary = "完成考试", description = "标记考试为已完成状态")
    @PostMapping(value = "/finishExam")
    public Result<?> finishExam(
            @RequestParam String paperId,
            HttpServletRequest request) {
        try {
            // 获取当前用户信息
            String username = JwtUtil.getUserNameByToken(request);
            if (username == null || username.isEmpty()) {
                return Result.error("用户未登录或登录已过期");
            }
            
            log.info("完成考试: username={}, paperId={}", username, paperId);
            
            // 调用服务方法
            java.util.Map<String, Object> result = appStudentsExamService.finishExam(username, paperId);
            
            if (Boolean.TRUE.equals(result.get("success"))) {
                return Result.OK("考试完成成功", result);
            } else {
                return Result.error(result.get("message").toString());
            }
        } catch (Exception e) {
            log.error("完成考试失败", e);
            return Result.error("完成考试失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取考试结果详情（包含正确答案和分析）
     */
    @AutoLog(value = "获取考试结果详情")
    @Operation(summary = "获取考试结果详情", description = "获取已提交考试的结果，包含正确答案和分析")
    @RequiresRoles("stu")
    @GetMapping(value = "/examResult")
    public Result<?> getExamResult(
            @RequestParam String paperId,
            HttpServletRequest request) {
        
        try {
            log.info("获取考试结果详情 - paperId: {}", paperId);
            
            // 从JWT获取用户名
            String username = JwtUtil.getUserNameByToken(request);
            if (username == null || username.isEmpty()) {
                log.error("无法获取用户名，JWT token可能无效");
                return Result.error("用户未登录或登录已过期");
            }
            log.info("当前用户: {}", username);
            
            // 调用服务获取考试结果详情
            java.util.Map<String, Object> result = appStudentsExamService.getExamResult(username, paperId);
            
            if (result != null && result.containsKey("success") && Boolean.TRUE.equals(result.get("success"))) {
                return Result.OK((String) result.get("message"), result.get("data"));
            } else {
                String errorMessage = result != null ? (String) result.get("message") : "获取考试结果失败";
                return Result.error(errorMessage);
            }
            
        } catch (Exception e) {
            log.error("获取考试结果详情失败", e);
            return Result.error("获取考试结果详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 调试接口：查看答案记录
     */
    @AutoLog(value = "调试查看答案记录")
    @Operation(summary = "调试查看答案记录", description = "用于调试的接口")
    @GetMapping(value = "/debug/answers")
    public Result<?> debugAnswers(
            @RequestParam String paperId,
            @RequestParam String questionId) {
        try {
            String sql = "SELECT * FROM app_student_answer WHERE paper_id = ? AND question_id = ?";
            List<Map<String, Object>> answers = jdbcTemplate.queryForList(sql, paperId, questionId);
            return Result.OK(answers);
        } catch (Exception e) {
            log.error("查看答案记录失败", e);
            return Result.error("查看答案记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 调试接口：查看选项答案记录
     */
    @AutoLog(value = "调试查看选项答案记录")
    @Operation(summary = "调试查看选项答案记录", description = "用于调试的接口")
    @GetMapping(value = "/debug/options")
    public Result<?> debugOptions(
            @RequestParam String paperId,
            @RequestParam String questionId) {
        try {
            String sql = "SELECT soa.*, qo.option_code, qo.option_content " +
                        "FROM app_student_option_answer soa " +
                        "JOIN app_student_answer sa ON soa.answer_id = sa.id " +
                        "JOIN app_question_option qo ON soa.option_id = qo.id " +
                        "WHERE sa.paper_id = ? AND sa.question_id = ?";
            List<Map<String, Object>> options = jdbcTemplate.queryForList(sql, paperId, questionId);
            return Result.OK(options);
        } catch (Exception e) {
            log.error("查看选项答案记录失败", e);
            return Result.error("查看选项答案记录失败: " + e.getMessage());
        }
    }

    /**
     * 调试接口：测试examId传递
     */
    @AutoLog(value = "调试examId传递")
    @Operation(summary = "调试examId传递", description = "用于调试examId参数传递")
    @PostMapping(value = "/debug/testExamId")
    public Result<?> debugTestExamId(@RequestParam String examId) {
        try {
            Map<String, Object> debugInfo = new HashMap<>();
            debugInfo.put("receivedExamId", examId);
            debugInfo.put("examIdType", examId != null ? examId.getClass().getSimpleName() : "null");
            debugInfo.put("examIdLength", examId != null ? examId.length() : 0);
            debugInfo.put("examIdIsEmpty", examId == null || examId.trim().isEmpty());
            
            // 检查考试是否存在
            String checkSql = "SELECT * FROM app_exam WHERE id = ?";
            List<Map<String, Object>> examResults = jdbcTemplate.queryForList(checkSql, examId);
            debugInfo.put("examExists", !examResults.isEmpty());
            if (!examResults.isEmpty()) {
                debugInfo.put("examInfo", examResults.get(0));
            }
            
            return Result.OK(debugInfo);
        } catch (Exception e) {
            log.error("调试examId传递失败", e);
            return Result.error("调试失败: " + e.getMessage());
        }
    }

    @AutoLog(value = "调试getExamQuestions参数")
    @Operation(summary = "调试getExamQuestions参数", description = "用于调试getExamQuestions方法的参数传递")
    @GetMapping(value = "/debug/getQuestions")
    public Result<?> debugGetExamQuestions(
            @RequestParam(required = false) String paperId,
            @RequestParam(required = false) String studentId,
            @RequestParam(required = false) String courseId) {
        
        Map<String, Object> debugInfo = new HashMap<>();
        debugInfo.put("paperId", paperId);
        debugInfo.put("studentId", studentId);
        debugInfo.put("courseId", courseId);
        
        log.info("调试getExamQuestions参数: paperId={}, studentId={}, courseId={}", paperId, studentId, courseId);
        
        // 检查数据库中的实际数据
        if (paperId != null) {
            try {
                // 查询试卷信息
                String paperSql = "SELECT id, exam_id, student_id, status FROM app_student_paper WHERE id = ?";
                List<Map<String, Object>> paperInfo = jdbcTemplate.queryForList(paperSql, paperId);
                debugInfo.put("paperInfo", paperInfo);
                
                // 查询答案信息
                String answerSql = "SELECT question_id, answer_content, student_id FROM app_student_answer WHERE paper_id = ?";
                List<Map<String, Object>> answerInfo = jdbcTemplate.queryForList(answerSql, paperId);
                debugInfo.put("answerInfo", answerInfo);
                
                log.info("试卷信息: {}", paperInfo);
                log.info("答案信息: {}", answerInfo);
                
            } catch (Exception e) {
                log.error("查询调试信息失败", e);
                debugInfo.put("error", e.getMessage());
            }
        }
        
        return Result.OK(debugInfo);
    }
}