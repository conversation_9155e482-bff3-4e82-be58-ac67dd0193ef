import { MockMethod } from 'vite-plugin-mock';

export default [
  // 获取教师统计数据
  {
    url: '/api/teacher/dashboard/stats',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        result: {
          courses: 8,
          students: 156,
          exams: 12,
          avgScore: 87.5,
          trends: {
            courses: 12,
            students: 8,
            exams: -5,
            avgScore: 15
          }
        }
      };
    }
  },

  // 获取教学数据分析
  {
    url: '/api/teacher/dashboard/teaching-data',
    method: 'get',
    response: ({ query }) => {
      const period = query.period || 'month';
      let labels = [];
      let examCounts = [];
      let avgScores = [];
      let participants = [];

      if (period === 'week') {
        labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        examCounts = [1, 0, 2, 1, 3, 0, 1];
        avgScores = [85, 0, 88, 82, 90, 0, 87];
        participants = [45, 0, 38, 42, 52, 0, 35];
      } else if (period === 'semester') {
        labels = ['第1月', '第2月', '第3月', '第4月', '第5月'];
        examCounts = [8, 10, 7, 12, 9];
        avgScores = [82, 85, 88, 86, 89];
        participants = [120, 135, 128, 145, 142];
      } else {
        labels = ['1月', '2月', '3月', '4月', '5月', '6月'];
        examCounts = [3, 4, 3, 5, 4, 6];
        avgScores = [82, 85, 88, 86, 89, 87];
        participants = [120, 135, 128, 145, 142, 156];
      }

      return {
        code: 200,
        message: 'success',
        result: {
          period,
          labels,
          examCounts,
          avgScores,
          participants
        }
      };
    }
  },

  // 获取最近考试
  {
    url: '/api/teacher/dashboard/recent-exams',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        result: [
          {
            id: '1',
            name: '高等数学期中考试',
            course: '高等数学',
            date: '2025-06-15',
            status: 'completed',
            participants: 45,
            needReview: true
          },
          {
            id: '2',
            name: '线性代数单元测试',
            course: '线性代数',
            date: '2025-06-18',
            status: 'ongoing',
            participants: 38,
            needReview: false
          },
          {
            id: '3',
            name: '概率统计作业',
            course: '概率统计',
            date: '2025-06-20',
            status: 'scheduled',
            participants: 42,
            needReview: false
          }
        ]
      };
    }
  },

  // 获取我的课程
  {
    url: '/api/teacher/dashboard/my-courses',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        result: [
          { id: '1', name: '高等数学', students: 45, progress: 75, color: '#1890ff' },
          { id: '2', name: '线性代数', students: 38, progress: 60, color: '#52c41a' },
          { id: '3', name: '概率统计', students: 42, progress: 85, color: '#faad14' }
        ]
      };
    }
  },

  // 获取系统通知
  {
    url: '/api/teacher/dashboard/notifications',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        result: [
          {
            id: '1',
            title: '新的学生提交了作业',
            time: '5分钟前',
            type: 'info',
            read: false
          },
          {
            id: '2',
            title: '考试时间提醒',
            time: '1小时前',
            type: 'warning',
            read: false
          },
          {
            id: '3',
            title: '系统维护通知',
            time: '2小时前',
            type: 'error',
            read: true
          }
        ]
      };
    }
  },

  // 获取待办事项
  {
    url: '/api/teacher/dashboard/todo-list',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        result: [
          { id: '1', title: '批改高等数学期中考试试卷', completed: false, dueDate: '今天', priority: 'high' },
          { id: '2', title: '准备下周线性代数课件', completed: false, dueDate: '明天', priority: 'medium' },
          { id: '3', title: '更新概率统计题库', completed: true, dueDate: '昨天', priority: 'low' },
          { id: '4', title: '参加教学研讨会', completed: false, dueDate: '6月20日', priority: 'medium' }
        ]
      };
    }
  },

  // 获取成绩分布
  {
    url: '/api/teacher/dashboard/score-distribution/:courseId',
    method: 'get',
    response: ({ query }) => {
      // 根据不同课程返回不同的成绩分布
      const distributions = {
        '1': [ // 高等数学
          { value: 8, name: '优秀(90-100)', color: '#52c41a' },
          { value: 15, name: '良好(80-89)', color: '#1890ff' },
          { value: 12, name: '中等(70-79)', color: '#faad14' },
          { value: 7, name: '及格(60-69)', color: '#ff7875' },
          { value: 3, name: '不及格(<60)', color: '#ff4d4f' }
        ],
        '2': [ // 线性代数
          { value: 6, name: '优秀(90-100)', color: '#52c41a' },
          { value: 12, name: '良好(80-89)', color: '#1890ff' },
          { value: 10, name: '中等(70-79)', color: '#faad14' },
          { value: 8, name: '及格(60-69)', color: '#ff7875' },
          { value: 2, name: '不及格(<60)', color: '#ff4d4f' }
        ],
        '3': [ // 概率统计
          { value: 10, name: '优秀(90-100)', color: '#52c41a' },
          { value: 18, name: '良好(80-89)', color: '#1890ff' },
          { value: 8, name: '中等(70-79)', color: '#faad14' },
          { value: 4, name: '及格(60-69)', color: '#ff7875' },
          { value: 2, name: '不及格(<60)', color: '#ff4d4f' }
        ]
      };

      return {
        code: 200,
        message: 'success',
        result: distributions[query.courseId] || distributions['1']
      };
    }
  }
] as MockMethod[]; 