package org.jeecg.modules.system.util;

import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TurnstileUtil {
    private static final String VERIFY_URL = "https://challenges.cloudflare.com/turnstile/v0/siteverify";

    public static boolean verifyTurnstileToken(String token, String secretKey, String remoteIp) {
        log.info("开始验证Turnstile Token，remoteIp={}", remoteIp);
        log.debug("Token长度: {}", token != null ? token.length() : 0);
        log.debug("SecretKey长度: {}", secretKey != null ? secretKey.length() : 0);
        
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            String body = "secret=" + secretKey + "&response=" + token + (remoteIp != null ? "&remoteip=" + remoteIp : "");
            HttpEntity<String> request = new HttpEntity<>(body, headers);
            log.info("发送Turnstile验证请求到: {}", VERIFY_URL);
            
            ResponseEntity<String> response = restTemplate.postForEntity(VERIFY_URL, request, String.class);
            log.info("Turnstile响应状态码: {}", response.getStatusCode());
            
            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject json = JSONObject.parseObject(response.getBody());
                log.info("Turnstile响应内容: {}", json);
                
                boolean success = json.getBooleanValue("success");
                if (!success) {
                    // 记录失败原因
                    String[] errorCodes = json.getObject("error-codes", String[].class);
                    if (errorCodes != null && errorCodes.length > 0) {
                        log.error("Turnstile验证失败，错误代码: {}", String.join(", ", errorCodes));
                    }
                }
                
                return success;
            } else {
                log.error("Turnstile请求失败，状态码: {}", response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Turnstile验证异常", e);
            e.printStackTrace();
        }
        return false;
    }
} 