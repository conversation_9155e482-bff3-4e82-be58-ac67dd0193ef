package org.jeecg.common.util;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.constant.enums.DySmsEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 自定义短信发送工具类
 * 集成第三方短信API服务
 * 
 * <AUTHOR>
 */
@Component
public class CustomSmsHelper {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomSmsHelper.class);
    
    private static String apiUrl;
    private static String username;
    private static String password;
    private static String sender;
    private static boolean enabled;
    
    @Value("${jeecg.sms-api.url}")
    public void setApiUrl(String apiUrl) {
        CustomSmsHelper.apiUrl = apiUrl;
    }
    
    @Value("${jeecg.sms-api.username}")
    public void setUsername(String username) {
        CustomSmsHelper.username = username;
    }
    
    @Value("${jeecg.sms-api.password}")
    public void setPassword(String password) {
        CustomSmsHelper.password = password;
    }
    
    @Value("${jeecg.sms-api.sender}")
    public void setSender(String sender) {
        CustomSmsHelper.sender = sender;
    }
    
    @Value("${jeecg.sms-api.enabled:false}")
    public void setEnabled(boolean enabled) {
        CustomSmsHelper.enabled = enabled;
    }
    
    /**
     * 发送短信
     *
     * @param phone 手机号
     * @param templateParamJson 模板参数，包含验证码等信息
     * @param dySmsEnum 短信模板枚举
     * @return 是否发送成功
     */
    public static boolean sendSms(String phone, JSONObject templateParamJson, DySmsEnum dySmsEnum) {
        if (!enabled) {
            logger.warn("自定义短信API未启用，请在配置文件中设置jeecg.sms-api.enabled=true");
            return false;
        }
        
        if (phone == null || templateParamJson == null) {
            logger.error("手机号或模板参数为空");
            return false;
        }
        
        try {
            // 获取验证码
            String code = templateParamJson.getString("code");
            if (code == null || code.isEmpty()) {
                logger.error("验证码不能为空");
                return false;
            }
            
            // 根据不同的短信模板类型，构建不同的短信内容
            String messageContent = buildMessageContent(code, dySmsEnum);
            
            // 构建API请求JSON
            Map<String, Object> messageMap = new HashMap<>();
            messageMap.put("to", formatPhoneNumber(phone));
            messageMap.put("message", messageContent);
            messageMap.put("sender", sender);
            messageMap.put("custom_ref", "jeecg_" + System.currentTimeMillis());
            
            // 打印请求内容以便调试
            logger.info("发送短信内容: " + messageContent);
            
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("messages", new Object[]{messageMap});
            
            String jsonInputString = JSONObject.toJSONString(requestMap);
            
            // 发送HTTP请求
            String credentials = Base64.getEncoder().encodeToString((username + ":" + password).getBytes());
            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Authorization", "Basic " + credentials);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setDoOutput(true);
            
            try (OutputStream os = connection.getOutputStream()) {
                os.write(jsonInputString.getBytes("utf-8"));
            }
            
            int responseCode = connection.getResponseCode();
            logger.info("短信API响应码: " + responseCode);
            
            if (responseCode >= 200 && responseCode < 300) {
                logger.info("短信发送成功，手机号: " + phone + ", 验证码: " + code);
                return true;
            } else {
                logger.error("短信发送失败，响应码: " + responseCode);
                return false;
            }
            
        } catch (Exception e) {
            logger.error("短信发送异常: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 根据不同的短信模板类型，构建不同的短信内容
     *
     * @param code 验证码
     * @param dySmsEnum 短信模板枚举
     * @return 短信内容
     */
    private static String buildMessageContent(String code, DySmsEnum dySmsEnum) {
        if (DySmsEnum.LOGIN_TEMPLATE_CODE.equals(dySmsEnum)) {
            return "您的登录验证码为：" + code + "，有效期5分钟，请勿泄露给他人。";
        } else if (DySmsEnum.REGISTER_TEMPLATE_CODE.equals(dySmsEnum)) {
            return "您的注册验证码为：" + code + "，有效期5分钟，请勿泄露给他人。";
        } else if (DySmsEnum.FORGET_PASSWORD_TEMPLATE_CODE.equals(dySmsEnum)) {
            return "您的密码重置验证码为：" + code + "，有效期5分钟，请勿泄露给他人。";
        } else if (DySmsEnum.CHANGE_PASSWORD_TEMPLATE_CODE.equals(dySmsEnum)) {
            return "您的密码修改验证码为：" + code + "，有效期5分钟，请勿泄露给他人。";
        } else {
            return "您的验证码为：" + code + "，有效期5分钟，请勿泄露给他人。";
        }
    }
    
    /**
     * 格式化手机号码
     * 保持手机号原样，不做额外处理
     *
     * @param phone 原始手机号
     * @return 格式化后的手机号
     */
    private static String formatPhoneNumber(String phone) {
        if (phone == null || phone.isEmpty()) {
            return phone;
        }
        
        // 直接返回原始手机号，不做额外处理
        // 避免验证码多一位数字的问题
        return phone;
    }
}
