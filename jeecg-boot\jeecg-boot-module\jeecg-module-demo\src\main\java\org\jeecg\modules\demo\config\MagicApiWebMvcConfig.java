package org.jeecg.modules.demo.config;

import org.jeecg.modules.demo.interceptor.MagicApiRateLimitInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Magic API代理Web MVC配置
 * 注册拦截器和其他Web相关配置
 */
@Configuration
public class MagicApiWebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private MagicApiRateLimitInterceptor magicApiRateLimitInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加限流拦截器，只拦截代理请求
        registry.addInterceptor(magicApiRateLimitInterceptor)
                .addPathPatterns("/appStudentsExam/teacher/**", "/appStudentsExam/student/**", "/demo/**");
    }
}
