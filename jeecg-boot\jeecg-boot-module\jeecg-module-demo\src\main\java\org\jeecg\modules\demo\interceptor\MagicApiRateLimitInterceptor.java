package org.jeecg.modules.demo.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import com.alibaba.fastjson.JSON;

/**
 * Magic API代理限流拦截器
 * 防止单个用户过度使用资源
 */
@Slf4j
@Component
public class MagicApiRateLimitInterceptor implements HandlerInterceptor {
    
    /** 限流Redis前缀 */
    private static final String RATE_LIMIT_PREFIX = "magic_api_rate_limit:";
    
    /** 限流时间段（秒） */
    private static final int LIMIT_PERIOD = 60; // 60秒内
    
    /** 限流次数 */
    private static final int LIMIT_COUNT = 100;  // 最多100次请求
    
    /** 需要限流的路径前缀 */
    private static final Set<String> RATE_LIMIT_PATHS = new HashSet<>(Arrays.asList(
        "/appStudentsExam/teacher/",
        "/appStudentsExam/student/",
        "/demo/"
    ));
    
    @Autowired
    private RedisUtil redisUtil;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String path = request.getRequestURI();
        
        // 检查是否需要对此路径进行限流
        boolean shouldRateLimit = false;
        for (String prefix : RATE_LIMIT_PATHS) {
            if (path.contains(prefix)) {
                shouldRateLimit = true;
                break;
            }
        }
        
        if (!shouldRateLimit) {
            return true; // 不需要限流的路径直接放行
        }
        
        // 获取用户标识
        String userKey = "anonymous";
        String token = request.getHeader("x-access-token");
        if (token != null && !token.isEmpty()) {
            try {
                userKey = JwtUtil.getUsername(token);
                if (userKey == null || userKey.isEmpty()) {
                    userKey = "anonymous";
                }
            } catch (Exception e) {
                log.warn("从token获取用户名失败: {}", e.getMessage());
            }
        }
        
        // 构造Redis键
        String rateKey = RATE_LIMIT_PREFIX + userKey + ":" + path;
        
        // 检查是否超过限制
        Object countObj = redisUtil.get(rateKey);
        if (countObj == null) {
            // 首次访问，设置计数器
            redisUtil.set(rateKey, 1, LIMIT_PERIOD);
        } else {
            int currentCount = Integer.parseInt(countObj.toString());
            if (currentCount >= LIMIT_COUNT) {
                // 超过限制，返回429错误
                log.warn("用户[{}]请求[{}]超过限流阈值: {} 次/{}秒", userKey, path, LIMIT_COUNT, LIMIT_PERIOD);
                sendRateLimitResponse(response);
                return false;
            }
            // 增加计数器
            redisUtil.incr(rateKey, 1);
        }
        
        return true;
    }
    
    /**
     * 发送限流响应
     * 
     * @param response HTTP响应
     * @throws IOException 如果写入响应时发生错误
     */
    private void sendRateLimitResponse(HttpServletResponse response) throws IOException {
        response.setStatus(429); // Too Many Requests
        response.setContentType("application/json;charset=UTF-8");
        
        Result<?> result = Result.error(429, "请求过于频繁，请稍后再试");
        
        try (PrintWriter writer = response.getWriter()) {
            writer.write(JSON.toJSONString(result));
            writer.flush();
        }
    }
}
