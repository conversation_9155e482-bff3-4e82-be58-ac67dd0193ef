# Magic API 演示项目

这是一个使用Magic API快速开发接口的演示项目。

## 项目特点

- 🚀 零代码快速开发HTTP API接口
- 💾 支持多种数据库（演示使用MySQL数据库）
- 🎯 在线可视化接口编辑器
- 🔄 热部署，实时生效
- 📊 内置SQL执行和数据库操作
- 🔍 支持在线调试和测试

## 前置要求

1. **MySQL数据库**: 确保MySQL服务已启动
2. **创建数据库**: 创建名为 `magic_api` 的数据库
   ```sql
   CREATE DATABASE magic_api CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

## 快速启动

### 1. 配置数据库连接
修改 `src/main/resources/application.yml` 中的数据库连接信息：
```yaml
spring:
  datasource:
    url: **********************************************************************************************************************************************
    username: root          # 修改为你的MySQL用户名
    password: 123456        # 修改为你的MySQL密码
```

### 2. 编译并启动
```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

### 3. 访问地址

- **Magic API 管理界面**: http://localhost:9999/magic/web
  - 用户名: `admin`
  - 密码: `123456`

## 管理界面密码修改

### 方法1: 在配置文件中修改
在 `application.yml` 中修改：
```yaml
magic-api:
  security:
    username: admin        # 登录用户名
    password: 新密码       # 修改这里的密码
```

### 方法2: 使用环境变量
```bash
# 启动时指定
java -jar target/magic-api-demo-1.0.0.jar --magic-api.security.password=新密码
```

### 方法3: 使用命令行参数
```bash
mvn spring-boot:run -Dspring-boot.run.arguments="--magic-api.security.password=新密码"
```

## 使用已有的Magic API数据库

如果你已经有之前的Magic API数据库，只需要：

1. **确保数据库中有 `magic_api_file` 表**：项目启动时会自动创建
2. **配置正确的数据库连接**：修改 `application.yml` 中的数据库连接信息
3. **配置使用数据库存储**：
   ```yaml
   magic-api:
     resource:
       type: database           # 使用数据库存储
       datasource: default      # 数据源名称
       table-name: magic_api_file  # 存储表名
   ```

启动后，你的旧接口数据将自动加载到管理界面中。

## 数据库配置说明

### MySQL连接配置
```yaml
spring:
  datasource:
    url: jdbc:mysql://主机:端口/数据库名?参数
    username: 用户名
    password: 密码
```

### 常用MySQL连接参数
- `useUnicode=true&characterEncoding=UTF-8`: 支持中文
- `serverTimezone=Asia/Shanghai`: 时区设置
- `useSSL=false`: 禁用SSL（开发环境）
- `allowMultiQueries=true`: 允许多语句查询

## 使用说明

### 1. 登录Magic API管理界面
访问 http://localhost:9999/magic/web，使用配置的账号密码登录

### 2. 创建接口
1. 点击"接口"菜单
2. 右键创建分组和接口
3. 编写接口脚本（支持自动提示）
4. 在线测试接口

### 3. 示例接口脚本

#### 查询所有用户
```javascript
return db.select('select * from users');
```

#### 根据ID查询用户
```javascript
var userId = request.getParameter('id');
return db.selectOne('select * from users where id = ?', userId);
```

#### 创建用户
```javascript
var username = request.getString('username');
var email = request.getString('email');
var age = request.getInt('age');

var result = db.update('insert into users(username, email, age) values(?, ?, ?)', 
                       username, email, age);
return {
    code: 0,
    message: '创建成功',
    data: result
};
```

#### 分页查询产品
```javascript
var page = request.getInt('page', 1);
var size = request.getInt('size', 10);

return db.page('select * from products', page, size);
```

### 4. 数据表结构

项目预置了两个测试表：

#### users 用户表
- id: 主键
- username: 用户名
- email: 邮箱
- age: 年龄
- created_time: 创建时间

#### products 产品表
- id: 主键
- name: 产品名称
- price: 价格
- description: 描述
- category: 分类
- created_time: 创建时间

#### magic_api_file 接口存储表
- id: 主键
- name: 文件名
- path: 文件路径
- content: 文件内容
- create_date: 创建时间
- update_date: 修改时间

## 配置说明

主要配置在 `application.yml` 中：

```yaml
magic-api:
  web: /magic/web           # 管理界面入口
  prefix: /                 # 接口路径前缀
  resource:
    type: database         # 存储类型：file 或 database
    datasource: default    # 数据源名称
    table-name: magic_api_file  # 存储表名
  security:
    username: admin        # 管理界面用户名
    password: 123456       # 管理界面密码
```

## 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库名、用户名、密码是否正确
- 检查防火墙设置

### 2. 找不到旧接口数据
- 确认配置了 `resource.type: database`
- 检查表名是否为 `magic_api_file`
- 确认数据库连接正确

### 3. 密码修改不生效
- 重启应用后生效
- 检查配置文件格式是否正确

## 功能特性

- ✅ 可视化接口编辑
- ✅ 代码自动提示
- ✅ 在线调试测试
- ✅ 历史版本管理
- ✅ 接口文档自动生成
- ✅ 支持数据库事务
- ✅ 支持文件上传下载
- ✅ 支持跨域访问
- ✅ 支持集群部署

## 更多信息

- 官方文档: https://ssssssss.org
- 在线演示: https://magic-api.ssssssss.org.cn
- GitHub: https://github.com/ssssssss-team/magic-api 