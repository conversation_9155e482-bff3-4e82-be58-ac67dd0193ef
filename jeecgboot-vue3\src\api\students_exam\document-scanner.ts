import { defHttp } from '/@/utils/http/axios';

enum Api {
  UploadDocument = '/teacher/document/upload',
  DocumentList = '/teacher/document/list',
  DeleteDocument = '/teacher/document/delete',
}

// 文档上传接口
export interface DocumentUploadResult {
  url: string;
  filename: string;
  size: number;
  type: string;
}

// 文档信息接口
export interface DocumentInfo {
  id: string;
  filename: string;
  originalName: string;
  url: string;
  size: number;
  type: string;
  uploadTime: string;
  teacherId: string;
  teacherName: string;
}

/**
 * 上传扫描的文档
 */
export const uploadDocument = (formData: FormData) => {
  return defHttp.uploadFile<DocumentUploadResult>(
    {
      url: Api.UploadDocument,
    },
    formData,
  );
};

/**
 * 获取文档列表
 */
export const getDocumentList = (params?: any) => {
  return defHttp.get<DocumentInfo[]>({ url: Api.DocumentList, params });
};

/**
 * 删除文档
 */
export const deleteDocument = (id: string) => {
  return defHttp.delete({ url: `${Api.DeleteDocument}/${id}` });
}; 