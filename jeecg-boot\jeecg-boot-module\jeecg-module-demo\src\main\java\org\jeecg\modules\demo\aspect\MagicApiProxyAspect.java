package org.jeecg.modules.demo.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.demo.annotation.MagicApiProxy;
import org.jeecg.modules.demo.service.MagicApiProxyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.util.UrlPathHelper;

import javax.servlet.http.HttpServletRequest;
import java.nio.file.Paths;

@Aspect
@Component
public class MagicApiProxyAspect {
    private static final Logger log = LoggerFactory.getLogger(MagicApiProxyAspect.class);

    @Autowired
    private MagicApiProxyService magicApiProxyService;

    @Around("@annotation(org.jeecg.modules.demo.annotation.MagicApiProxy)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        MagicApiProxy annotation = signature.getMethod().getAnnotation(MagicApiProxy.class);
        
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            log.error("MagicApiProxyAspect: No HTTP request context available for method {}.", signature.getName());
            return Result.error("No HTTP request context available");
        }
        HttpServletRequest request = attributes.getRequest();
        String httpMethodToUse = request.getMethod();
        String finalMagicApiPath;

        String annotationPathValue = annotation.value();

        if (annotationPathValue == null || annotationPathValue.isEmpty()) {
            // Case 1: Annotation value is empty, path comes from the first String method argument
            Object[] args = joinPoint.getArgs();
            String pathFromArg = null;
            for (Object arg : args) {
                if (arg instanceof String) {
                    pathFromArg = (String) arg;
                    break; 
                }
            }
            if (pathFromArg == null) {
                log.error("MagicApiProxyAspect: @MagicApiProxy has empty value, but no String argument found in method {} for path.", signature.getName());
                return Result.error("Proxy configuration error: Path argument missing in proxied method.");
            }
            finalMagicApiPath = pathFromArg;
            log.debug("MagicApiProxyAspect: Using path from method argument for {}: {}", signature.getName(), finalMagicApiPath);
        } else {
            // Case 2: Annotation value is a base path, append remaining request path
            String bestMatchingPattern = (String) request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
            String pathWithinApplication = new UrlPathHelper().getPathWithinApplication(request);

            if (bestMatchingPattern == null) {
                log.warn("MagicApiProxyAspect: HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE not found for request to {}. Using annotation value '{}' as full path.", pathWithinApplication, annotationPathValue);
                finalMagicApiPath = annotationPathValue; // Fallback: assume direct mapping if pattern is missing
            } else {
                String remainingPath = new AntPathMatcher().extractPathWithinPattern(bestMatchingPattern, pathWithinApplication);
                if (remainingPath == null) remainingPath = "";

                // Robustly join annotationPathValue (base) and remainingPath
                // e.g., base="/teacher", remaining="details/123" -> "/teacher/details/123"
                // e.g., base="/teacher", remaining="" -> "/teacher"
                String base = annotationPathValue;
                String[] relativeParts = remainingPath.split("/");
                
                // Normalize base path by removing trailing slash if it's not the root itself
                if (base.endsWith("/") && base.length() > 1) {
                    base = base.substring(0, base.length() - 1);
                }

                // Construct the path using java.nio.file.Paths for reliable joining and normalization
                // then replace backslashes if on Windows.
                // Note: Paths.get(base, relativeParts) might not behave as expected if base is empty or relativeParts are complex.
                // A simpler concatenation is often more direct for URL paths:
                if (relativeParts.length == 0 || (relativeParts.length == 1 && relativeParts[0].isEmpty())) {
                    finalMagicApiPath = base; // No actual relative parts
                } else {
                    StringBuilder combinedPath = new StringBuilder(base);
                    for (String part : relativeParts) {
                        if (!part.isEmpty()) {
                            if (combinedPath.length() == 0 || combinedPath.charAt(combinedPath.length() - 1) != '/') {
                                combinedPath.append('/');
                            }
                            combinedPath.append(part);
                        }
                    }
                    finalMagicApiPath = combinedPath.toString();
                    // Ensure if base was "/" and parts made it "//foo", it becomes "/foo"
                    if (finalMagicApiPath.startsWith("//")) {
                        finalMagicApiPath = finalMagicApiPath.substring(1);
                    }
                }
                 if (finalMagicApiPath.isEmpty() && !annotationPathValue.isEmpty() && (relativeParts.length == 0 || (relativeParts.length == 1 && relativeParts[0].isEmpty())) ){
                    finalMagicApiPath = annotationPathValue; // if base was /foo and relative was empty, result is /foo
                 }
            }
            log.debug("MagicApiProxyAspect: Using path from annotation value '{}' + remaining for {}: {}", annotationPathValue, signature.getName(), finalMagicApiPath);
        }
        
        if (finalMagicApiPath == null || finalMagicApiPath.isEmpty() && !"/".equals(finalMagicApiPath)) {
             log.error("MagicApiProxyAspect: Calculated finalMagicApiPath is empty or null for method {}. Original annotation value: '{}'", signature.getName(), annotation.value());
             return Result.error("Proxy configuration error: Resulting API path is empty.");
        }

        // Call the proxy service
        return magicApiProxyService.proxyRequest(
            finalMagicApiPath,
            httpMethodToUse,
            request
        );
    }
}
