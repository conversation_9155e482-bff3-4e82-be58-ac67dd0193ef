server:
  port: 9999

spring:
  application:
    name: magic-api-demo
  
  # 数据源配置 - 使用MySQL数据库
  datasource:
    url: ***********************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456
    # 添加数据源名称配置
    name: default
    
  # 初始化数据库脚本（可选，用于创建测试数据）
  sql:
    init:
      mode: always
      schema-locations: classpath:schema.sql
      data-locations: classpath:data.sql

# Magic API 配置
magic-api:
  # Web管理界面入口
  web: /magic/web
  
  # 接口路径前缀
  prefix: /
  
  # 资源存储配置 - 使用数据库存储（这样可以使用旧的API数据）
  resource:
    type: database
    # 不指定datasource，使用默认数据源
    table-name: magic_api_file
    readonly: false
  
  # 安全配置 - 管理界面登录账号密码
  security:
    username: admin
    password: 123456
  
  # 其他配置
  show-sql: true
  support-cross-domain: true
  throw-exception: false
  auto-import-module: db
  show-url: true
  banner: true
  
  # 调试配置
  debug:
    timeout: 60
    
  # 分页配置  
  page:
    size: 10
    page: 1 