{"mcpServers": {"mysql": {"command": "uvx", "args": ["--from", "mysql-mcp-server", "mysql_mcp_server"], "env": {"MYSQL_HOST": "localhost", "MYSQL_USER": "root", "MYSQL_PASSWORD": "123456", "MYSQL_DATABASE": "jeecg-boot"}, "disabled": false, "alwaysAllow": ["execute_sql"]}, "Context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "interactive-feedback-mcp": {"command": "uv", "args": ["--directory", "D:/temp/cursor_MCP/interactive-feedback-mcp", "run", "server.py"], "timeout": 600, "autoApprove": ["interactive_feedback"]}}}