<template>
  <div class="login-tabs mb-4">
    <div class="flex justify-center space-x-4">
      <Button 
        type="link" 
        :type="getLoginState === LoginStateEnum.LOGIN ? 'primary' : 'text'"
        @click="setLoginState(LoginStateEnum.LOGIN)"
      >
        {{ t('sys.login.signInFormTitle') }}
      </Button>
      <Button 
        type="link" 
        :type="getLoginState === LoginStateEnum.MOBILE ? 'primary' : 'text'"
        @click="setLoginState(LoginStateEnum.MOBILE)"
      >
        {{ t('sys.login.mobileSignInFormTitle') }}
      </Button>
      <Button 
        type="link" 
        :type="getLoginState === LoginStateEnum.EMAIL ? 'primary' : 'text'"
        @click="setLoginState(LoginStateEnum.EMAIL)"
      >
        {{ t('sys.login.emailSignInFormTitle') }}
      </Button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { Button } from 'ant-design-vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { useLoginState, LoginStateEnum } from './useLogin';

const { t } = useI18n();
const { setLoginState, getLoginState } = useLoginState();
</script>

<style lang="less" scoped>
.login-tabs {
  margin-bottom: 16px;
  
  .ant-btn {
    padding: 8px 16px;
    font-size: 16px;
    height: auto;
    
    &[type="primary"] {
      border-bottom: 2px solid var(--ant-primary-color);
      border-radius: 0;
    }
  }
}
</style>
