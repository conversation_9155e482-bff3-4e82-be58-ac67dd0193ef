package org.jeecg.modules.demo.app_students_exam.service;

import java.util.List;
import org.jeecg.modules.demo.app_students_exam.vo.ExamInfoVO;

/**
 * @Description: 学生考试服务接口
 * @Author: jeecg-boot
 * @Date: 2023-06-01
 * @Version: V1.0
 */
public interface IAppStudentsExamService {
    
    /**
     * 获取考试课程列表
     * 
     * @return 课程列表
     */
    List<?> getExamCourses();
    
    /**
     * 根据课程ID获取考题
     * 
     * @param courseId 课程ID
     * @return 考题信息
     */
    ExamInfoVO getQuestionsByCourseId(String courseId);
    
    /**
     * 提交考试答案
     * 
     * @param courseId 课程ID
     * @param answers 答案列表
     */
    void submitExam(String courseId, List<Object> answers);
    
    /**
     * 提交考试答案（完整参数版）
     * 
     * @param courseId 课程ID
     * @param paperId 试卷ID
     * @param studentId 学生ID
     * @param answers 答案列表
     * @return 是否提交成功
     */
    boolean submitExamAnswers(String courseId, String paperId, String studentId, List<Object> answers);
    
    /**
     * 根据试卷ID和学生ID获取考题
     *
     * @param paperId 试卷ID
     * @param studentId 学生ID
     * @return 考题信息
     */
    ExamInfoVO getExamQuestions(String paperId, String studentId);

    /**
     * 根据课程ID获取原始考题信息（不含学生作答信息）
     *
     * @param courseId 课程ID
     * @return 考题信息（原始）
     */
    ExamInfoVO getExamQuestionsByCourseIdRaw(String courseId);

    /**
     * 单题提交学生答案
     *
     * @param answer 答案内容
     */
    void submitStudentAnswer(java.util.Map<String, Object> answer);
    
    /**
     * 执行存储过程
     *
     * @param storedProcedureName 存储过程名称（包含call语句）
     * @param params 参数数组
     * @return 结果集列表
     */
    List<java.util.Map<String, Object>> executeStoredProcedure(String storedProcedureName, Object[] params);
    
    /**
     * 开始考试
     *
     * @param examId 考试ID
     * @return 包含paperId和状态信息的Map
     */
    java.util.Map<String, Object> startExam(String examId);
    
    /**
     * 提交单题答案（使用存储过程）
     *
     * @param studentUsername 学生用户名
     * @param paperId 试卷ID
     * @param questionId 题目ID  
     * @param answerContent 答案内容
     * @return 提交结果
     */
    java.util.Map<String, Object> submitSingleAnswer(String studentUsername, String paperId, String questionId, String answerContent);
    
    /**
     * 完成考试（使用存储过程）
     *
     * @param studentUsername 学生用户名
     * @param paperId 试卷ID
     * @return 完成结果
     */
    java.util.Map<String, Object> finishExam(String studentUsername, String paperId);
    
    /**
     * 获取考试结果详情（包含正确答案和分析）
     *
     * @param studentUsername 学生用户名
     * @param paperId 试卷ID
     * @return 考试结果详情
     */
    java.util.Map<String, Object> getExamResult(String studentUsername, String paperId);
}