import { defHttp } from '/@/utils/http/axios';

enum Api {
  TeacherStats = '/teacher/dashboard/stats',
  TeachingData = '/teacher/dashboard/teaching-data',
  RecentExams = '/teacher/dashboard/recent-exams',
  MyCourses = '/teacher/dashboard/my-courses',
  Notifications = '/teacher/dashboard/notifications',
  TodoList = '/teacher/dashboard/todo-list',
}

// 教师统计数据接口
export interface TeacherStatsData {
  courses: number;
  students: number;
  exams: number;
  avgScore: number;
  trends: {
    courses: number;
    students: number;
    exams: number;
    avgScore: number;
  };
}

// 教学数据接口
export interface TeachingData {
  period: string;
  examCounts: number[];
  avgScores: number[];
  participants: number[];
  labels: string[];
}

// 考试数据接口
export interface ExamData {
  id: string;
  name: string;
  course: string;
  date: string;
  status: 'completed' | 'ongoing' | 'scheduled';
  statusText: string;
  participants: number;
  needReview: boolean;
}

// 课程数据接口
export interface CourseData {
  id: string;
  name: string;
  students: number;
  progress: number;
  color: string;
}

// 通知数据接口
export interface NotificationData {
  id: string;
  title: string;
  time: string;
  type: 'info' | 'warning' | 'error';
  read: boolean;
}

// 待办事项接口
export interface TodoItem {
  id: string;
  title: string;
  completed: boolean;
  dueDate: string;
  priority: 'high' | 'medium' | 'low';
}

/**
 * 获取教师统计数据
 */
export const getTeacherStats = () => {
  return defHttp.get<TeacherStatsData>({ url: Api.TeacherStats });
};

/**
 * 获取教学数据分析
 */
export const getTeachingData = (params: { period: string; courseId?: string }) => {
  return defHttp.get<TeachingData>({ url: Api.TeachingData, params });
};

/**
 * 获取最近考试列表
 */
export const getRecentExams = (params?: { limit?: number }) => {
  return defHttp.get<ExamData[]>({ url: Api.RecentExams, params });
};

/**
 * 获取我的课程列表
 */
export const getMyCourses = () => {
  return defHttp.get<CourseData[]>({ url: Api.MyCourses });
};

/**
 * 获取系统通知
 */
export const getNotifications = (params?: { limit?: number; unreadOnly?: boolean }) => {
  return defHttp.get<NotificationData[]>({ url: Api.Notifications, params });
};

/**
 * 获取待办事项列表
 */
export const getTodoList = () => {
  return defHttp.get<TodoItem[]>({ url: Api.TodoList });
};

/**
 * 添加待办事项
 */
export const addTodoItem = (data: Omit<TodoItem, 'id'>) => {
  return defHttp.post<{ id: string }>({ url: Api.TodoList, data });
};

/**
 * 更新待办事项
 */
export const updateTodoItem = (id: string, data: Partial<TodoItem>) => {
  return defHttp.put<void>({ url: `${Api.TodoList}/${id}`, data });
};

/**
 * 删除待办事项
 */
export const deleteTodoItem = (id: string) => {
  return defHttp.delete<void>({ url: `${Api.TodoList}/${id}` });
};

/**
 * 标记通知为已读
 */
export const markNotificationRead = (id: string) => {
  return defHttp.put<void>({ url: `${Api.Notifications}/${id}/read` });
};

/**
 * 获取学生成绩分布数据
 */
export const getScoreDistribution = (courseId: string) => {
  return defHttp.get<{ name: string; value: number; color: string }[]>({
    url: `/teacher/dashboard/score-distribution/${courseId}`,
  });
}; 