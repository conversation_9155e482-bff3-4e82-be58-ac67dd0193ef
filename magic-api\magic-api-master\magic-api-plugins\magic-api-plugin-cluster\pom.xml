<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.ssssssss</groupId>
        <artifactId>magic-api-plugins</artifactId>
        <version>2.2.1</version>
    </parent>
    <artifactId>magic-api-plugin-cluster</artifactId>
    <packaging>jar</packaging>
    <name>magic-api-plugin-cluster</name>
    <description>magic-api-plugin-cluster</description>
    <dependencies>
        <dependency>
            <groupId>org.ssssssss</groupId>
            <artifactId>magic-api-plugin-redis</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>
