package org.jeecg.modules.demo.app_students_exam.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.app_students_exam.entity.AppStudentsMyExamsView;
import org.jeecg.modules.demo.app_students_exam.service.IAppStudentsMyExamsViewService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: app_students_my_exams_view
 * @Author: jeecg-boot
 * @Date:   2025-05-16
 * @Version: V1.0
 */
@Tag(name="app_students_my_exams_view")
@RestController
@RequestMapping("/app_students_exam/appStudentsMyExamsView")
@Slf4j
public class AppStudentsMyExamsViewController extends JeecgController<AppStudentsMyExamsView, IAppStudentsMyExamsViewService> {
	@Autowired
	private IAppStudentsMyExamsViewService appStudentsMyExamsViewService;
	
	/**
	 * 分页列表查询
	 *
	 * @param appStudentsMyExamsView
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "app_students_my_exams_view-分页列表查询")
	@Operation(summary="app_students_my_exams_view-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<AppStudentsMyExamsView>> queryPageList(AppStudentsMyExamsView appStudentsMyExamsView,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<AppStudentsMyExamsView> queryWrapper = QueryGenerator.initQueryWrapper(appStudentsMyExamsView, req.getParameterMap());
		Page<AppStudentsMyExamsView> page = new Page<AppStudentsMyExamsView>(pageNo, pageSize);
		IPage<AppStudentsMyExamsView> pageList = appStudentsMyExamsViewService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param appStudentsMyExamsView
	 * @return
	 */
	@AutoLog(value = "app_students_my_exams_view-添加")
	@Operation(summary="app_students_my_exams_view-添加")
	@RequiresPermissions("app_students_exam:app_students_my_exams_view:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody AppStudentsMyExamsView appStudentsMyExamsView) {
		appStudentsMyExamsViewService.save(appStudentsMyExamsView);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param appStudentsMyExamsView
	 * @return
	 */
	@AutoLog(value = "app_students_my_exams_view-编辑")
	@Operation(summary="app_students_my_exams_view-编辑")
	@RequiresPermissions("app_students_exam:app_students_my_exams_view:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody AppStudentsMyExamsView appStudentsMyExamsView) {
		appStudentsMyExamsViewService.updateById(appStudentsMyExamsView);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "app_students_my_exams_view-通过id删除")
	@Operation(summary="app_students_my_exams_view-通过id删除")
	@RequiresPermissions("app_students_exam:app_students_my_exams_view:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		appStudentsMyExamsViewService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "app_students_my_exams_view-批量删除")
	@Operation(summary="app_students_my_exams_view-批量删除")
	@RequiresPermissions("app_students_exam:app_students_my_exams_view:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.appStudentsMyExamsViewService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "app_students_my_exams_view-通过id查询")
	@Operation(summary="app_students_my_exams_view-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<AppStudentsMyExamsView> queryById(@RequestParam(name="id",required=true) String id) {
		AppStudentsMyExamsView appStudentsMyExamsView = appStudentsMyExamsViewService.getById(id);
		if(appStudentsMyExamsView==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(appStudentsMyExamsView);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param appStudentsMyExamsView
    */
    @RequiresPermissions("app_students_exam:app_students_my_exams_view:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AppStudentsMyExamsView appStudentsMyExamsView) {
        return super.exportXls(request, appStudentsMyExamsView, AppStudentsMyExamsView.class, "app_students_my_exams_view");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("app_students_exam:app_students_my_exams_view:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AppStudentsMyExamsView.class);
    }

}
