package org.jeecg.modules.demo.app_students_exam.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.demo.app_students_exam.entity.*;
import org.jeecg.modules.demo.app_students_exam.mapper.*;
import org.jeecg.modules.demo.app_students_exam.service.IAppStudentsExamService;
import org.jeecg.modules.demo.app_students_exam.vo.ExamInfoVO;
import org.jeecg.modules.demo.app_students_exam.vo.QuestionVO;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.SqlOutParameter;
// import org.springframework.jdbc.core.SqlParameter; // 不再需要
// import org.springframework.jdbc.core.namedparam.MapSqlParameterSource; // 不再需要
// import org.springframework.jdbc.core.simple.SimpleJdbcCall; // 不再需要
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.sql.Types;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 学生考试服务实现类
 * @Author: jeecg-boot
 * @Date: 2023-06-01
 * @Version: V1.0
 */
@Service
@Slf4j
public class AppStudentsExamServiceImpl implements IAppStudentsExamService {
    
    @Autowired
    private AppStudentsCourseMapper courseMapper;
    
    @Autowired
    private AppStudentsQuestionMapper questionMapper;
    
    @Autowired
    private AppStudentsExamRecordMapper examRecordMapper;

    @Autowired
    private AppStudentAnswerMapper appStudentAnswerMapper;
    
    @Autowired
    private AppStudentOptionAnswerMapper appStudentOptionAnswerMapper;
    
    @Autowired
    private org.springframework.jdbc.core.JdbcTemplate jdbcTemplate;

    @Autowired
    private ISysUserService sysUserService;
    
    /**
     * 安全地将Object转换为Integer，处理Long和Integer类型
     */
    private Integer safeToInteger(Object obj) {
        if (obj == null) {
            return null;
        } else if (obj instanceof Long) {
            return ((Long) obj).intValue();
        } else if (obj instanceof Integer) {
            return (Integer) obj;
        } else {
            try {
                return Integer.valueOf(obj.toString());
            } catch (NumberFormatException e) {
                log.warn("无法转换为Integer: {}", obj);
                return null;
            }
        }
    }

    /**
     * 查询指定课程的题目（直接SQL实现，返回ExamInfoVO，questions数组填充）
     */
    public ExamInfoVO getExamQuestionsByCourseIdRaw(String courseId) {
        log.info("开始通过courseId查询考题: {}", courseId);
        ExamInfoVO examInfo = new ExamInfoVO();
        
        if (courseId == null || courseId.isEmpty()) {
            log.error("courseId为空");
            examInfo.setTitle("参数错误");
            examInfo.setQuestions(new ArrayList<>());
            examInfo.setTotalScore(0);
            return examInfo;
        }
        
        try {
            // 查询课程信息
            log.info("查询课程信息: {}", courseId);
            AppStudentsCourse course = courseMapper.selectById(courseId);
            if (course == null) {
                log.error("课程不存在: {}", courseId);
                examInfo.setTitle("课程不存在");
                examInfo.setQuestions(new ArrayList<>());
                examInfo.setTotalScore(0);
                return examInfo;
            }
            
            log.info("找到课程: {}", course.getCourseName());
            examInfo.setTitle(course.getCourseName());
            
            // 查询课程对应的考试信息以获取考试时长
            try {
                String examSql = "SELECT duration FROM app_exam WHERE course_id = ? LIMIT 1";
                List<Map<String, Object>> examResults = jdbcTemplate.queryForList(examSql, courseId);
                if (!examResults.isEmpty() && examResults.get(0).get("duration") != null) {
                    Integer duration = Integer.valueOf(examResults.get(0).get("duration").toString());
                    examInfo.setDuration(duration);
                    log.info("从app_exam表获取考试时长: {}分钟", duration);
                } else {
                    examInfo.setDuration(90); // 默认90分钟
                    log.info("未找到考试时长，使用默认值: 90分钟");
                }
            } catch (Exception e) {
                log.warn("获取考试时长失败: {}", e.getMessage());
                examInfo.setDuration(90); // 默认90分钟
            }
            
            // 查询题目
            log.info("查询课程题目: {}", courseId);
            String sql = "SELECT * FROM app_question WHERE course_id = ? ORDER BY sort_no ASC";
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql, courseId);
            log.info("查询到{}个题目", rows.size());
            
            if (rows.isEmpty()) {
                log.warn("课程没有题目: {}", courseId);
                examInfo.setQuestions(new ArrayList<>());
                examInfo.setTotalScore(0);
                return examInfo;
            }

            List<QuestionVO> questions = new ArrayList<>();
            for (int i = 0; i < rows.size(); i++) {
                Map<String, Object> row = rows.get(i);
                
                QuestionVO vo = new QuestionVO();
                vo.setId(row.get("id") != null ? row.get("id").toString() : null);
                vo.setTitle(row.get("title") != null ? row.get("title").toString() : null);
                
                // 设置题目内容（question_content字段）
                String questionContent = row.get("question_content") != null ? row.get("question_content").toString() : null;
                if (questionContent != null && !questionContent.trim().isEmpty()) {
                    // 使用反射设置question_content字段，如果VO类有这个字段
                    try {
                        java.lang.reflect.Field field = vo.getClass().getDeclaredField("question_content");
                        field.setAccessible(true);
                        field.set(vo, questionContent);
                    } catch (NoSuchFieldException e) {
                        // 如果VO类没有question_content字段，设置到title中
                        if (vo.getTitle() == null || vo.getTitle().trim().isEmpty()) {
                            vo.setTitle(questionContent);
                        }
                    } catch (Exception e) {
                        log.warn("设置question_content字段失败", e);
                    }
                }
                
                // 处理题型
                String questionType = row.get("question_type") != null ? row.get("question_type").toString() : null;
                if (questionType != null) {
                    // 将数字题型转换为字符串题型
                    switch (questionType) {
                        case "1": vo.setType("radio"); break;
                        case "2": vo.setType("checkbox"); break;
                        case "3": vo.setType("judge"); break;
                        case "4": case "5": vo.setType("text"); break;
                        default: vo.setType("text");
                    }
                } else {
                    vo.setType("text");
                }
                
                // 查询选项
                String optionsSql = "SELECT option_content, option_code, is_correct FROM app_question_option WHERE question_id = ? ORDER BY sort_no ASC";
                List<Map<String, Object>> optionRows = jdbcTemplate.queryForList(optionsSql, vo.getId());
                
                if (!optionRows.isEmpty()) {
                    List<Object> options = new ArrayList<>();
                    for (Map<String, Object> optionRow : optionRows) {
                        Map<String, Object> option = new HashMap<>();
                        String optionContent = optionRow.get("option_content") != null ? optionRow.get("option_content").toString() : "";
                        String optionCode = optionRow.get("option_code") != null ? optionRow.get("option_code").toString() : "";
                        
                        // 如果option_code为空或null，使用option_content作为value
                        option.put("label", optionContent);
                        option.put("value", optionCode.isEmpty() || "None".equals(optionCode) ? optionContent : optionCode);
                        
                        if (optionRow.get("is_correct") != null) {
                            option.put("correct", optionRow.get("is_correct"));
                        }
                        options.add(option);
                    }
                    vo.setOptions(options);
                } else {
                    vo.setOptions(new ArrayList<>());
                }
                
                // 设置分值
                try {
                    vo.setScore(row.get("score") != null ? 
                            new java.math.BigDecimal(row.get("score").toString()).intValue() : 5);
                } catch (Exception e) {
                    log.error("解析分值异常", e);
                    vo.setScore(0);
                }
                
                questions.add(vo);
                log.debug("添加题目: id={}, type={}, 选项数={}", vo.getId(), vo.getType(), 
                        vo.getOptions() != null ? vo.getOptions().size() : 0);
            }
            
            examInfo.setQuestions(questions);
            examInfo.setTotalScore(questions.stream().mapToInt(QuestionVO::getScore).sum());
            
            log.info("成功获取考题信息: 标题={}, 题目数={}, 总分={}, 考试时长={}分钟", 
                    examInfo.getTitle(), questions.size(), examInfo.getTotalScore(), examInfo.getDuration());
            
        } catch (Exception e) {
            log.error("查询考题异常: courseId={}", courseId, e);
            examInfo.setTitle("查询异常: " + e.getMessage());
            examInfo.setQuestions(new ArrayList<>());
            examInfo.setTotalScore(0);
            examInfo.setDuration(90); // 默认时长
        }
        
        return examInfo;
    }
    
    @Override
    public List<?> getExamCourses() {
        // 查询可参加考试的课程列表
        QueryWrapper<AppStudentsCourse> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1); // 假设状态1为可用
        queryWrapper.orderByDesc("create_time");
        
        List<AppStudentsCourse> courseList = courseMapper.selectList(queryWrapper);
        // 统计每个课程的题目数量
        for (AppStudentsCourse course : courseList) {
            QueryWrapper<AppStudentsQuestion> qw = new QueryWrapper<>();
            qw.eq("course_id", course.getId());
            long count = questionMapper.selectCount(qw);
            try {
                // 反射设置questionCount字段（如果有）
                java.lang.reflect.Field field = course.getClass().getDeclaredField("questionCount");
                field.setAccessible(true);
                field.set(course, (int)count);
            } catch (Exception e) {
                // 如果没有该字段则忽略
            }
        }
        return courseList;
    }
    
    @Override
    public ExamInfoVO getQuestionsByCourseId(String courseId) {
        // 1. 查询课程信息
        AppStudentsCourse course = courseMapper.selectById(courseId);
        if (course == null) {
            throw new RuntimeException("课程不存在");
        }
        
        // 2. 查询课程的考题
        QueryWrapper<AppStudentsQuestion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("course_id", courseId);
        queryWrapper.orderByAsc("sort_no"); // 按排序号排序
        
        List<AppStudentsQuestion> questionList = questionMapper.selectList(queryWrapper);
        
        // 3. 转换为前端需要的格式
        List<QuestionVO> questions = new ArrayList<>();
        for (AppStudentsQuestion question : questionList) {
            QuestionVO vo = new QuestionVO();
            vo.setId(question.getId());
            vo.setTitle(question.getTitle());
            vo.setType(question.getType());
            vo.setOptions(question.getOptions()); // 假设已经是JSON格式，需要根据实际情况处理
            vo.setScore(question.getScore());
            questions.add(vo);
        }
        
        // 4. 构建返回对象
        ExamInfoVO examInfo = new ExamInfoVO();
        examInfo.setTitle(course.getCourseName());
        examInfo.setQuestions(questions);
        // examInfo.setDuration(course.getDuration()); // 若无duration字段可注释或删除
        examInfo.setTotalScore(questions.stream().mapToInt(QuestionVO::getScore).sum());
        
        return examInfo;
    }
    
    @Override
    @Transactional
    public void submitExam(String courseId, List<Object> answers) {
        // 获取当前登录用户
        HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
        String username = JwtUtil.getUserNameByToken(request);

        if (answers == null || answers.isEmpty()) {
            throw new IllegalArgumentException("答案列表不能为空");
        }

        Date now = new Date();
        for (Object obj : answers) {
            if (!(obj instanceof Map)) {
                throw new IllegalArgumentException("答案格式不正确");
            }
            Map<String, Object> answerMap = (Map<String, Object>) obj;

            // 校验必填字段
            String paperId = (String) answerMap.get("paperId");
            String questionId = (String) answerMap.get("questionId");
            String studentId = (String) answerMap.get("studentId");
            String answerContent = (String) answerMap.get("answerContent");
            if (paperId == null || questionId == null || studentId == null || answerContent == null) {
                throw new IllegalArgumentException("paperId, questionId, studentId, answerContent 不能为空");
            }

            AppStudentAnswer answer = new AppStudentAnswer();
            answer.setId(java.util.UUID.randomUUID().toString().replace("-", ""));
            answer.setPaperId(paperId);
            answer.setQuestionId(questionId);
            answer.setStudentId(studentId);
            answer.setAnswerContent(answerContent);

            // 可选字段
            answer.setIsCorrect((Integer) answerMap.getOrDefault("isCorrect", null));
            if (answerMap.get("score") != null) {
                answer.setScore(new java.math.BigDecimal(answerMap.get("score").toString()));
            }
            if (answerMap.get("maxScore") != null) {
                answer.setMaxScore(new java.math.BigDecimal(answerMap.get("maxScore").toString()));
            }
            answer.setMarkingType((Integer) answerMap.getOrDefault("markingType", 0));
            answer.setMarkingComment((String) answerMap.getOrDefault("markingComment", null));
            if (answerMap.get("markingTime") != null) {
                answer.setMarkingTime(DateUtils.str2Date(answerMap.get("markingTime").toString(), DateUtils.datetimeFormat.get()));
            }
            answer.setMarkingUser((String) answerMap.getOrDefault("markingUser", null));

            answer.setCreateBy(username);
            answer.setCreateTime(now);
            answer.setUpdateBy(username);
            answer.setUpdateTime(now);

            appStudentAnswerMapper.insert(answer);
        }
    }
    
    @Override
    @Transactional
    public boolean submitExamAnswers(String courseId, String paperId, String studentId, List<Object> answers) {
        log.info("提交考试答案 - courseId: {}, paperId: {}, studentId: {}, 答案数量: {}", 
                courseId, paperId, studentId, answers != null ? answers.size() : 0);
        
        // 获取当前登录用户
        HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
        String username = JwtUtil.getUserNameByToken(request);
        
        if (answers == null || answers.isEmpty()) {
            log.warn("答案列表为空");
            return false;
        }
        
        try {
            Date now = new Date();
            int successCount = 0;
            
            // 先删除该试卷的已有答案（如果有）
            QueryWrapper<AppStudentAnswer> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.eq("paper_id", paperId);
            deleteWrapper.eq("student_id", studentId);
            int deletedCount = appStudentAnswerMapper.delete(deleteWrapper);
            log.info("删除已有答案 {} 条", deletedCount);
            
            // 遍历答案数组，插入新的答案
            for (int i = 0; i < answers.size(); i++) {
                Object answerObj = answers.get(i);
                
                // 获取题目ID
                String questionId;
                try {
                    // 从考题列表中获取题目ID
                    QueryWrapper<AppStudentsQuestion> questionWrapper = new QueryWrapper<>();
                    questionWrapper.eq("course_id", courseId);
                    questionWrapper.orderByAsc("sort_no");
                    List<AppStudentsQuestion> questions = questionMapper.selectList(questionWrapper);
                    
                    if (i < questions.size()) {
                        questionId = questions.get(i).getId();
                    } else {
                        log.warn("答案索引 {} 超出题目数量 {}", i, questions.size());
                        continue;
                    }
                } catch (Exception e) {
                    log.error("获取题目ID失败", e);
                    continue;
                }
                
                // 创建答案对象
                AppStudentAnswer answer = new AppStudentAnswer();
                answer.setId(UUID.randomUUID().toString().replace("-", ""));
                answer.setPaperId(paperId);
                answer.setQuestionId(questionId);
                answer.setStudentId(studentId);
                
                // 处理答案内容
                String answerContent;
                if (answerObj == null) {
                    answerContent = "";
                } else if (answerObj instanceof List) {
                    // 多选题答案是数组
                    answerContent = JSON.toJSONString(answerObj);
                } else {
                    // 单选题、判断题、填空题答案是单值
                    answerContent = answerObj.toString();
                }
                answer.setAnswerContent(answerContent);
                
                // 设置其他字段
                answer.setCreateBy(username);
                answer.setCreateTime(now);
                answer.setUpdateBy(username);
                answer.setUpdateTime(now);
                
                // 插入答案
                appStudentAnswerMapper.insert(answer);
                successCount++;
            }
            
            log.info("成功提交 {} 条答案", successCount);
            
            // 更新考试记录状态为已提交
            try {
                String updateSql = "UPDATE app_student_paper SET status = 2, submit_time = ? WHERE id = ? AND student_id = ?";
                jdbcTemplate.update(updateSql, now, paperId, studentId);
                log.info("更新试卷状态为已提交");
            } catch (Exception e) {
                log.error("更新试卷状态失败", e);
            }
            
            return true;
        } catch (Exception e) {
            log.error("提交考试答案失败", e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public void submitStudentAnswer(java.util.Map<String, Object> answerMap) {
        // 获取当前登录用户
        javax.servlet.http.HttpServletRequest request = org.jeecg.common.util.SpringContextUtils.getHttpServletRequest();
        String username = org.jeecg.common.system.util.JwtUtil.getUserNameByToken(request);

        if (answerMap == null || answerMap.isEmpty()) {
            throw new IllegalArgumentException("答案内容不能为空");
        }

        java.util.Date now = new java.util.Date();

        // 校验必填字段
        String paperId = (String) answerMap.get("paper_id");
        String questionId = (String) answerMap.get("question_id");
        String studentId = (String) answerMap.get("student_id");
        Object answerContentObj = answerMap.get("answer_content");
        String answerContent = answerContentObj != null ? answerContentObj.toString() : null;
        if (paperId == null || questionId == null || studentId == null || answerContent == null) {
            throw new IllegalArgumentException("paper_id, question_id, student_id, answer_content 不能为空");
        }

        log.info("提交学生答案: paperId={}, questionId={}, studentId={}, answerContent={}", 
                paperId, questionId, studentId, answerContent);

        // 查询题目类型
        String questionTypeSql = "SELECT question_type FROM app_question WHERE id = ?";
        List<Map<String, Object>> questionTypeResults = jdbcTemplate.queryForList(questionTypeSql, questionId);
        Integer questionType = null;
        if (!questionTypeResults.isEmpty() && questionTypeResults.get(0).get("question_type") != null) {
            questionType = Integer.valueOf(questionTypeResults.get(0).get("question_type").toString());
        }
        
        log.info("题目类型: {}", questionType);

        // 检查是否已存在答案记录
        String checkSql = "SELECT id FROM app_student_answer WHERE paper_id = ? AND question_id = ? AND student_id = ?";
        List<Map<String, Object>> existingAnswers = jdbcTemplate.queryForList(checkSql, paperId, questionId, studentId);
        
        String answerId;
        if (!existingAnswers.isEmpty()) {
            // 更新已存在的答案
            answerId = existingAnswers.get(0).get("id").toString();
            log.info("更新已存在的答案记录: {}", answerId);
            
            String updateSql = "UPDATE app_student_answer SET answer_content = ?, update_time = ?, update_by = ? WHERE id = ?";
            jdbcTemplate.update(updateSql, answerContent, now, username, answerId);
        } else {
            // 创建新的答案记录
            answerId = java.util.UUID.randomUUID().toString().replace("-", "");
            log.info("创建新的答案记录: {}", answerId);
            
            AppStudentAnswer answer = new AppStudentAnswer();
            answer.setId(answerId);
            answer.setPaperId(paperId);
            answer.setQuestionId(questionId);
            answer.setStudentId(studentId);
            answer.setAnswerContent(answerContent);
            answer.setCreateBy(username);
            answer.setCreateTime(now);
            answer.setUpdateBy(username);
            answer.setUpdateTime(now);

            appStudentAnswerMapper.insert(answer);
        }

        // 如果是选择题（单选、多选、判断题），需要保存选项答案到app_student_option_answer表
        if (questionType != null && (questionType == 1 || questionType == 2 || questionType == 3)) {
            log.info("处理选择题选项答案，题目类型: {}", questionType);
            
            // 先删除该答案的所有选项记录
            String deleteOptionsSql = "DELETE FROM app_student_option_answer WHERE answer_id = ?";
            jdbcTemplate.update(deleteOptionsSql, answerId);
            log.info("删除原有选项答案记录");
            
            // 解析答案内容，获取选择的选项
            List<String> selectedOptions = new ArrayList<>();
            try {
                if (answerContent != null && !answerContent.trim().isEmpty()) {
                    if (answerContent.startsWith("[") && answerContent.endsWith("]")) {
                        // JSON数组格式（多选题）
                        List<String> options = JSON.parseArray(answerContent, String.class);
                        selectedOptions.addAll(options);
                    } else {
                        // 单个值（单选题、判断题）
                        selectedOptions.add(answerContent);
                    }
                }
            } catch (Exception e) {
                log.warn("解析答案内容失败: {}", e.getMessage());
                // 如果解析失败，当作单个值处理
                if (answerContent != null && !answerContent.trim().isEmpty()) {
                    selectedOptions.add(answerContent);
                }
            }
            
            log.info("解析到的选择选项: {}", selectedOptions);
            
            // 查询该题目的所有选项
            String optionsSql = "SELECT id, option_code, option_content FROM app_question_option WHERE question_id = ? ORDER BY sort_no";
            List<Map<String, Object>> optionResults = jdbcTemplate.queryForList(optionsSql, questionId);
            
            // 为每个选项创建记录
            for (Map<String, Object> optionRow : optionResults) {
                String optionId = optionRow.get("id").toString();
                String optionCode = optionRow.get("option_code") != null ? optionRow.get("option_code").toString() : "";
                String optionContent = optionRow.get("option_content") != null ? optionRow.get("option_content").toString() : "";
                
                // 判断该选项是否被选择
                // 支持多种匹配方式：option_code、option_content、option_id
                boolean isSelected = false;
                if (!selectedOptions.isEmpty()) {
                    for (String selectedOption : selectedOptions) {
                        if (selectedOption.equals(optionCode) || 
                            selectedOption.equals(optionContent) || 
                            selectedOption.equals(optionId) ||
                            ("None".equals(optionCode) && selectedOption.equals(optionContent))) {
                            isSelected = true;
                            break;
                        }
                    }
                }
                
                AppStudentOptionAnswer optionAnswer = new AppStudentOptionAnswer();
                optionAnswer.setId(java.util.UUID.randomUUID().toString().replace("-", ""));
                optionAnswer.setAnswerId(answerId);
                optionAnswer.setOptionId(optionId);
                optionAnswer.setIsSelected(isSelected ? 1 : 0);
                optionAnswer.setCreateBy(username);
                optionAnswer.setCreateTime(now);
                optionAnswer.setUpdateBy(username);
                optionAnswer.setUpdateTime(now);
                
                appStudentOptionAnswerMapper.insert(optionAnswer);
                
                log.info("保存选项答案: optionId={}, optionCode={}, optionContent={}, isSelected={}", 
                        optionId, optionCode, optionContent, isSelected);
            }
        }
        
        log.info("学生答案提交完成");
    }
    @Override
    public ExamInfoVO getExamQuestions(String paperId, String studentId) {
        if (paperId == null || studentId == null) {
            throw new IllegalArgumentException("paperId和studentId不能为空");
        }
        
        log.info("=== getExamQuestions方法开始 ===");
        log.info("获取考试题目和答案: paperId={}, studentId={}", paperId, studentId);
        log.info("参数类型检查 - paperId: [{}], studentId: [{}]", paperId, studentId);
        
        try {
            log.info("🚀 开始执行getExamQuestions，参数检查: paperId=[{}], studentId=[{}]", paperId, studentId);
            
            // 逐步恢复功能 - 第一步：测试基础数据库查询
            // 1.1 先查询student_paper获取exam_id
            String sqlGetExamId = "SELECT exam_id, student_id FROM app_student_paper WHERE id = ?";
            log.info("🔍 步骤1：查询paper信息...");
            log.info("SQL: {}", sqlGetExamId);
            log.info("参数: paperId={}", paperId);
            
            List<Map<String, Object>> paperResults = jdbcTemplate.queryForList(sqlGetExamId, paperId);
            log.info("✅ paper查询成功，返回{}条记录", paperResults.size());
            
            if (!paperResults.isEmpty()) {
                log.info("📋 paper记录内容: {}", paperResults.get(0));
            }
            
            if (paperResults.isEmpty()) {
                log.warn("⚠️ 未找到paperId: {}", paperId);
                ExamInfoVO examInfo = new ExamInfoVO();
                examInfo.setTitle("试卷不存在");
                examInfo.setQuestions(new ArrayList<>());
                examInfo.setTotalScore(0);
                return examInfo;
            }
            
            String examId = paperResults.get(0).get("exam_id").toString();
            log.info("📋 获取到examId: {}", examId);
            
            // 第二步：查询exam信息
            String sqlGetExam = "SELECT id, exam_name, course_id FROM app_exam WHERE id = ?";
            log.info("🔍 步骤2：查询exam信息...");
            List<Map<String, Object>> examResults = jdbcTemplate.queryForList(sqlGetExam, examId);
            
            if (examResults.isEmpty()) {
                log.warn("⚠️ 未找到examId: {}", examId);
                ExamInfoVO examInfo = new ExamInfoVO();
                examInfo.setTitle("考试不存在");
                examInfo.setQuestions(new ArrayList<>());
                examInfo.setTotalScore(0);
                return examInfo;
            }
            
            Map<String, Object> examInfo = examResults.get(0);
            String examName = examInfo.get("exam_name").toString();
            String courseId = examInfo.get("course_id").toString();
            log.info("📋 获取到exam信息: examName={}, courseId={}", examName, courseId);
            
            // 第三步：查询题目（先查询考试配置的题目，如果没有则查询课程的所有题目）
            String sqlFindQuestions = "SELECT q.id as question_id, q.question_content, q.question_type, " +
                                     "q.score, eq.sort_no " +
                                     "FROM app_exam_question eq " +
                                     "JOIN app_question q ON eq.course_id = q.course_id " +
                                     "WHERE eq.exam_id = ? " +
                                     "ORDER BY COALESCE(eq.sort_no, 0), q.id";
            
            log.info("🔍 步骤3：查询考试配置的题目...");
            List<Map<String, Object>> questionRows = jdbcTemplate.queryForList(sqlFindQuestions, examId);
            log.info("查询到{}个考试配置的题目", questionRows.size());
            
            // 如果考试没有配置具体题目，则查询该课程的所有题目
            if (questionRows.isEmpty()) {
                log.info("考试没有配置具体题目，查询课程{}的所有题目", courseId);
                String sqlFindCourseQuestions = "SELECT q.id as question_id, q.question_content, q.question_type, " +
                                               "q.score, q.sort_no " +
                                               "FROM app_question q " +
                                               "WHERE q.course_id = ? " +
                                               "ORDER BY COALESCE(q.sort_no, 0), q.id";
                
                questionRows = jdbcTemplate.queryForList(sqlFindCourseQuestions, courseId);
                log.info("从课程中查询到{}个题目", questionRows.size());
            }
            
            // 构建题目列表（暂时不加载选项和答案）
            List<QuestionVO> questions = new ArrayList<>();
            for (Map<String, Object> questionRow : questionRows) {
                QuestionVO vo = new QuestionVO();
                String questionId = questionRow.get("question_id").toString();
                
                vo.setId(questionId);
                vo.setTitle(questionRow.get("question_content") != null ? 
                           questionRow.get("question_content").toString() : "");
                
                // 处理题目类型
                Object questionTypeObj = questionRow.get("question_type");
                String questionType = "text"; // 默认类型
                if (questionTypeObj != null) {
                    int typeNum = Integer.parseInt(questionTypeObj.toString());
                    switch (typeNum) {
                        case 1: questionType = "radio"; break;
                        case 2: questionType = "checkbox"; break;
                        case 3: questionType = "judge"; break;
                        case 4: case 5: questionType = "text"; break;
                    }
                }
                vo.setType(questionType);
                
                // 安全地解析分数（处理decimal类型）
                Object scoreObj = questionRow.get("score");
                int score = 0;
                if (scoreObj != null) {
                    try {
                        // 处理可能的decimal格式（如11.00）
                        double scoreDouble = Double.parseDouble(scoreObj.toString());
                        score = (int) scoreDouble;
                        log.info("题目{}分数解析: {} -> {}", questionId, scoreObj, score);
                    } catch (NumberFormatException e) {
                        log.warn("题目{}分数解析失败: {}, 使用默认值0", questionId, scoreObj);
                        score = 0;
                    }
                }
                vo.setScore(score);
                
                questions.add(vo);
            }
            
            // 第四步：查询所有题目的选项
            log.info("🔍 步骤4：查询题目选项...");
            String sqlFindOptions = "SELECT question_id, option_content, option_code, sort_no " +
                                   "FROM app_question_option " +
                                   "WHERE question_id IN (" + 
                                   questionRows.stream().map(row -> "'" + row.get("question_id") + "'").collect(Collectors.joining(",")) + ") " +
                                   "ORDER BY question_id, sort_no";
            
            List<Map<String, Object>> optionRows = new ArrayList<>();
            if (!questionRows.isEmpty()) {
                optionRows = jdbcTemplate.queryForList(sqlFindOptions);
                log.info("查询到{}个选项", optionRows.size());
            }
            
            // 将选项按题目ID组织
            Map<String, List<Map<String, Object>>> optionsMap = new HashMap<>();
            for (Map<String, Object> optionRow : optionRows) {
                String questionId = optionRow.get("question_id").toString();
                optionsMap.computeIfAbsent(questionId, k -> new ArrayList<>()).add(optionRow);
            }
            
            // 第五步：查询学生已保存的答案
            log.info("🔍 步骤5：查询学生答案...");
            String sqlFindAnswers = "SELECT question_id, answer_content " +
                                   "FROM app_student_answer " +
                                   "WHERE paper_id = ? AND student_id = ?";
            
            List<Map<String, Object>> answerRows = jdbcTemplate.queryForList(sqlFindAnswers, paperId, studentId);
            log.info("查询到{}个已保存的答案", answerRows.size());
            
            // 将答案按题目ID组织成Map
            Map<String, String> answerMap = new HashMap<>();
            for (Map<String, Object> answerRow : answerRows) {
                String questionId = answerRow.get("question_id").toString();
                String answerContent = answerRow.get("answer_content") != null ? 
                                     answerRow.get("answer_content").toString() : "";
                answerMap.put(questionId, answerContent);
                log.info("加载答案: questionId={}, answerContent={}", questionId, answerContent);
            }
            
            // 第六步：为题目添加选项和答案
            log.info("🔍 步骤6：组装题目的选项和答案...");
            for (QuestionVO question : questions) {
                String questionId = question.getId();
                
                // 添加选项
                List<Map<String, Object>> questionOptions = optionsMap.get(questionId);
                if (questionOptions != null && !questionOptions.isEmpty()) {
                    List<Object> options = new ArrayList<>();
                    for (Map<String, Object> option : questionOptions) {
                        Map<String, Object> optionVO = new HashMap<>();
                        String optionContent = option.get("option_content") != null ? option.get("option_content").toString() : "";
                        String optionCode = option.get("option_code") != null ? option.get("option_code").toString() : "";
                        
                        optionVO.put("label", optionContent);
                        // 如果option_code为空或null，使用option_content作为value
                        optionVO.put("value", optionCode.isEmpty() || "None".equals(optionCode) ? optionContent : optionCode);
                        options.add(optionVO);
                        
                        log.info("题目{}选项: label={}, value={}", questionId, optionContent, optionVO.get("value"));
                    }
                    question.setOptions(options);
                    log.info("题目{}添加{}个选项", questionId, options.size());
                } else {
                    question.setOptions(new ArrayList<>());
                    log.info("题目{}没有选项", questionId);
                }
                
                // 添加学生答案
                String savedAnswer = answerMap.get(questionId);
                if (savedAnswer != null && !savedAnswer.trim().isEmpty()) {
                    question.setStudentAnswer(savedAnswer);
                    log.info("✅ 题目{}设置保存的答案: [{}]", questionId, savedAnswer);
                } else {
                    question.setStudentAnswer(null);
                    log.info("❌ 题目{}没有保存的答案", questionId);
                }
            }
            
            // 查询试卷开始时间和考试时长
            String sqlGetPaperInfo = "SELECT sp.start_time, e.duration " +
                                    "FROM app_student_paper sp " +
                                    "JOIN app_exam e ON sp.exam_id = e.id " +
                                    "WHERE sp.id = ?";
            
            List<Map<String, Object>> paperInfoResults = jdbcTemplate.queryForList(sqlGetPaperInfo, paperId);
            
            Object startTimeObj = null;
            Integer duration = 150; // 默认时长150分钟
            
            if (!paperInfoResults.isEmpty()) {
                Map<String, Object> paperInfo = paperInfoResults.get(0);
                startTimeObj = paperInfo.get("start_time");
                Object durationObj = paperInfo.get("duration");
                if (durationObj != null) {
                    duration = Integer.valueOf(durationObj.toString());
                }
                log.info("获取到试卷时间信息: start_time={}, duration={}分钟", startTimeObj, duration);
            }
            
            // 构建返回对象
            ExamInfoVO resultVO = new ExamInfoVO();
            resultVO.setTitle(examName);
            resultVO.setQuestions(questions);
            resultVO.setTotalScore(questions.stream().mapToInt(QuestionVO::getScore).sum());
            resultVO.setDuration(duration);
            
            // 如果有开始时间，设置到返回对象中
            if (startTimeObj != null) {
                // 处理不同类型的时间对象
                try {
                    java.lang.reflect.Field startTimeField = resultVO.getClass().getDeclaredField("startTime");
                    startTimeField.setAccessible(true);
                    
                    // 根据实际类型进行转换
                    if (startTimeObj instanceof java.time.LocalDateTime) {
                        // LocalDateTime转换为Timestamp
                        java.time.LocalDateTime localDateTime = (java.time.LocalDateTime) startTimeObj;
                        java.sql.Timestamp timestamp = java.sql.Timestamp.valueOf(localDateTime);
                        startTimeField.set(resultVO, timestamp);
                        log.info("设置开始时间到返回对象(LocalDateTime->Timestamp): {}", timestamp);
                    } else if (startTimeObj instanceof java.sql.Timestamp) {
                        // 直接使用Timestamp
                        startTimeField.set(resultVO, startTimeObj);
                        log.info("设置开始时间到返回对象(Timestamp): {}", startTimeObj);
                    } else {
                        // 尝试字符串解析
                        String timeStr = startTimeObj.toString();
                        java.sql.Timestamp timestamp = java.sql.Timestamp.valueOf(timeStr);
                        startTimeField.set(resultVO, timestamp);
                        log.info("设置开始时间到返回对象(String->Timestamp): {}", timestamp);
                    }
                } catch (NoSuchFieldException e) {
                    log.info("ExamInfoVO没有startTime字段，将通过其他方式传递开始时间");
                } catch (Exception e) {
                    log.warn("设置开始时间失败: {}", e.getMessage(), e);
                }
            }
            
            log.info("✅ 成功构建完整考试信息: 标题={}, 题目数={}, 总分={}, 已保存答案数={}", 
                    resultVO.getTitle(), questions.size(), resultVO.getTotalScore(), answerMap.size());
            
            return resultVO;
            
            /* 继续注释其他逻辑，逐步验证
            // 1. 先通过paperId查找对应的exam_id和course_id
            
            // 简化查询：分步查询，避免复杂的JOIN
            // 1.1 先查询student_paper获取exam_id
            String sqlGetExamId = "SELECT exam_id, student_id FROM app_student_paper WHERE id = ?";
            log.info("🔍 步骤1：查询paper信息...");
            log.info("SQL: {}", sqlGetExamId);
            log.info("参数: paperId={}", paperId);
            
            List<Map<String, Object>> paperResults;
            try {
                paperResults = jdbcTemplate.queryForList(sqlGetExamId, paperId);
                log.info("✅ paper查询成功，返回{}条记录", paperResults.size());
                
                if (!paperResults.isEmpty()) {
                    log.info("📋 paper记录内容: {}", paperResults.get(0));
                }
            } catch (Exception sqlException) {
                log.error("❌ paper查询失败", sqlException);
                log.error("SQL异常详情: {}", sqlException.getMessage());
                throw sqlException;
            }
            
            if (paperResults.isEmpty()) {
                log.warn("⚠️ 未找到paperId: {}", paperId);
                ExamInfoVO examInfo = new ExamInfoVO();
                examInfo.setTitle("试卷不存在");
                examInfo.setQuestions(new ArrayList<>());
                examInfo.setTotalScore(0);
                return examInfo;
            }
            
            String examId = paperResults.get(0).get("exam_id").toString();
            log.info("📋 获取到examId: {}", examId);
            
            // 1.2 查询exam信息
            String sqlGetExam = "SELECT id, exam_name, course_id FROM app_exam WHERE id = ?";
            log.info("🔍 步骤2：查询exam信息...");
            List<Map<String, Object>> examResults = jdbcTemplate.queryForList(sqlGetExam, examId);
            
            if (examResults.isEmpty()) {
                log.warn("⚠️ 未找到examId: {}", examId);
                ExamInfoVO examInfo = new ExamInfoVO();
                examInfo.setTitle("考试不存在");
                examInfo.setQuestions(new ArrayList<>());
                examInfo.setTotalScore(0);
                return examInfo;
            }
            
            Map<String, Object> examInfo = examResults.get(0);
            String examName = examInfo.get("exam_name").toString();
            String courseId = examInfo.get("course_id").toString();
            log.info("📋 获取到exam信息: examName={}, courseId={}", examName, courseId);
            
            // 1.3 查询course信息（尝试不同的表名）
            String courseName = "未知课程";
            try {
                String sqlGetCourse = "SELECT course_name FROM app_students_course WHERE id = ?";
                List<Map<String, Object>> courseResults = jdbcTemplate.queryForList(sqlGetCourse, courseId);
                if (!courseResults.isEmpty()) {
                    courseName = courseResults.get(0).get("course_name").toString();
                    log.info("✅ 从app_students_course获取课程名: {}", courseName);
                } else {
                    log.warn("⚠️ app_students_course中未找到courseId: {}", courseId);
                }
            } catch (Exception e1) {
                log.warn("⚠️ app_students_course表查询失败，尝试app_course表");
                try {
                    String sqlGetCourse2 = "SELECT course_name FROM app_course WHERE id = ?";
                    List<Map<String, Object>> courseResults2 = jdbcTemplate.queryForList(sqlGetCourse2, courseId);
                    if (!courseResults2.isEmpty()) {
                        courseName = courseResults2.get(0).get("course_name").toString();
                        log.info("✅ 从app_course获取课程名: {}", courseName);
                    }
                } catch (Exception e2) {
                    log.warn("⚠️ app_course表也查询失败，使用默认课程名");
                }
            }
            
            log.info("✅ 成功获取考试基本信息: examId={}, examName={}, courseId={}, courseName={}", examId, examName, courseId, courseName);
            
            // 2. 查询考试的题目（通过course_id关联）
            String sqlFindQuestions = "SELECT q.id as question_id, q.question_content, q.question_type, " +
                                     "q.score, eq.sort_no " +
                                     "FROM app_exam_question eq " +
                                     "JOIN app_question q ON eq.course_id = q.course_id " +
                                     "WHERE eq.exam_id = ? " +
                                     "ORDER BY COALESCE(eq.sort_no, 0), q.id";
            
            List<Map<String, Object>> questionRows = jdbcTemplate.queryForList(sqlFindQuestions, examId);
            log.info("查询到{}个考试题目", questionRows.size());
            
            // 3. 查询学生已保存的答案
            String sqlFindAnswers = "SELECT question_id, answer_content " +
                                   "FROM app_student_answer " +
                                   "WHERE paper_id = ? AND student_id = ?";
            
            log.info("查询学生答案SQL: {}, 参数: paperId={}, studentId={}", sqlFindAnswers, paperId, studentId);
            List<Map<String, Object>> answerRows = jdbcTemplate.queryForList(sqlFindAnswers, paperId, studentId);
            log.info("查询到{}个已保存的答案", answerRows.size());
            
            // 如果没有找到答案，尝试查询所有相关的答案记录进行调试
            if (answerRows.isEmpty()) {
                log.warn("未找到学生答案，尝试查询该paper_id的所有答案记录进行调试");
                String debugSql = "SELECT question_id, answer_content, student_id FROM app_student_answer WHERE paper_id = ?";
                List<Map<String, Object>> debugRows = jdbcTemplate.queryForList(debugSql, paperId);
                log.info("该paper_id下的所有答案记录: {}", debugRows);
                
                // 如果找到了答案但student_id不匹配，使用找到的student_id重新查询
                if (!debugRows.isEmpty()) {
                    String actualStudentId = debugRows.get(0).get("student_id").toString();
                    log.info("发现实际的student_id: {}, 重新查询答案", actualStudentId);
                    answerRows = jdbcTemplate.queryForList(sqlFindAnswers, paperId, actualStudentId);
                    log.info("使用实际student_id重新查询到{}个答案", answerRows.size());
                }
            }
            
            // 4. 将答案按题目ID组织成Map
            Map<String, String> answerMap = new HashMap<>();
            for (Map<String, Object> answerRow : answerRows) {
                String questionId = answerRow.get("question_id").toString();
                String answerContent = answerRow.get("answer_content") != null ? 
                                     answerRow.get("answer_content").toString() : "";
                answerMap.put(questionId, answerContent);
                log.info("加载答案: questionId={}, answerContent={}", questionId, answerContent);
            }
            
            // 5. 查询题目选项
            String sqlFindOptions = "SELECT question_id, option_content, option_code, sort_no " +
                                   "FROM app_question_option " +
                                   "WHERE question_id IN (" + 
                                   questionRows.stream().map(row -> "'" + row.get("question_id") + "'").collect(Collectors.joining(",")) + ") " +
                                   "ORDER BY question_id, sort_no";
            
            List<Map<String, Object>> optionRows = new ArrayList<>();
            if (!questionRows.isEmpty()) {
                optionRows = jdbcTemplate.queryForList(sqlFindOptions);
            }
            
            // 6. 将选项按题目ID组织
            Map<String, List<Map<String, Object>>> optionsMap = new HashMap<>();
            for (Map<String, Object> optionRow : optionRows) {
                String questionId = optionRow.get("question_id").toString();
                optionsMap.computeIfAbsent(questionId, k -> new ArrayList<>()).add(optionRow);
            }
            
            // 7. 构建题目列表
            List<QuestionVO> questions = new ArrayList<>();
            for (Map<String, Object> questionRow : questionRows) {
                QuestionVO vo = new QuestionVO();
                String questionId = questionRow.get("question_id").toString();
                
                vo.setId(questionId);
                vo.setTitle(questionRow.get("question_content") != null ? 
                           questionRow.get("question_content").toString() : "");
                
                // 处理题目类型
                Object questionTypeObj = questionRow.get("question_type");
                String questionType = "text"; // 默认类型
                if (questionTypeObj != null) {
                    int typeNum = Integer.parseInt(questionTypeObj.toString());
                    switch (typeNum) {
                        case 1: questionType = "radio"; break;
                        case 2: questionType = "checkbox"; break;
                        case 3: questionType = "judge"; break;
                        case 4: case 5: questionType = "text"; break;
                    }
                }
                vo.setType(questionType);
                
                // 处理选项
                List<Map<String, Object>> questionOptions = optionsMap.get(questionId);
                if (questionOptions != null && !questionOptions.isEmpty()) {
                    List<Object> options = new ArrayList<>();
                    for (Map<String, Object> option : questionOptions) {
                        Map<String, Object> optionVO = new HashMap<>();
                        String optionContent = option.get("option_content") != null ? option.get("option_content").toString() : "";
                        String optionCode = option.get("option_code") != null ? option.get("option_code").toString() : "";
                        
                        optionVO.put("label", optionContent);
                        // 如果option_code为空或null，使用option_content作为value
                        optionVO.put("value", optionCode.isEmpty() || "None".equals(optionCode) ? optionContent : optionCode);
                        options.add(optionVO);
                    }
                    vo.setOptions(options);
                }
                
                vo.setScore(questionRow.get("score") != null ? 
                           Integer.parseInt(questionRow.get("score").toString()) : 0);
                
                // 设置学生已保存的答案
                String savedAnswer = answerMap.get(questionId);
                log.info("处理题目{}的答案设置: answerMap中的值={}, 是否为null={}", 
                        questionId, savedAnswer, savedAnswer == null);
                
                if (savedAnswer != null && !savedAnswer.trim().isEmpty()) {
                    vo.setStudentAnswer(savedAnswer);
                    log.info("✅ 题目{}设置保存的答案: [{}]", questionId, savedAnswer);
                } else {
                    // 为了调试，即使没有答案也设置为null，确保字段存在
                    vo.setStudentAnswer(null);
                    log.info("❌ 题目{}没有保存的答案，设置为null", questionId);
                }
                
                // 额外调试信息：检查answerMap的完整内容
                if (answerMap.isEmpty()) {
                    log.warn("⚠️ answerMap为空，没有任何已保存的答案");
                } else {
                    log.info("📋 当前answerMap内容: {}", answerMap);
                }
                
                questions.add(vo);
            }
            
            // 7.5 答案加载完成，移除了强制测试代码，现在所有答案都从数据库正常加载
            
            // 8. 构建返回对象
            ExamInfoVO resultVO = new ExamInfoVO();
            resultVO.setTitle(examName + " - " + courseName);
            resultVO.setQuestions(questions);
            resultVO.setTotalScore(questions.stream().mapToInt(QuestionVO::getScore).sum());
            
            log.info("成功构建考试信息: 题目数={}, 总分={}, 已保存答案数={}", 
                    questions.size(), resultVO.getTotalScore(), answerMap.size());
            
            return resultVO;
            */
            
        } catch (Exception e) {
            log.error("❌❌❌ 获取考试题目和答案失败 ❌❌❌", e);
            log.error("异常类型: {}", e.getClass().getSimpleName());
            log.error("异常消息: {}", e.getMessage());
            log.error("异常堆栈: ", e);
            
            // 尝试详细分析异常原因
            if (e instanceof org.springframework.dao.DataAccessException) {
                log.error("🔥 这是数据库访问异常，可能是SQL语句错误或表不存在");
            } else if (e instanceof java.sql.SQLException) {
                log.error("🔥 这是SQL异常，检查SQL语句和数据库连接");
            } else {
                log.error("🔥 这是其他类型的异常");
            }
            
            ExamInfoVO examInfo = new ExamInfoVO();
            examInfo.setTitle("获取考试信息失败: " + e.getMessage());
            examInfo.setQuestions(new ArrayList<>());
            examInfo.setTotalScore(0);
            return examInfo;
        }
    }
    
    @Override
    public List<Map<String, Object>> executeStoredProcedure(String storedProcedureName, Object[] params) {
        log.info("执行存储过程: {}, 参数数量: {}", storedProcedureName, params != null ? params.length : 0);
        
        try {
            // 执行存储过程并获取结果
            List<Map<String, Object>> resultList = jdbcTemplate.queryForList(storedProcedureName, params);
            log.info("存储过程执行结果: {}条记录", resultList.size());
            return resultList;
        } catch (Exception e) {
            log.error("执行存储过程失败: " + storedProcedureName, e);
            throw new RuntimeException("执行存储过程失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public java.util.Map<String, Object> startExam(String examId) {
        java.util.Map<String, Object> result = new java.util.HashMap<>();
        
        try {
            log.info("startExam方法被调用，examId: {}", examId);
            
            // 验证examId参数
            if (examId == null || examId.trim().isEmpty()) {
                log.warn("examId参数为空");
                result.put("success", false);
                result.put("message", "考试ID不能为空");
                return result;
            }
            
            // 获取当前登录用户
            HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
            log.info("获取到HttpServletRequest: {}", request != null ? "成功" : "失败");
            
            String username = JwtUtil.getUserNameByToken(request);
            log.info("从token获取到的用户名: {}", username);
            
            if (username == null || username.isEmpty()) {
                log.warn("用户认证失败，username为空");
                result.put("success", false);
                result.put("message", "用户未登录或登录已过期");
                return result;
            }
            
            log.info("开始考试: username={}, examId={}", username, examId);
            
            // 调用存储过程 sp_start_exam_with_result
            String callSql = "CALL sp_start_exam_with_result(?, ?)";
            log.info("准备调用存储过程: {}, 参数: username={}, examId={}", callSql, username, examId);
            
            List<Map<String, Object>> spResults = jdbcTemplate.queryForList(callSql, username, examId);
            log.info("存储过程返回结果数量: {}", spResults.size());
            
            if (!spResults.isEmpty()) {
                Map<String, Object> spResult = spResults.get(0);
                log.info("存储过程返回的原始结果: {}", spResult);
                
                // 安全地转换result_code
                Integer resultCode = safeToInteger(spResult.get("result_code"));
                String message = (String) spResult.get("message");
                String paperId = (String) spResult.get("paper_id");
                
                log.info("存储过程执行结果: resultCode={}, message={}, paperId={}", resultCode, message, paperId);
                
                if (resultCode != null && resultCode == 0) {
                    result.put("success", true);
                    result.put("message", message);
                    result.put("resultCode", resultCode);
                    result.put("paperId", paperId);
                    log.info("开始考试成功: paperId={}", paperId);
                } else {
                    result.put("success", false);
                    result.put("message", message != null ? message : "开始考试失败");
                    result.put("resultCode", resultCode != null ? resultCode : -1);
                    log.warn("开始考试失败: resultCode={}, message={}", resultCode, message);
                }
            } else {
                result.put("success", false);
                result.put("message", "存储过程执行失败，未返回结果");
                result.put("resultCode", -1);
                log.error("存储过程未返回结果");
            }
            
        } catch (Exception e) {
            log.error("开始考试失败，异常类型: {}, 异常信息: {}", e.getClass().getSimpleName(), e.getMessage(), e);
            result.put("success", false);
            result.put("message", "开始考试失败: " + e.getMessage());
            result.put("resultCode", -1);
        }
        
        return result;
    }
    
    @Override
    @Transactional
    public java.util.Map<String, Object> submitSingleAnswer(String studentUsername, String paperId, String questionId, String answerContent) {
        java.util.Map<String, Object> result = new java.util.HashMap<>();
        
        try {
            log.info("提交单题答案: username={}, paperId={}, questionId={}, answerContent={}", 
                    studentUsername, paperId, questionId, answerContent);
            
            // 获取学生ID
            String studentIdSql = "SELECT s.id FROM sys_user u " +
                                 "INNER JOIN app_student s ON u.id = s.user_id " +
                                 "INNER JOIN sys_user_role ur ON u.id = ur.user_id " +
                                 "INNER JOIN sys_role r ON ur.role_id = r.id " +
                                 "WHERE u.username = ? AND r.role_code = 'stu' AND u.status = 1 AND s.status = 1 " +
                                 "LIMIT 1";
            
            List<Map<String, Object>> studentResults = jdbcTemplate.queryForList(studentIdSql, studentUsername);
            if (studentResults.isEmpty()) {
                result.put("success", false);
                result.put("message", "权限验证失败：非学生用户");
                result.put("resultCode", -2);
                return result;
            }
            
            String studentId = studentResults.get(0).get("id").toString();
            log.info("获取到学生ID: {}", studentId);
            
            // 验证试卷是否属于该学生
            String paperValidationSql = "SELECT sp.exam_id, sp.status, e.end_time " +
                                       "FROM app_student_paper sp " +
                                       "JOIN app_exam e ON sp.exam_id = e.id " +
                                       "WHERE sp.id = ? AND sp.student_id = ? " +
                                       "LIMIT 1";
            
            List<Map<String, Object>> paperResults = jdbcTemplate.queryForList(paperValidationSql, paperId, studentId);
            if (paperResults.isEmpty()) {
                result.put("success", false);
                result.put("message", "试卷不存在或不属于该学生");
                result.put("resultCode", -3);
                return result;
            }
            
            Map<String, Object> paperInfo = paperResults.get(0);
            String examId = paperInfo.get("exam_id").toString();
            Integer paperStatus = Integer.valueOf(paperInfo.get("status").toString());
            
            if (paperStatus != 0) {
                result.put("success", false);
                result.put("message", "只能为进行中的考试提交答案");
                result.put("resultCode", -5);
                return result;
            }
            
            // 验证题目是否属于当前考试（支持两种方式：考试配置的题目 或 课程的题目）
            // 首先查询考试是否有配置的题目
            String questionValidationSql1 = "SELECT COUNT(*) as count FROM app_exam_question eq " +
                                           "INNER JOIN app_question q ON eq.course_id = q.course_id " +
                                           "WHERE eq.exam_id = ? AND q.id = ?";
            
            List<Map<String, Object>> questionResults1 = jdbcTemplate.queryForList(questionValidationSql1, examId, questionId);
            Integer questionCount1 = Integer.valueOf(questionResults1.get(0).get("count").toString());
            
            log.info("题目验证结果1（考试配置题目）: examId={}, questionId={}, count={}", examId, questionId, questionCount1);
            
            // 如果考试没有配置题目，则验证该题目是否属于考试的课程
            if (questionCount1 == 0) {
                log.info("考试没有配置题目，验证题目是否属于考试的课程");
                
                // 获取考试的course_id
                String getExamCourseSql = "SELECT course_id FROM app_exam WHERE id = ?";
                List<Map<String, Object>> examCourseResults = jdbcTemplate.queryForList(getExamCourseSql, examId);
                
                if (examCourseResults.isEmpty()) {
                    result.put("success", false);
                    result.put("message", "考试不存在");
                    result.put("resultCode", -4);
                    return result;
                }
                
                String examCourseId = examCourseResults.get(0).get("course_id").toString();
                
                // 验证题目是否属于该课程
                String questionValidationSql2 = "SELECT COUNT(*) as count FROM app_question " +
                                               "WHERE course_id = ? AND id = ?";
                
                List<Map<String, Object>> questionResults2 = jdbcTemplate.queryForList(questionValidationSql2, examCourseId, questionId);
                Integer questionCount2 = Integer.valueOf(questionResults2.get(0).get("count").toString());
                
                log.info("题目验证结果2（课程题目）: courseId={}, questionId={}, count={}", examCourseId, questionId, questionCount2);
                
                if (questionCount2 == 0) {
                    result.put("success", false);
                    result.put("message", "题目不存在于当前考试的课程中");
                    result.put("resultCode", -4);
                    return result;
                }
            }
            
            // 调用已有的submitStudentAnswer方法来处理答案保存（包括选择题选项）
            java.util.Map<String, Object> answerMap = new java.util.HashMap<>();
            answerMap.put("paper_id", paperId);
            answerMap.put("question_id", questionId);
            answerMap.put("student_id", studentId);
            answerMap.put("answer_content", answerContent);
            
            // 调用已有的方法，它会处理选择题的选项保存
            submitStudentAnswer(answerMap);
            
            result.put("success", true);
            result.put("message", "答案提交成功");
            result.put("resultCode", 0);
            
            log.info("单题答案提交成功: paperId={}, questionId={}", paperId, questionId);
            
        } catch (Exception e) {
            log.error("提交单题答案失败", e);
            result.put("success", false);
            result.put("message", "提交答案失败: " + e.getMessage());
            result.put("resultCode", -1);
        }
        
        return result;
    }
    
    @Override
    @Transactional
    public java.util.Map<String, Object> finishExam(String studentUsername, String paperId) {
        java.util.Map<String, Object> result = new java.util.HashMap<>();
        
        try {
            log.info("调用存储过程完成考试（带自动评分）: username={}, paperId={}", studentUsername, paperId);
            
            // 调用带自动评分功能的存储过程 sp_finish_exam_with_scoring
            String callSql = "CALL sp_finish_exam_with_scoring(?, ?)";
            List<Map<String, Object>> spResults = jdbcTemplate.queryForList(callSql, studentUsername, paperId);
            
            if (!spResults.isEmpty()) {
                Map<String, Object> spResult = spResults.get(0);
                // 安全地转换result_code
                Integer resultCode = safeToInteger(spResult.get("result_code"));
                String message = (String) spResult.get("message");
                // 获取总分（如果有）
                Object totalScoreObj = spResult.get("total_score");
                
                result.put("success", resultCode != null && resultCode == 0);
                result.put("message", message != null ? message : "未知错误");
                result.put("resultCode", resultCode != null ? resultCode : -1);
                
                if (totalScoreObj != null) {
                    result.put("totalScore", totalScoreObj);
                    log.info("考试完成并自动评分，总分: {}", totalScoreObj);
                }
                
                log.info("完成考试结果: success={}, message={}, resultCode={}, totalScore={}", 
                        result.get("success"), message, resultCode, totalScoreObj);
            } else {
                result.put("success", false);
                result.put("message", "存储过程执行失败，未返回结果");
                result.put("resultCode", -1);
                log.error("存储过程未返回结果");
            }
            
        } catch (Exception e) {
            log.error("完成考试失败", e);
            result.put("success", false);
            result.put("message", "完成考试失败: " + e.getMessage());
            result.put("resultCode", -1);
        }
        
        return result;
    }
        
    @Override
    @Transactional(readOnly = true)
    public java.util.Map<String, Object> getExamResult(String studentUsername, String paperId) {
        java.util.Map<String, Object> result = new java.util.HashMap<>();
        
        try {
            log.info("获取考试结果详情: username={}, paperId={}", studentUsername, paperId);
            
            // 验证学生用户
            String studentIdSql = "SELECT s.id FROM sys_user u " +
                                 "INNER JOIN app_student s ON u.id = s.user_id " +
                                 "INNER JOIN sys_user_role ur ON u.id = ur.user_id " +
                                 "INNER JOIN sys_role r ON ur.role_id = r.id " +
                                 "WHERE u.username = ? AND r.role_code = 'stu' AND u.status = 1 AND s.status = 1 " +
                                 "LIMIT 1";
            
            List<Map<String, Object>> studentResults = jdbcTemplate.queryForList(studentIdSql, studentUsername);
            if (studentResults.isEmpty()) {
                result.put("success", false);
                result.put("message", "权限验证失败：非学生用户");
                return result;
            }
            
            String studentId = studentResults.get(0).get("id").toString();
            
            // 验证试卷是否属于该学生
            String paperValidationSql = "SELECT sp.exam_id, sp.status, sp.total_score, sp.submit_time, e.exam_name " +
                                       "FROM app_student_paper sp " +
                                       "JOIN app_exam e ON sp.exam_id = e.id " +
                                       "WHERE sp.id = ? AND sp.student_id = ? " +
                                       "LIMIT 1";
            
            List<Map<String, Object>> paperResults = jdbcTemplate.queryForList(paperValidationSql, paperId, studentId);
            if (paperResults.isEmpty()) {
                result.put("success", false);
                result.put("message", "试卷不存在或不属于该学生");
                return result;
            }
            
            Map<String, Object> paperInfo = paperResults.get(0);
            String examId = paperInfo.get("exam_id").toString();
            Integer paperStatus = Integer.valueOf(paperInfo.get("status").toString());
            
            // 检查考试状态：必须是已提交状态才能查看结果
            if (paperStatus == 0) {
                result.put("success", false);
                result.put("message", "考试尚未提交，无法查看结果");
                return result;
            }
            
            // 获取试卷基本信息
            java.util.Map<String, Object> examInfo = new java.util.HashMap<>();
            examInfo.put("examName", paperInfo.get("exam_name"));
            examInfo.put("status", paperStatus);
            examInfo.put("totalScore", paperInfo.get("total_score"));
            examInfo.put("submitTime", paperInfo.get("submit_time"));
            
            // 获取题目和答案详情
            String questionSql = "SELECT " +
                    "sa.question_id, " +
                    "sa.answer_content as student_answer, " +
                    "sa.score, " +
                    "sa.max_score, " +
                    "sa.is_correct, " +
                    "sa.marking_comment as teacher_comment, " +
                    "sa.marking_time as marking_time, " +
                    "sa.marking_user as marking_user, " +
                    "q.question_content, " +
                    "q.question_answer as correct_answer, " +
                    "q.question_analysis, " +
                    "q.question_type, " +
                    "q.score as question_score " +
                    "FROM app_student_answer sa " +
                    "JOIN app_question q ON sa.question_id = q.id " +
                    "WHERE sa.paper_id = ? " +
                    "ORDER BY sa.create_time";
            
            List<Map<String, Object>> questionResults = jdbcTemplate.queryForList(questionSql, paperId);
            
            // 获取题目选项（如果是选择题）
            List<java.util.Map<String, Object>> questionsWithOptions = new ArrayList<>();
            for (Map<String, Object> questionResult : questionResults) {
                java.util.Map<String, Object> questionData = new java.util.HashMap<>();
                questionData.putAll(questionResult);
                
                String questionId = questionResult.get("question_id").toString();
                Integer questionType = Integer.valueOf(questionResult.get("question_type").toString());
                
                // 如果是选择题，获取选项
                if (questionType >= 1 && questionType <= 3) {
                    String optionSql = "SELECT option_code, option_content, is_correct " +
                                      "FROM app_question_option " +
                                      "WHERE question_id = ? " +
                                      "ORDER BY sort_no";
                    
                    List<Map<String, Object>> options = jdbcTemplate.queryForList(optionSql, questionId);
                    questionData.put("options", options);
                }
                
                questionsWithOptions.add(questionData);
            }
            
            // 构建返回数据
            java.util.Map<String, Object> data = new java.util.HashMap<>();
            data.put("examInfo", examInfo);
            data.put("questions", questionsWithOptions);
            
            result.put("success", true);
            result.put("message", "获取考试结果成功");
            result.put("data", data);
            
            log.info("成功获取考试结果详情，题目数量: {}", questionsWithOptions.size());
            
        } catch (Exception e) {
            log.error("获取考试结果详情失败", e);
            result.put("success", false);
            result.put("message", "获取考试结果详情失败: " + e.getMessage());
        }
        
        return result;
    }
        
}