/**
 * Minified by jsDelivr using Terser v5.39.0.
 * Original file: /gh/<PERSON><PERSON><PERSON><PERSON>/jscanify@master/src/jscanify.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
/*! jscanify v1.4.0 | (c) <PERSON><PERSON><PERSON><PERSON> and other contributors | MIT License */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.jscanify=t()}(this,(function(){"use strict";return class{constructor(){}findPaperContour(e){const t=new cv.Mat;cv.Canny(e,t,50,200);const o=new cv.Mat;cv.<PERSON><PERSON>lur(t,o,new cv.Size(3,3),0,0,cv.BORDER_DEFAULT);const n=new cv.Mat;cv.threshold(o,n,0,255,cv.THRESH_OTSU);let r=new cv.MatVector,c=new cv.Mat;cv.findContours(n,r,c,cv.RETR_CCOMP,cv.CHAIN_APPROX_SIMPLE);let i=0,a=-1;for(let e=0;e<r.size();++e){let t=cv.contourArea(r.get(e));t>i&&(i=t,a=e)}const s=a>=0?r.get(a):null;return t.delete(),o.delete(),n.delete(),r.delete(),c.delete(),s}highlightPaper(e,t){(t=t||{}).color=t.color||"orange",t.thickness=t.thickness||10;const o=document.createElement("canvas"),n=o.getContext("2d"),r=cv.imread(e),c=this.findPaperContour(r);if(cv.imshow(o,r),c){const{topLeftCorner:e,topRightCorner:o,bottomLeftCorner:i,bottomRightCorner:a}=this.getCornerPoints(c,r);e&&o&&i&&a&&(n.strokeStyle=t.color,n.lineWidth=t.thickness,n.beginPath(),n.moveTo(...Object.values(e)),n.lineTo(...Object.values(o)),n.lineTo(...Object.values(a)),n.lineTo(...Object.values(i)),n.lineTo(...Object.values(e)),n.stroke())}return r.delete(),o}extractPaper(e,t,o,n){const r=document.createElement("canvas"),c=cv.imread(e),i=n?null:this.findPaperContour(c);if(null==i&&void 0===n)return null;const{topLeftCorner:a,topRightCorner:s,bottomLeftCorner:l,bottomRightCorner:v}=n||this.getCornerPoints(i,c);let u=new cv.Mat,d=new cv.Size(t,o),C=cv.matFromArray(4,1,cv.CV_32FC2,[a.x,a.y,s.x,s.y,l.x,l.y,v.x,v.y]),f=cv.matFromArray(4,1,cv.CV_32FC2,[0,0,t,0,0,o,t,o]),h=cv.getPerspectiveTransform(C,f);return cv.warpPerspective(c,u,h,d,cv.INTER_LINEAR,cv.BORDER_CONSTANT,new cv.Scalar),cv.imshow(r,u),c.delete(),u.delete(),r}getCornerPoints(e){const t=cv.minAreaRect(e).center;let o,n,r,c,i=0,a=0,s=0,l=0;for(let d=0;d<e.data32S.length;d+=2){const C={x:e.data32S[d],y:e.data32S[d+1]},f=(v=C,u=t,Math.hypot(v.x-u.x,v.y-u.y));C.x<t.x&&C.y<t.y?f>i&&(o=C,i=f):C.x>t.x&&C.y<t.y?f>a&&(n=C,a=f):C.x<t.x&&C.y>t.y?f>s&&(r=C,s=f):C.x>t.x&&C.y>t.y&&f>l&&(c=C,l=f)}var v,u;return{topLeftCorner:o,topRightCorner:n,bottomLeftCorner:r,bottomRightCorner:c}}}}));
//# sourceMappingURL=/sm/f4b0fbf42a9b611deea75f2299eea9ad053e3664950195e6d617ef82058d69b3.map