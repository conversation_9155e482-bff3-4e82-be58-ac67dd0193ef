import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '考试ID',
    align:"center",
    dataIndex: 'examId'
   },
   {
    title: '考试名称',
    align:"center",
    dataIndex: 'examName'
   },
   {
    title: '考试课程',
    align:"center",
    dataIndex: 'courseId'
   },
   {
    title: '课程名称',
    align:"center",
    dataIndex: 'courseName'
   },
   {
    title: '学生ID',
    align:"center",
    dataIndex: 'studentId'
   },
   {
    title: '真实姓名',
    align:"center",
    dataIndex: 'realname'
   },
   {
    title: '登录账号',
    align:"center",
    dataIndex: 'stuUsername'
   },
   {
    title: '开始答题时间',
    align:"center",
    dataIndex: 'startTime'
   },
   {
    title: '提交时间',
    align:"center",
    dataIndex: 'submitTime'
   },
   {
    title: '用时(分钟)',
    align:"center",
    dataIndex: 'usedTime'
   },
   {
    title: '总得分',
    align:"center",
    dataIndex: 'totalScore'
   },
   {
    title: '状态(0-未开始,1-进行中,2-已提交,3-已批改)',
    align:"center",
    dataIndex: 'status'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '考试ID',
    field: 'examId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入考试ID!'},
          ];
     },
  },
  {
    label: '考试名称',
    field: 'examName',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入考试名称!'},
          ];
     },
  },
  {
    label: '考试课程',
    field: 'courseId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入考试课程!'},
          ];
     },
  },
  {
    label: '课程名称',
    field: 'courseName',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入课程名称!'},
          ];
     },
  },
  {
    label: '学生ID',
    field: 'studentId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入学生ID!'},
          ];
     },
  },
  {
    label: '真实姓名',
    field: 'realname',
    component: 'Input',
  },
  {
    label: '登录账号',
    field: 'stuUsername',
    component: 'Input',
  },
  {
    label: '开始答题时间',
    field: 'startTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '提交时间',
    field: 'submitTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '用时(分钟)',
    field: 'usedTime',
    component: 'InputNumber',
  },
  {
    label: '总得分',
    field: 'totalScore',
    component: 'InputNumber',
  },
  {
    label: '状态(0-未开始,1-进行中,2-已提交,3-已批改)',
    field: 'status',
    component: 'InputNumber',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  examId: {title: '考试ID',order: 0,view: 'text', type: 'string',},
  examName: {title: '考试名称',order: 1,view: 'text', type: 'string',},
  courseId: {title: '考试课程',order: 2,view: 'text', type: 'string',},
  courseName: {title: '课程名称',order: 3,view: 'text', type: 'string',},
  studentId: {title: '学生ID',order: 4,view: 'text', type: 'string',},
  realname: {title: '真实姓名',order: 5,view: 'text', type: 'string',},
  stuUsername: {title: '登录账号',order: 6,view: 'text', type: 'string',},
  startTime: {title: '开始答题时间',order: 7,view: 'datetime', type: 'string',},
  submitTime: {title: '提交时间',order: 8,view: 'datetime', type: 'string',},
  usedTime: {title: '用时(分钟)',order: 9,view: 'number', type: 'number',},
  totalScore: {title: '总得分',order: 10,view: 'number', type: 'number',},
  status: {title: '状态(0-未开始,1-进行中,2-已提交,3-已批改)',order: 11,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}