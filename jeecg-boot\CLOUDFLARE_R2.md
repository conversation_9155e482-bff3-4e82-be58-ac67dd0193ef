# Cloudflare R2 存储集成指南

本文档介绍了如何在 JeecgBoot 中配置和使用 Cloudflare R2 作为文件存储服务。

## 功能特性

- 支持文件上传、下载、删除等基本操作
- 支持生成预签名 URL 用于临时访问
- 支持 CDN 加速
- 与 JeecgBoot 现有文件上传框架无缝集成

## 配置说明

在 `application-dev.yml` 中配置 Cloudflare R2 相关参数：

```yaml
jeecg:
  # Cloudflare R2 存储配置
  cloudflare:
    access-key: ${CLOUDFLARE_R2_ACCESS_KEY:??}
    secret-key: ${CLOUDFLARE_R2_SECRET_KEY:??}
    account-id: ${CLOUDFLARE_ACCOUNT_ID:??}
    bucket-name: ${CLOUDFLARE_R2_BUCKET:jeecg-dev}
    public-url: ${CLOUDFLARE_R2_PUBLIC_URL:https://your-account-id.r2.cloudflarestorage.com}
    region: auto
    # 上传文件URL过期时间（秒），默认为7天
    presigned-url-expiry: 604800
    # 是否开启CDN加速
    cdn-enabled: true
    # CDN域名（如果开启CDN）
    cdn-url: ${CLOUDFLARE_CDN_URL:}
```

### 环境变量说明

| 环境变量 | 说明 | 必填 |
|---------|------|------|
| CLOUDFLARE_R2_ACCESS_KEY | Cloudflare R2 Access Key | 是 |
| CLOUDFLARE_R2_SECRET_KEY | Cloudflare R2 Secret Key | 是 |
| CLOUDFLARE_ACCOUNT_ID | Cloudflare 账户 ID | 是 |
| CLOUDFLARE_R2_BUCKET | 存储桶名称，默认为 jeecg-dev | 否 |
| CLOUDFLARE_R2_PUBLIC_URL | R2 公共访问 URL | 是 |
| CLOUDFLARE_CDN_URL | CDN 域名，如果开启 CDN 加速 | 否 |

## 使用方法

### 1. 代码中使用

```java
import org.jeecg.common.util.cloudflare.CloudflareR2Util;

// 上传文件
String fileUrl = CloudflareR2Util.upload(multipartFile, "bizPath");

// 获取文件访问URL
String fileUrl = CloudflareR2Util.getFileUrl("bizPath/filename.jpg");

// 删除文件
CloudflareR2Util.delete("bizPath/filename.jpg");
```

### 2. 通过 CommonUtils 使用

```java
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.constant.CommonConstant;

// 上传文件
String fileUrl = CommonUtils.upload(file, "bizPath", CommonConstant.UPLOAD_TYPE_CLOUDFLARE);
```

## 常见问题

### 1. 如何获取 Cloudflare R2 凭证？

1. 登录 Cloudflare 控制台
2. 进入 R2 页面
3. 创建 API 令牌（API Token）
4. 获取 Access Key 和 Secret Key

### 2. 如何配置 CORS？

在 Cloudflare R2 控制台中为存储桶配置 CORS 规则：

```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
    "AllowedOrigins": ["*"],
    "ExposeHeaders": [],
    "MaxAgeSeconds": 3000
  }
]
```

### 3. 如何开启 CDN？

1. 在 Cloudflare 控制台中启用 CDN
2. 在配置中设置 `cdn-enabled: true`
3. 配置 `cdn-url` 为你的 CDN 域名

## 注意事项

1. 请确保存储桶的权限设置正确
2. 生产环境请勿使用默认的 Access Key 和 Secret Key
3. 建议设置适当的文件访问策略
4. 大文件上传建议使用分片上传

## 版本要求

- JeecgBoot 3.8.0 或更高版本
- Java 17 或更高版本
- AWS SDK for Java 1.12.261 或更高版本
