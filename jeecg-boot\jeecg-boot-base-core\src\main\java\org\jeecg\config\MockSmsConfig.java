package org.jeecg.config;

import com.aliyuncs.exceptions.ClientException;
import org.jeecg.common.util.DySmsHelper;
import org.jeecg.common.util.MockDySmsHelper;
import org.jeecg.config.aliyun.DySmsConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

/**
 * Configuration for mock SMS service.
 * This configuration is only active when the 'dev' or 'test' profile is active.
 */
@Configuration
@Profile({"dev", "test"}) // Only active in dev or test profiles
public class MockSmsConfig {

    /**
     * Service interface for sending SMS
     */
    public interface SmsService {
        boolean sendSms(String phone, com.alibaba.fastjson.JSONObject templateParamJson, 
                      org.jeecg.common.constant.enums.DySmsEnum dySmsEnum) throws ClientException;
    }

    /**
     * Production implementation that uses the real DySmsHelper
     */
    public static class ProductionSmsService implements SmsService {
        @Override
        public boolean sendSms(String phone, com.alibaba.fastjson.JSONObject templateParamJson, 
                             org.jeecg.common.constant.enums.DySmsEnum dySmsEnum) throws ClientException {
            return DySmsHelper.sendSms(phone, templateParamJson, dySmsEnum);
        }
    }

    /**
     * Mock implementation that logs to console instead of sending real SMS
     */
    public static class MockSmsService implements SmsService {
        @Override
        public boolean sendSms(String phone, com.alibaba.fastjson.JSONObject templateParamJson, 
                             org.jeecg.common.constant.enums.DySmsEnum dySmsEnum) throws ClientException {
            return MockDySmsHelper.sendSms(phone, templateParamJson, dySmsEnum);
        }
    }

    /**
     * Provide the mock SMS service in dev/test environments
     */
    @Bean
    @Primary
    public SmsService smsService() {
        return new MockSmsService();
    }
    
    /**
     * Provide a mock implementation of DySmsConfig
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean
    public DySmsConfig dySmsConfig() {
        return new DySmsConfig() {
            @Override
            public String getAccessKeyId() {
                return "mock-access-key";
            }
            
            @Override
            public String getAccessKeySecret() {
                return "mock-secret-key";
            }
        };
    }
}
