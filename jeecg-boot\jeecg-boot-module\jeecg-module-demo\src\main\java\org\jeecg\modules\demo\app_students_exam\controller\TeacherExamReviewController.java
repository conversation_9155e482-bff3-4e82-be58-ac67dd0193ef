package org.jeecg.modules.demo.app_students_exam.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.app_students_exam.entity.AppStudentPaper;
import org.jeecg.modules.demo.app_students_exam.entity.AppStudentAnswer;
import org.jeecg.modules.demo.app_students_exam.service.ITeacherExamReviewService;
import org.jeecg.modules.demo.app_students_exam.vo.StudentPaperDetailVO;
import org.jeecg.modules.demo.app_students_exam.vo.StudentPaperVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.jeecg.common.system.util.JwtUtil;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * @Description: 教师考试批改控制器
 * @Author: JeecgBoot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@RestController
@RequestMapping("/teacherExamReview")
@RequiresRoles("te")  // 需要teacher角色编码	te
@Slf4j
public class TeacherExamReviewController extends JeecgController<AppStudentPaper, ITeacherExamReviewService> {
	
	@Autowired
	private ITeacherExamReviewService teacherExamReviewService;
	
	/**
	 * 将Object转换为Date，处理LocalDateTime的情况
	 * @param obj 日期对象
	 * @return Date对象
	 */
	private Date toDate(Object obj) {
		if (obj == null) {
			return null;
		}
		if (obj instanceof Date) {
			return (Date) obj;
		}
		if (obj instanceof LocalDateTime) {
			LocalDateTime ldt = (LocalDateTime) obj;
			return Date.from(ldt.atZone(ZoneId.systemDefault()).toInstant());
		}
		return null;
	}
	
		/**
	 * 获取教师的考试列表及其试卷信息
	 * @return
	 */
	@AutoLog(value = "教师批改-获取教师考试列表")
	@GetMapping(value = "/teacherExams")
	public Result<Map<String, Object>> getTeacherExams(HttpServletRequest request) {
		try {
			log.info("🎯 获取教师的考试列表及试卷信息");
			
			// 获取当前登录用户
			String username = JwtUtil.getUserNameByToken(request);
			log.info("📝 当前登录用户: {}", username);
			
			// 查询分配给当前教师的试卷所属的考试
			String examSql = "SELECT DISTINCT " +
					"e.id, " +
					"e.exam_name as name, " +
					"e.course_id, " +
					"c.course_name, " +
					"e.start_time, " +
					"e.end_time, " +
					"e.status as exam_status, " +
					"COUNT(DISTINCT sp.id) as paper_count, " +
					"COUNT(DISTINCT CASE WHEN sp.status IN ('2', '3') THEN sp.id END) as reviewed_count " +
					"FROM app_student_paper sp " +
					"JOIN app_exam e ON sp.exam_id = e.id " +
					"LEFT JOIN app_course c ON e.course_id = c.id " +
					"WHERE sp.create_by = ? " +
					"GROUP BY e.id, e.exam_name, e.course_id, c.course_name, e.start_time, e.end_time, e.status " +
					"ORDER BY e.create_time DESC";
			
			List<Map<String, Object>> examList = teacherExamReviewService.getJdbcTemplate().queryForList(examSql, username);
			
			// 处理考试数据
			for (Map<String, Object> exam : examList) {
				// 处理日期字段
				exam.put("start_time", toDate(exam.get("start_time")));
				exam.put("end_time", toDate(exam.get("end_time")));
				
				// 计算进度
				int paperCount = ((Number) exam.getOrDefault("paper_count", 0)).intValue();
				int reviewedCount = ((Number) exam.getOrDefault("reviewed_count", 0)).intValue();
				double progress = paperCount > 0 ? (double) reviewedCount / paperCount * 100 : 0;
				exam.put("progress", Math.round(progress));
			}
			
			// 查询试卷详细信息（包含学生信息）
			String paperSql = "SELECT " +
					"sp.id as paperId, " +
					"sp.exam_id as examId, " +
					"sp.student_id as studentId, " +
					"u.realname as studentName, " +
					"u.username as studentUsername, " +
					"e.exam_name as examName, " +
					"sp.start_time as startTime, " +
					"sp.submit_time as submitTime, " +
					"sp.status, " +
					"sp.total_score as totalScore, " +
					"e.total_score as maxScore, " +
					"sp.update_time as reviewTime, " +
					"CASE WHEN sp.status IN (2, 3) THEN 1 ELSE 0 END as isReviewed, " +
					"TIMESTAMPDIFF(MINUTE, sp.start_time, sp.submit_time) as duration " +
					"FROM app_student_paper sp " +
					"LEFT JOIN app_student s ON sp.student_id = s.id " +
					"LEFT JOIN sys_user u ON s.user_id = u.id " +
					"LEFT JOIN app_exam e ON sp.exam_id = e.id " +
					"WHERE sp.create_by = ? " +
					"ORDER BY sp.exam_id, sp.create_time DESC";
			
			List<Map<String, Object>> paperList = teacherExamReviewService.getJdbcTemplate().queryForList(paperSql, username);
			
			// 处理试卷数据
			for (Map<String, Object> paper : paperList) {
				paper.put("startTime", toDate(paper.get("startTime")));
				paper.put("submitTime", toDate(paper.get("submitTime")));
				paper.put("reviewTime", toDate(paper.get("reviewTime")));
				
				// 计算得分率
				Object totalScore = paper.get("totalScore");
				Object maxScore = paper.get("maxScore");
				if (totalScore != null && maxScore != null && ((Number) maxScore).doubleValue() > 0) {
					double scoreRate = ((Number) totalScore).doubleValue() / ((Number) maxScore).doubleValue() * 100;
					paper.put("scoreRate", Math.round(scoreRate * 100) / 100.0);
				}
			}
			
			// 组合返回结果
			Map<String, Object> result = new HashMap<>();
			result.put("examList", examList);
			result.put("paperList", paperList);
			
			log.info("✅ 获取到 {} 个考试，{} 份试卷", examList.size(), paperList.size());
			return Result.OK(result);
		} catch (Exception e) {
			log.error("💥 获取教师考试列表失败: {}", e.getMessage(), e);
			return Result.error("获取教师考试列表失败: " + e.getMessage());
		}
	}
	
	/**
	 * 获取指定考试的所有学生试卷列表
	 * @param examId 考试ID
	 * @return
	 */
	@AutoLog(value = "教师批改-获取学生试卷列表")
	@GetMapping(value = "/studentPapers")
	public Result<List<StudentPaperVO>> getStudentPapers(@RequestParam(name="examId",required=true) String examId) {
		try {
			log.info("🎯 获取考试学生试卷列表, examId: {}", examId);
			List<StudentPaperVO> result = teacherExamReviewService.getStudentPapersByExamId(examId);
			log.info("✅ 获取学生试卷列表成功, 数量: {}", result.size());
			return Result.OK(result);
		} catch (Exception e) {
			log.error("💥 获取学生试卷列表失败: {}", e.getMessage(), e);
			return Result.error("获取学生试卷列表失败: " + e.getMessage());
		}
	}
	
	/**
	 * 获取学生试卷详情（用于批改）
	 * @param paperId 试卷ID
	 * @return
	 */
	@AutoLog(value = "教师批改-获取学生试卷详情")
	@GetMapping(value = "/studentPaperDetail")
	public Result<StudentPaperDetailVO> getStudentPaperDetail(@RequestParam(name="paperId",required=true) String paperId) {
		try {
			log.info("🎯 获取学生试卷详情, paperId: {}", paperId);
			StudentPaperDetailVO result = teacherExamReviewService.getStudentPaperDetail(paperId);
			log.info("✅ 获取学生试卷详情成功");
			return Result.OK(result);
		} catch (Exception e) {
			log.error("💥 获取学生试卷详情失败: {}", e.getMessage(), e);
			return Result.error("获取学生试卷详情失败: " + e.getMessage());
		}
	}
	
	/**
	 * 更新单题分数
	 * @param paperId 试卷ID
	 * @param questionId 题目ID
	 * @param score 得分
	 * @param maxScore 满分
	 * @param reviewComment 批改备注
	 * @return
	 */
	@AutoLog(value = "教师批改-更新题目分数")
	@PostMapping(value = "/updateQuestionScore")
	public Result<String> updateQuestionScore(
			@RequestParam(name="paperId",required=true) String paperId,
			@RequestParam(name="questionId",required=true) String questionId,
			@RequestParam(name="score",required=true) String score,
			@RequestParam(name="maxScore",required=true) String maxScore,
			@RequestParam(name="reviewComment",required=false) String reviewComment) {
		try {
			log.info("🎯 更新题目分数, paperId: {}, questionId: {}, score: {}, maxScore: {}", 
					paperId, questionId, score, maxScore);
			
			// 参数验证
			if (oConvertUtils.isEmpty(paperId) || oConvertUtils.isEmpty(questionId)) {
				return Result.error("试卷ID和题目ID不能为空");
			}
			
			double scoreValue;
			double maxScoreValue;
			try {
				scoreValue = Double.parseDouble(score);
				maxScoreValue = Double.parseDouble(maxScore);
			} catch (NumberFormatException e) {
				return Result.error("分数格式错误");
			}
			
			if (scoreValue < 0 || scoreValue > maxScoreValue) {
				return Result.error("分数不能小于0或大于满分");
			}
			
			teacherExamReviewService.updateQuestionScore(paperId, questionId, scoreValue, maxScoreValue, reviewComment);
			log.info("✅ 更新题目分数成功");
			return Result.OK("更新题目分数成功");
		} catch (Exception e) {
			log.error("💥 更新题目分数失败: {}", e.getMessage(), e);
			return Result.error("更新题目分数失败: " + e.getMessage());
		}
	}
	
	/**
	 * 更新试卷总分
	 * @param paperId 试卷ID
	 * @param totalScore 总分
	 * @return
	 */
	@AutoLog(value = "教师批改-更新试卷总分")
	@PostMapping(value = "/updatePaperScore")
	public Result<String> updatePaperScore(
			@RequestParam(name="paperId",required=true) String paperId,
			@RequestParam(name="totalScore",required=true) String totalScore) {
		try {
			log.info("🎯 更新试卷总分, paperId: {}, totalScore: {}", paperId, totalScore);
			
			if (oConvertUtils.isEmpty(paperId)) {
				return Result.error("试卷ID不能为空");
			}
			
			double totalScoreValue;
			try {
				totalScoreValue = Double.parseDouble(totalScore);
			} catch (NumberFormatException e) {
				return Result.error("总分格式错误");
			}
			
			if (totalScoreValue < 0) {
				return Result.error("总分不能小于0");
			}
			
			teacherExamReviewService.updatePaperTotalScore(paperId, totalScoreValue);
			log.info("✅ 更新试卷总分成功");
			return Result.OK("更新试卷总分成功");
		} catch (Exception e) {
			log.error("💥 更新试卷总分失败: {}", e.getMessage(), e);
			return Result.error("更新试卷总分失败: " + e.getMessage());
		}
	}
	
	/**
	 * 完成批改（标记试卷为已批改状态）
	 * @param paperId 试卷ID
	 * @return
	 */
	@AutoLog(value = "教师批改-完成批改")
	@PostMapping(value = "/finishReview")
	public Result<String> finishReview(@RequestParam(name="paperId",required=true) String paperId) {
		try {
			log.info("🎯 完成试卷批改, paperId: {}", paperId);
			
			if (oConvertUtils.isEmpty(paperId)) {
				return Result.error("试卷ID不能为空");
			}
			
			teacherExamReviewService.finishReview(paperId);
			log.info("✅ 完成试卷批改成功");
			return Result.OK("试卷批改已完成");
		} catch (Exception e) {
			log.error("💥 完成试卷批改失败: {}", e.getMessage(), e);
			return Result.error("完成试卷批改失败: " + e.getMessage());
		}
	}
	
	/**
	 * 获取已批改的试卷列表
	 * @param examId 考试ID（可选）
	 * @return
	 */
	@AutoLog(value = "教师批改-获取已批改试卷列表")
	@GetMapping(value = "/reviewedPapers")
	public Result<List<StudentPaperVO>> getReviewedPapers(@RequestParam(name="examId",required=false) String examId) {
		try {
			log.info("🎯 获取已批改试卷列表, examId: {}", examId);
			List<StudentPaperVO> result = teacherExamReviewService.getReviewedPapers(examId);
			log.info("✅ 获取已批改试卷列表成功, 数量: {}", result.size());
			return Result.OK(result);
		} catch (Exception e) {
			log.error("💥 获取已批改试卷列表失败: {}", e.getMessage(), e);
			return Result.error("获取已批改试卷列表失败: " + e.getMessage());
		}
	}
	
	/**
	 * 批量自动批改（重新运行自动批改逻辑）
	 * @param examId 考试ID
	 * @return
	 */
	@AutoLog(value = "教师批改-批量自动批改")
	@PostMapping(value = "/batchReview")
	public Result<String> batchAutoReview(@RequestParam(name="examId",required=true) String examId) {
		try {
			log.info("🎯 批量自动批改, examId: {}", examId);
			
			if (oConvertUtils.isEmpty(examId)) {
				return Result.error("考试ID不能为空");
			}
			
			int count = teacherExamReviewService.batchAutoReview(examId);
			log.info("✅ 批量自动批改成功, 处理试卷数量: {}", count);
			return Result.OK("批量自动批改完成，处理了 " + count + " 份试卷");
		} catch (Exception e) {
			log.error("💥 批量自动批改失败: {}", e.getMessage(), e);
			return Result.error("批量自动批改失败: " + e.getMessage());
		}
	}
	
	/**
	 * 获取考试统计信息（待批改、已批改等）
	 * @param examId 考试ID
	 * @return
	 */
	@AutoLog(value = "教师批改-获取考试统计信息")
	@GetMapping(value = "/examStatistics")
	public Result<Map<String, Object>> getExamStatistics(@RequestParam(name="examId",required=true) String examId,
														  HttpServletRequest request) {
		try {
			log.info("🎯 获取考试统计信息, examId: {}", examId);
			
			// 获取当前登录教师
			String username = JwtUtil.getUserNameByToken(request);
			log.info("📝 当前登录教师: {}", username);
			
			Map<String, Object> result = teacherExamReviewService.getExamStatistics(examId, username);
			log.info("✅ 获取考试统计信息成功");
			return Result.OK(result);
		} catch (Exception e) {
			log.error("💥 获取考试统计信息失败: {}", e.getMessage(), e);
			return Result.error("获取考试统计信息失败: " + e.getMessage());
		}
	}
	
	/**
	 * 批量更新题目分数
	 */
	@AutoLog(value = "教师批改-批量更新题目分数")
	@PostMapping(value = "/batchUpdateQuestionScores")
	public Result<String> batchUpdateQuestionScores(@RequestBody Map<String, Object> requestData) {
		try {
			log.info("🎯 批量更新题目分数");
			
			@SuppressWarnings("unchecked")
			List<Map<String, Object>> questions = (List<Map<String, Object>>) requestData.get("questions");
			
			if (questions == null || questions.isEmpty()) {
				return Result.error("没有需要更新的题目");
			}
			
			int successCount = 0;
			int errorCount = 0;
			
			for (Map<String, Object> question : questions) {
				try {
					String paperId = (String) question.get("paperId");
					String questionId = (String) question.get("questionId");
					Double score = Double.valueOf(question.get("score").toString());
					Double maxScore = Double.valueOf(question.get("maxScore").toString());
					String reviewComment = (String) question.get("reviewComment");
					
					teacherExamReviewService.updateQuestionScore(paperId, questionId, score, maxScore, reviewComment);
					successCount++;
				} catch (Exception e) {
					log.error("更新题目分数失败: {}", e.getMessage());
					errorCount++;
				}
			}
			
			String message = String.format("批量更新完成，成功%d个，失败%d个", successCount, errorCount);
			log.info("✅ {}", message);
			
			if (errorCount > 0) {
				return Result.error(message);
			} else {
				return Result.OK(message);
			}
			
		} catch (Exception e) {
			log.error("批量更新题目分数失败", e);
			return Result.error("批量更新题目分数失败: " + e.getMessage());
		}
	}
} 