<template>
  <LoginFormTitle v-show="getShow" class="enter-x" />
  <Form class="p-4 enter-x" :model="formData" :rules="getFormRules" ref="formRef" v-show="getShow" @keypress.enter="handleLogin">
    <FormItem name="account" class="enter-x">
      <Input size="large" v-model:value="formData.account" :placeholder="t('sys.login.userName')" class="fix-auto-fill" />
    </FormItem>
    <FormItem name="password" class="enter-x">
      <InputPassword size="large" visibilityToggle v-model:value="formData.password" :placeholder="t('sys.login.password')" />
    </FormItem>

    <!--验证码类型-->
    <a-row class="enter-x">
      <a-col :span="24">
        <a-radio-group v-model:value="formData.captchaType" style="margin-bottom: 8px">
          <a-radio-button value="image">图片验证码</a-radio-button>
          <a-radio-button value="turnstile">Turnstile</a-radio-button>
        </a-radio-group>
      </a-col>
    </a-row>

    <!-- 原图片验证码输入和图片 -->
    <a-row v-if="formData.captchaType === 'image'" class="enter-x">
      <a-col :span="12">
        <FormItem name="inputCode" class="enter-x">
          <Input size="large" v-model:value="formData.inputCode" :placeholder="t('sys.login.inputCode')" style="min-width: 100px" />
        </FormItem>
      </a-col>
      <a-col :span="8">
        <FormItem :style="{ 'text-align': 'right', 'margin-left': '20px' }" class="enter-x">
          <img
            v-if="randCodeData.requestCodeSuccess"
            style="margin-top: 2px; max-width: initial"
            :src="randCodeData.randCodeImage"
            @click="handleChangeCheckCode"
          />
          <img v-else style="margin-top: 2px; max-width: initial" src="../../../assets/images/checkcode.png" @click="handleChangeCheckCode" />
        </FormItem>
      </a-col>
    </a-row>

    <!-- Turnstile -->
    <a-row v-if="formData.captchaType === 'turnstile'" class="enter-x">
      <a-col :span="24">
        <div id="turnstile-container"></div>
        <!-- 注释掉重新加载按钮，但保留代码 
        <a-button type="link" @click="reloadTurnstile" style="padding: 0; margin-top: 8px;">
          <ReloadOutlined /> {{ t('sys.login.reloadCaptcha') }}
        </a-button>
        -->
      </a-col>
    </a-row>

    <ARow class="enter-x">
      <ACol :span="12">
        <FormItem>
          <!-- No logic, you need to deal with it yourself -->
          <Checkbox v-model:checked="rememberMe" size="small">
            {{ t('sys.login.rememberMe') }}
          </Checkbox>
        </FormItem>
      </ACol>
      <ACol :span="12">
        <FormItem :style="{ 'text-align': 'right' }">
          <!-- No logic, you need to deal with it yourself -->
          <Button type="link" size="small" @click="setLoginState(LoginStateEnum.RESET_PASSWORD)">
            {{ t('sys.login.forgetPassword') }}
          </Button>
        </FormItem>
      </ACol>
    </ARow>

    <FormItem class="enter-x">
      <Button type="primary" size="large" block @click="handleLogin" :loading="loading">
        {{ t('sys.login.loginButton') }}
      </Button>
    </FormItem>
    
    <!-- Login options buttons -->
    <div class="mt-4 enter-x">
      <Button block class="mb-2" @click="setLoginState(LoginStateEnum.MOBILE)">
        {{ t('sys.login.mobileSignInFormTitle') }}
      </Button>
      
      <Button block class="mb-2" @click="setLoginState(LoginStateEnum.EMAIL)">
        {{ t('sys.login.emailSignInFormTitle') }}
      </Button>
      
      <Button block class="mb-2" @click="setLoginState(LoginStateEnum.QR_CODE)">
        {{ t('sys.login.qrSignInFormTitle') }}
      </Button>
      
      <Button block class="mb-2" @click="setLoginState(LoginStateEnum.REGISTER)">
        {{ t('sys.login.registerButton') }}
      </Button>
    </div>

    <Divider class="enter-x">{{ t('sys.login.otherSignIn') }}</Divider>

    <div class="flex justify-evenly enter-x" :class="`${prefixCls}-sign-in-way`">
      <a @click="onThirdLogin('github')" title="github"><GithubFilled /></a>
      <a @click="onThirdLogin('wechat_enterprise')" title="企业微信"> <icon-font class="item-icon" type="icon-qiyeweixin3" /></a>
      <a @click="onThirdLogin('dingtalk')" title="钉钉"><DingtalkCircleFilled /></a>
      <a @click="onThirdLogin('wechat_open')" title="微信"><WechatFilled /></a>
    </div>
  </Form>
  <!-- 第三方登录相关弹框 -->
 <!-- <ThirdModal ref="thirdModalRef"></ThirdModal> -->
</template>
<script lang="ts" setup>
  import { reactive, ref, toRaw, unref, computed, onMounted, watch } from 'vue';

  import { Checkbox, Form, Input, Row, Col, Button, Divider } from 'ant-design-vue';
  import { GithubFilled, WechatFilled, DingtalkCircleFilled, ReloadOutlined, createFromIconfontCN } from '@ant-design/icons-vue';
  import LoginFormTitle from './LoginFormTitle.vue';
  import ThirdModal from './ThirdModal.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';

  import { useUserStore } from '/@/store/modules/user';
  import { LoginStateEnum, useLoginState, useFormRules, useFormValid } from './useLogin';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { getCodeInfo } from '/@/api/sys/user';
  //import { onKeyStroke } from '@vueuse/core';

  const ACol = Col;
  const ARow = Row;
  const FormItem = Form.Item;
  const InputPassword = Input.Password;
  const IconFont = createFromIconfontCN({
    scriptUrl: '//at.alicdn.com/t/font_2316098_umqusozousr.js',
  });
  const { t } = useI18n();
  const { notification, createErrorModal } = useMessage();
  const { prefixCls } = useDesign('login');
  const userStore = useUserStore();

  const { setLoginState, getLoginState } = useLoginState();
  const { getFormRules } = useFormRules();

  const formRef = ref();
  const thirdModalRef = ref();
  const loading = ref(false);
  const rememberMe = ref(false);

  const formData = reactive({
    account: 'admin',
    password: '123456',
    inputCode: '',
    captchaType: 'image',
    turnstileToken: '',
  });
  const randCodeData = reactive({
    randCodeImage: '',
    requestCodeSuccess: false,
    checkKey: null,
  });

  const { validForm } = useFormValid(formRef);

  //onKeyStroke('Enter', handleLogin);

  const getShow = computed(() => {
    const state = unref(getLoginState);
    console.log('Current login state:', state);
    return state === LoginStateEnum.LOGIN || state === LoginStateEnum.EMAIL || state === LoginStateEnum.MOBILE || state === LoginStateEnum.QR_CODE;
  });

  function loadTurnstile() {
    if (window.turnstile) {
      // 清空容器内容，确保重新渲染
      const container = document.getElementById('turnstile-container');
      if (container) {
        container.innerHTML = '';
      }
      
      window.turnstile.render('#turnstile-container', {
        sitekey: '0x4AAAAAABhBBIww9F9MG1Ov', // 替换为你的site-key
        callback: (token) => {
          formData.turnstileToken = token;
        },
        'expired-callback': () => {
          formData.turnstileToken = '';
        },
        'error-callback': () => {
          formData.turnstileToken = '';
        },
      });
    }
  }

  // 添加重新加载Turnstile验证码的方法
  function reloadTurnstile() {
    formData.turnstileToken = '';
    loadTurnstile();
    notification.success({
      message: t('sys.login.captchaTitle'),
      description: t('sys.login.captchaReloaded'),
      duration: 3,
    });
  }

  watch(() => formData.captchaType, (val) => {
    if (val === 'turnstile') {
      // 动态加载Turnstile脚本
      if (!window.turnstile) {
        const script = document.createElement('script');
        script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
        script.async = true;
        script.onload = loadTurnstile;
        document.body.appendChild(script);
      } else {
        loadTurnstile();
      }
    }
  });

  async function handleLogin() {
    const data = await validForm();
    if (!data) return;
    try {
      loading.value = true;
      const loginParams = {
        password: data.password,
        username: data.account,
        captchaType: formData.captchaType,
        turnstileToken: formData.captchaType === 'turnstile' ? formData.turnstileToken : undefined,
        captcha: formData.captchaType === 'image' ? data.inputCode : undefined,
        checkKey: formData.captchaType === 'image' ? randCodeData.checkKey : undefined,
        mode: 'none',
      };
      const { userInfo } = await userStore.login(toRaw(loginParams));
      if (userInfo) {
        notification.success({
          message: t('sys.login.loginSuccessTitle'),
          description: `${t('sys.login.loginSuccessDesc')}: ${userInfo.realname}`,
          duration: 3,
        });
      }
    } catch (error) {
      notification.error({
        message: t('sys.api.errorTip'),
        description: error.message || t('sys.api.networkExceptionMsg'),
        duration: 3,
      });
      loading.value = false;
      
      // 登录失败时，根据错误类型自动重新加载验证码
      if (formData.captchaType === 'turnstile') {
        // 如果错误信息包含验证码相关的关键词，自动重新加载
        if (error.message && (
            error.message.toLowerCase().includes('turnstile') || 
            error.message.toLowerCase().includes('captcha') || 
            error.message.toLowerCase().includes('验证码')
          )) {
          console.log('检测到验证码错误，自动重新加载Turnstile验证码');
          reloadTurnstile();
        }
      } else {
        handleChangeCheckCode();
      }
    }
  }
  function handleChangeCheckCode() {
    formData.inputCode = '';
    //TODO 兼容mock和接口，暂时这样处理
    //update-begin---author:chenrui ---date:2025/1/7  for：[QQYUN-10775]验证码可以复用 #7674------------
    randCodeData.checkKey = new Date().getTime() + Math.random().toString(36).slice(-4); // 1629428467008;
    //update-end---author:chenrui ---date:2025/1/7  for：[QQYUN-10775]验证码可以复用 #7674------------
    getCodeInfo(randCodeData.checkKey).then((res) => {
      randCodeData.randCodeImage = res;
      randCodeData.requestCodeSuccess = true;
    });
  }

  /**
   * 第三方登录
   * @param type
   */
  function onThirdLogin(type) {
    thirdModalRef.value.onThirdLogin(type);
  }
  //初始化验证码
  onMounted(() => {
    handleChangeCheckCode();
  });
</script>
