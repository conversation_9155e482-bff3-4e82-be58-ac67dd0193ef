package org.jeecg.config;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.enums.DySmsEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

/**
 * Configuration for email verification service.
 */
@Configuration
@Slf4j
public class EmailVerificationConfig {

    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.from-address:<EMAIL>}")
    private String fromAddress;

    /**
     * Service interface for sending email verification codes
     */
    public interface EmailService {
        boolean sendVerificationEmail(String email, JSONObject templateParamJson, DySmsEnum templateEnum) throws MessagingException;
    }

    /**
     * Implementation that sends verification emails
     */
    @Slf4j
    public static class EmailVerificationService implements EmailService {
        
        private final JavaMailSender mailSender;
        private final String fromAddress;
        
        public EmailVerificationService(JavaMailSender mailSender, String fromAddress) {
            this.mailSender = mailSender;
            this.fromAddress = fromAddress;
        }
        
        @Override
        public boolean sendVerificationEmail(String email, JSONObject templateParamJson, DySmsEnum templateEnum) throws MessagingException {
            try {
                MimeMessage message = mailSender.createMimeMessage();
                MimeMessageHelper helper = new MimeMessageHelper(message, true);
                
                helper.setFrom(fromAddress);
                helper.setTo(email);
                
                String code = templateParamJson.getString("code");
                String subject;
                String content;
                
                // Determine email subject and content based on template type
                if (templateEnum == DySmsEnum.LOGIN_TEMPLATE_CODE) {
                    subject = "Login Verification Code";
                    content = "Your login verification code is: " + code + ". Valid for 10 minutes.";
                } else if (templateEnum == DySmsEnum.REGISTER_TEMPLATE_CODE) {
                    subject = "Registration Verification Code";
                    content = "Your registration verification code is: " + code + ". Valid for 10 minutes.";
                } else if (templateEnum == DySmsEnum.FORGET_PASSWORD_TEMPLATE_CODE) {
                    subject = "Password Reset Verification Code";
                    content = "Your password reset verification code is: " + code + ". Valid for 10 minutes.";
                } else {
                    subject = "Verification Code";
                    content = "Your verification code is: " + code + ". Valid for 10 minutes.";
                }
                
                helper.setSubject(subject);
                helper.setText(content, false);
                
                mailSender.send(message);
                log.info("Email verification code sent to {}, code: {}", email, code);
                return true;
            } catch (MessagingException e) {
                log.error("Failed to send verification email to {}", email, e);
                throw e;
            }
        }
    }

    /**
     * Provide the email verification service
     */
    @Bean
    public EmailService emailService() {
        return new EmailVerificationService(mailSender, fromAddress);
    }
}
