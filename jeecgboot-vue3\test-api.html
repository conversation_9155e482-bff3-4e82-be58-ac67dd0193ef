<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
</head>
<body>
    <h1>测试保存答案API</h1>
    <button id="testBtn">测试提交单题答案</button>
    <div id="result"></div>

    <script>
        document.getElementById('testBtn').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在测试...';
            
            try {
                const params = new URLSearchParams();
                params.append('questionId', 'test_question_123');
                params.append('answerContent', 'A');
                params.append('paperId', 'test_paper_456');
                
                console.log('发送请求参数:', params.toString());
                
                const response = await fetch('/api/appStudentsExam/submitSingleAnswer', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Access-Token': localStorage.getItem('ACCESS_TOKEN') || ''
                    },
                    body: params
                });
                
                const data = await response.json();
                console.log('API响应:', data);
                
                resultDiv.innerHTML = `
                    <h3>响应结果:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('请求失败:', error);
                resultDiv.innerHTML = `<h3>错误:</h3><pre>${error.message}</pre>`;
            }
        });
    </script>
</body>
</html> 