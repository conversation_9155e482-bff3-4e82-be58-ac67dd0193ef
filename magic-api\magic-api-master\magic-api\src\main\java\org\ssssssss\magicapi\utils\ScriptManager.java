package org.ssssssss.magicapi.utils;

import org.ssssssss.script.MagicScript;
import org.ssssssss.script.MagicScriptContext;
import org.ssssssss.script.MagicScriptDebugContext;

import java.util.Map;

/**
 * 脚本管理
 *
 * <AUTHOR>
 */
public class ScriptManager {

	private static final String EXPRESSION_PREFIX = "/* generated by execute expression */ return ";

	/**
	 * 执行脚本
	 */
	public static Object executeScript(String script, MagicScriptContext context) {
		script = (context instanceof MagicScriptDebugContext ? MagicScript.DEBUG_MARK : "") + script;
		MagicScript magicScript = MagicScript.create(script, null);
		// 执行脚本
		return magicScript.execute(context);
	}

	/**
	 * 执行脚本
	 */
	public static Object executeExpression(String script, MagicScriptContext context) {
		return executeScript(EXPRESSION_PREFIX + script, context);
	}

	/**
	 * 执行脚本
	 */
	public static Object executeExpression(String script, Map<String, Object> paramMap) {
		return executeExpression(script, new MagicScriptContext(paramMap));
	}
}
