<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JScanify - Document Scanner</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .upload-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .upload-area {
            border: 3px dashed #4facfe;
            border-radius: 15px;
            padding: 60px 20px;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-area:hover {
            border-color: #00f2fe;
            background: #f0f7ff;
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: #00f2fe;
            background: #e6f3ff;
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
        }

        .upload-area.dragover .upload-icon {
            animation: bounce 0.6s ease-in-out infinite alternate;
        }

        @keyframes bounce {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-10px); }
        }

        .upload-icon {
            font-size: 4rem;
            color: #4facfe;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 15px;
        }

        .upload-subtext {
            color: #999;
            font-size: 0.9rem;
        }

        #fileInput {
            display: none;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .results-section {
            display: none;
            margin-top: 40px;
        }

        .results-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }

        .result-item {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }

        .result-item h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .result-canvas {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #d63031;
            display: none;
        }

        .success {
            background: #e6ffe6;
            color: #00b894;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #00b894;
            display: none;
        }

        @media (max-width: 768px) {
            .results-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }

            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 JScanify Scanner</h1>
            <p>Upload a photo and automatically detect and crop documents</p>
        </div>

        <div class="main-content">
            <div class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">Click to upload or drag and drop</div>
                    <div class="upload-subtext">Supports JPG, PNG, GIF files</div>
                    <input type="file" id="fileInput" accept="image/*">
                </div>

                <div style="margin-top: 20px;">
                    <button class="btn" onclick="document.getElementById('fileInput').click()">
                        Choose File
                    </button>
                    <button class="btn" id="scanBtn" onclick="scanDocument()" disabled>
                        🔍 Scan Document
                    </button>
                    <button class="btn" id="downloadBtn" onclick="downloadResult()" disabled style="display: none;">
                        💾 Download Result
                    </button>
                </div>
            </div>

            <div class="error" id="errorMsg"></div>
            <div class="success" id="successMsg"></div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Processing your document...</p>
            </div>

            <div class="results-section" id="resultsSection">
                <h2 style="text-align: center; color: #333; margin-bottom: 20px;">Scan Results</h2>
                <div class="results-grid">
                    <div class="result-item">
                        <h3>📷 Original with Detection</h3>
                        <canvas id="highlightCanvas" class="result-canvas"></canvas>
                    </div>
                    <div class="result-item">
                        <h3>📄 Extracted Document</h3>
                        <canvas id="extractCanvas" class="result-canvas"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let scanner;
        let currentImage;
        let isOpenCvReady = false;
        let extractedCanvas;

        // Initialize when OpenCV is ready
        function onOpenCvReady() {
            console.log('OpenCV.js is ready');
            isOpenCvReady = true;
            try {
                scanner = new jscanify();
                showSuccess('Scanner initialized successfully!');
            } catch (error) {
                console.error('Failed to initialize scanner:', error);
                showError('Failed to initialize scanner. Please refresh the page.');
            }
        }

        // Initialize scanner
        function initializeScanner() {
            console.log('Starting scanner initialization...');

            // Load OpenCV
            const script = document.createElement('script');
            script.src = 'https://docs.opencv.org/4.7.0/opencv.js';
            script.async = true;

            script.onload = () => {
                console.log('OpenCV script loaded, waiting for initialization...');

                const checkCV = () => {
                    if (typeof cv !== 'undefined' && cv.Mat) {
                        console.log('OpenCV is ready, loading JScanify...');

                        // Load JScanify
                        const jscanifyScript = document.createElement('script');
                        jscanifyScript.src = 'https://cdn.jsdelivr.net/gh/ColonelParrot/jscanify@master/src/jscanify.min.js';
                        jscanifyScript.onload = () => {
                            onOpenCvReady();
                        };
                        jscanifyScript.onerror = () => {
                            showError('Failed to load JScanify library');
                        };
                        document.head.appendChild(jscanifyScript);
                    } else {
                        setTimeout(checkCV, 100);
                    }
                };
                checkCV();
            };

            script.onerror = () => {
                showError('Failed to load OpenCV library');
            };

            document.head.appendChild(script);
        }
    </script>

    <!-- Scripts will be loaded dynamically -->

    <script>

        // File input handling
        let fileInput, uploadArea, scanBtn, downloadBtn;

        function initializeEventListeners() {
            fileInput = document.getElementById('fileInput');
            uploadArea = document.getElementById('uploadArea');
            scanBtn = document.getElementById('scanBtn');
            downloadBtn = document.getElementById('downloadBtn');

            fileInput.addEventListener('change', handleFileSelect);

            // Drag and drop functionality
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragenter', handleDragEnter);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
            uploadArea.addEventListener('click', handleUploadClick);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.stopPropagation();
            uploadArea.classList.add('dragover');
        }

        function handleDragEnter(e) {
            e.preventDefault();
            e.stopPropagation();
            uploadArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            e.stopPropagation();
            // Only remove dragover if we're leaving the upload area entirely
            if (!uploadArea.contains(e.relatedTarget)) {
                uploadArea.classList.remove('dragover');
            }
        }

        function handleDrop(e) {
            e.preventDefault();
            e.stopPropagation();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        function handleUploadClick() {
            fileInput.click();
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                showError('Please select a valid image file.');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    currentImage = img;
                    scanBtn.disabled = false;
                    showSuccess('Image loaded successfully! Click "Scan Document" to process.');

                    // Update upload area to show selected file but preserve functionality
                    updateUploadAreaContent(file.name, true);
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        function updateUploadAreaContent(fileName = null, isLoaded = false) {
            if (isLoaded && fileName) {
                uploadArea.innerHTML = `
                    <div class="upload-icon">✅</div>
                    <div class="upload-text">Image loaded: ${fileName}</div>
                    <div class="upload-subtext">Ready to scan • Click to change or drag new file</div>
                `;
            } else {
                uploadArea.innerHTML = `
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">Click to upload or drag and drop</div>
                    <div class="upload-subtext">Supports JPG, PNG, GIF files</div>
                `;
            }

            // Re-add the file input
            const existingInput = uploadArea.querySelector('#fileInput');
            if (!existingInput) {
                const newInput = document.createElement('input');
                newInput.type = 'file';
                newInput.id = 'fileInput';
                newInput.accept = 'image/*';
                newInput.style.display = 'none';
                newInput.addEventListener('change', handleFileSelect);
                uploadArea.appendChild(newInput);
                fileInput = newInput;
            }

            // Re-attach event listeners to ensure drag and drop still works
            uploadArea.removeEventListener('dragover', handleDragOver);
            uploadArea.removeEventListener('dragenter', handleDragEnter);
            uploadArea.removeEventListener('dragleave', handleDragLeave);
            uploadArea.removeEventListener('drop', handleDrop);
            uploadArea.removeEventListener('click', handleUploadClick);

            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragenter', handleDragEnter);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
            uploadArea.addEventListener('click', handleUploadClick);
        }

        function scanDocument() {
            if (!isOpenCvReady) {
                showError('Scanner is still initializing. Please wait a moment and try again.');
                return;
            }

            if (!currentImage) {
                showError('Please select an image first.');
                return;
            }

            showLoading(true);
            hideMessages();

            try {
                // Small delay to allow UI to update
                setTimeout(() => {
                    try {
                        // Highlight paper detection
                        const highlightCanvas = scanner.highlightPaper(currentImage);
                        const highlightCtx = document.getElementById('highlightCanvas');

                        // Copy the result to our canvas
                        highlightCtx.width = highlightCanvas.width;
                        highlightCtx.height = highlightCanvas.height;
                        const ctx = highlightCtx.getContext('2d');
                        ctx.drawImage(highlightCanvas, 0, 0);

                        // Extract paper with automatic sizing
                        const paperWidth = 800;
                        const paperHeight = 1000;
                        extractedCanvas = scanner.extractPaper(currentImage, paperWidth, paperHeight);

                        const extractCtx = document.getElementById('extractCanvas');
                        extractCtx.width = extractedCanvas.width;
                        extractCtx.height = extractedCanvas.height;
                        const ctx2 = extractCtx.getContext('2d');
                        ctx2.drawImage(extractedCanvas, 0, 0);

                        // Show results
                        document.getElementById('resultsSection').style.display = 'block';
                        downloadBtn.style.display = 'inline-block';
                        downloadBtn.disabled = false;

                        showLoading(false);
                        showSuccess('Document scanned successfully!');

                        // Scroll to results
                        document.getElementById('resultsSection').scrollIntoView({
                            behavior: 'smooth'
                        });

                    } catch (error) {
                        console.error('Scanning error:', error);
                        showLoading(false);
                        showError('Failed to scan document. Please try with a different image or ensure the document is clearly visible.');
                    }
                }, 100);

            } catch (error) {
                console.error('Scanning error:', error);
                showLoading(false);
                showError('Failed to scan document. Please try again.');
            }
        }

        function downloadResult() {
            if (!extractedCanvas) {
                showError('No scanned document to download.');
                return;
            }

            try {
                // Create download link
                const link = document.createElement('a');
                link.download = 'scanned-document.png';
                link.href = extractedCanvas.toDataURL();
                link.click();

                showSuccess('Document downloaded successfully!');
            } catch (error) {
                console.error('Download error:', error);
                showError('Failed to download document.');
            }
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMsg');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('successMsg');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 3000);
        }

        function hideMessages() {
            document.getElementById('errorMsg').style.display = 'none';
            document.getElementById('successMsg').style.display = 'none';
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            console.log('Page loaded, starting scanner initialization...');
            initializeEventListeners();
            initializeScanner();
        });
    </script>
</body>
</html>