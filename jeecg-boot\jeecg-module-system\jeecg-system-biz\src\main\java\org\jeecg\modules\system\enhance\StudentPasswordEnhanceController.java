package org.jeecg.modules.system.enhance;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: Student表密码加密增强Http-api
 * @Author: AI Assistant
 * @Date: 2025-01-25
 */
@Slf4j
@RestController
@RequestMapping("/api/enhance/student")
public class StudentPasswordEnhanceController {

    @Autowired
    private ISysUserService sysUserService;

    // 在类加载时输出提示信息
    static {
        System.out.println("🚀🚀🚀 StudentPasswordEnhanceController 类已加载！");
        System.out.println("🚀 接口地址: /api/enhance/student/encryptPassword");
        System.out.println("🚀 接口地址: /api/enhance/student/verifyPassword");
        System.out.println("🚀 接口地址: /api/enhance/student/syncToSysUser");
    }

    /**
     * Student表密码加密增强接口
     * 用于Online表单开发中的Student表密码字段加密处理
     * 
     * @param params 包含表单数据的JSON对象
     * @return Result 处理结果
     */
    @PostMapping("/encryptPassword")
    public Result<?> encryptStudentPassword(@RequestBody JSONObject params) {
        System.out.println("========================================");
        System.out.println("🔥 Student密码加密增强接口被调用！");
        System.out.println("🔥 调用时间: " + new java.util.Date());
        System.out.println("🔥 接收参数: " + params.toJSONString());
        System.out.println("========================================");
        
        log.info("=== Student密码加密增强接口被调用 ===");
        log.info("接收参数: {}", params.toJSONString());
        
        try {
            // 获取表名和记录数据
            String tableName = params.getString("tableName");
            JSONObject record = params.getJSONObject("record");
            
            if (record == null) {
                log.error("record参数为空");
                return Result.error("记录数据不能为空");
            }
            
            System.out.println("📋 表名: " + tableName);
            System.out.println("📋 记录数据: " + record.toJSONString());
            
            log.info("表名: {}", tableName);
            log.info("记录数据: {}", record.toJSONString());
            
            // 检查是否为Student表
            if (!"app_student".equals(tableName)) {
                System.out.println("⚠️  表名不匹配，当前表名: " + tableName + ", 期望: app_student");
                log.warn("表名不匹配，当前表名: {}, 期望: app_student", tableName);
                // 对于非Student表，直接返回原数据，不做处理
                JSONObject result = new JSONObject();
                result.put("code", 1);
                result.put("record", record);
                System.out.println("🔄 返回原数据（非Student表）");
                return Result.OK(result);
            }
            
            // 获取密码字段
            String password = record.getString("password");
            String username = record.getString("username");
            
            System.out.println("🔑 获取到的密码: " + (StringUtils.isEmpty(password) ? "【空】" : "【有值，长度:" + password.length() + "】"));
            System.out.println("👤 获取到的用户名: " + (StringUtils.isEmpty(username) ? "【空】" : username));
            
            // 检查密码是否为空
            if (StringUtils.isEmpty(password)) {
                System.out.println("⚠️  密码字段为空，跳过加密处理");
                log.warn("密码字段为空，跳过加密处理");
                JSONObject result = new JSONObject();
                result.put("code", 1);
                result.put("record", record);
                System.out.println("🔄 返回原数据（密码为空）");
                return Result.OK(result);
            }
            
            // 检查用户名是否为空，如果为空则使用学号或email作为加密密钥
            if (StringUtils.isEmpty(username)) {
                String studentCode = record.getString("student_code");
                String email = record.getString("email");
                
                if (!StringUtils.isEmpty(studentCode)) {
                    username = studentCode;
                    record.put("username", studentCode); // 设置username为学号
                    log.info("使用学号作为用户名: {}", studentCode);
                } else if (!StringUtils.isEmpty(email)) {
                    username = email;
                    record.put("username", email); // 设置username为邮箱
                    log.info("使用邮箱作为用户名: {}", email);
                } else {
                    log.error("无法确定用户名：username、student_code、email 均为空");
                    return Result.error("密码加密失败：无法确定用户名，请确保username、student_code或email至少有一个不为空");
                }
            }
            
            // 获取当前请求的用户信息（遵循JeecgBoot最佳实践）
            HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
            String currentUser = "system"; // 默认用户
            
            // 检查当前密码是否已经是加密格式（长度检查：加密后通常是32位16进制字符串）
            // 注意：不能仅仅检查salt字段，因为用户可能修改了密码但salt依然存在
            System.out.println("🔍 密码长度: " + password.length());
            System.out.println("🔍 密码内容: " + password);
            
            // 判断是否为加密密码：
            // 1. 长度通常为32位（MD5）或更长
            // 2. 全部为16进制字符
            boolean isAlreadyEncrypted = false;
            if (password.length() >= 16 && password.matches("^[a-fA-F0-9]+$")) {
                isAlreadyEncrypted = true;
                System.out.println("🔍 密码格式判断：已加密（16进制格式，长度:" + password.length() + "）");
            } else {
                System.out.println("🔍 密码格式判断：明文（包含非16进制字符或长度不符）");
            }
            
            if (isAlreadyEncrypted) {
                System.out.println("⚠️  密码已经是加密格式，跳过加密处理");
                log.info("密码已经是加密格式，跳过加密处理");
                JSONObject result = new JSONObject();
                result.put("code", 1);
                result.put("record", record);
                System.out.println("🔄 返回原数据（密码已加密）");
                return Result.OK(result);
            }
            
            // 生成盐值（使用JeecgBoot内置方法）
            String salt = oConvertUtils.randomGen(8);
            System.out.println("🧂 生成新盐值: " + salt);
            
            // 使用JeecgBoot内置密码加密方法
            // PasswordUtil.encrypt(keyPassword, plaintext, salt) - 第一个参数是密钥，第二个是明文
            String encryptedPassword = PasswordUtil.encrypt(username, password, salt);
            System.out.println("🔐 密码加密成功!");
            System.out.println("🔐 原密码长度: " + password.length());
            System.out.println("🔐 加密后长度: " + encryptedPassword.length());
            System.out.println("🔐 加密后前20字符: " + encryptedPassword.substring(0, Math.min(20, encryptedPassword.length())) + "...");
            
            log.info("密码加密成功 - 用户名: {}, 盐值: {}", username, salt);
            
            // 更新记录数据
            record.put("password", encryptedPassword);
            record.put("salt", salt);
            System.out.println("📝 已更新记录中的password和salt字段");
            
            // 返回处理结果（遵循JeecgBoot Online增强规范Demo格式）
            JSONObject result = new JSONObject();
            result.put("code", 1); // 1表示新增/修改数据
            result.put("record", record); // 返回修改后的记录
            
            System.out.println("✅ Student密码加密处理完成!");
            System.out.println("✅ 返回结果: " + result.toJSONString());
            System.out.println("🔑 重要：采用与Demo完全一致的返回格式（不包含success字段）");
            System.out.println("========================================");
            
            log.info("=== Student密码加密处理完成 ===");
            log.info("返回结果: {}", result.toJSONString());
            
            // 重要：按照JeecgBoot Online增强Demo格式，不设置success字段
            return Result.OK(result);
            
        } catch (Exception e) {
            System.out.println("❌ Student密码加密处理异常: " + e.getMessage());
            e.printStackTrace();
            log.error("Student密码加密处理异常", e);
            return Result.error("密码加密失败: " + e.getMessage());
        }
    }
    
    /**
     * Student表密码验证接口
     * 用于验证明文密码与加密密码是否匹配
     * 
     * @param params 包含验证参数的JSON对象
     * @return Result 验证结果
     */
    @PostMapping("/verifyPassword")
    public Result<?> verifyStudentPassword(@RequestBody JSONObject params) {
        System.out.println("========================================");
        System.out.println("🔍 Student密码验证接口被调用！");
        System.out.println("🔍 调用时间: " + new java.util.Date());
        System.out.println("🔍 接收参数: " + params.toJSONString());
        System.out.println("========================================");
        
        log.info("=== Student密码验证接口被调用 ===");
        log.info("接收参数: {}", params.toJSONString());
        
        try {
            String username = params.getString("username");
            String password = params.getString("password");
            String salt = params.getString("salt");
            String encryptedPassword = params.getString("encryptedPassword");
            
            if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password) || 
                StringUtils.isEmpty(salt) || StringUtils.isEmpty(encryptedPassword)) {
                return Result.error("验证参数不完整：username、password、salt、encryptedPassword 都不能为空");
            }
            
            // 使用相同的加密方法验证密码
            String testEncrypted = PasswordUtil.encrypt(username, password, salt);
            boolean isValid = encryptedPassword.equals(testEncrypted);
            
            System.out.println("🔍 密码验证结果: " + isValid);
            log.info("密码验证结果: {}", isValid);
            
            JSONObject result = new JSONObject();
            result.put("valid", isValid);
            result.put("message", isValid ? "密码验证成功" : "密码验证失败");
            
            System.out.println("✅ 密码验证完成: " + result.toJSONString());
            System.out.println("========================================");
            
            return Result.OK(result);
            
        } catch (Exception e) {
            System.out.println("❌ Student密码验证异常: " + e.getMessage());
            e.printStackTrace();
            log.error("Student密码验证异常", e);
            return Result.error("密码验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试接口 - 确认Controller是否可达
     */
    @GetMapping("/test")
    public Result<?> testController() {
        System.out.println("🧪 测试接口被调用 - Controller工作正常！");
        return Result.OK("StudentPasswordEnhanceController 工作正常！时间: " + new java.util.Date());
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Result<?> healthCheck() {
        System.out.println("💗 健康检查接口被调用");
        JSONObject health = new JSONObject();
        health.put("status", "UP");
        health.put("controller", "StudentPasswordEnhanceController");
        health.put("timestamp", new java.util.Date());
        health.put("endpoints", new String[]{
            "/api/enhance/student/encryptPassword",
            "/api/enhance/student/verifyPassword",
            "/api/enhance/student/syncToSysUser",
            "/api/enhance/student/test",
            "/api/enhance/student/health"
        });
        return Result.OK(health);
    }

    /**
     * Student密码同步到系统用户接口
     * 解决直接复制password和salt无法登录的问题
     * 
     * @param params 包含同步参数的JSON对象
     * @return Result 同步结果
     */
    @PostMapping("/syncToSysUser")
    public Result<?> syncStudentPasswordToSysUser(@RequestBody JSONObject params) {
        System.out.println("========================================");
        System.out.println("🔄 Student密码同步到系统用户接口被调用！");
        System.out.println("🔄 调用时间: " + new java.util.Date());
        System.out.println("🔄 接收参数: " + params.toJSONString());
        System.out.println("========================================");
        
        log.info("=== Student密码同步到系统用户接口被调用 ===");
        log.info("接收参数: {}", params.toJSONString());
        
        try {
            // 获取参数
            String studentCode = params.getString("studentCode");
            String plainPassword = params.getString("plainPassword");
            String sysUsername = params.getString("sysUsername");
            
            System.out.println("📋 学号: " + studentCode);
            System.out.println("🔑 明文密码: " + (StringUtils.isEmpty(plainPassword) ? "【空】" : "【有值，长度:" + plainPassword.length() + "】"));
            System.out.println("👤 系统用户名: " + sysUsername);
            
            // 参数验证
            if (StringUtils.isEmpty(studentCode)) {
                return Result.error("学号不能为空");
            }
            if (StringUtils.isEmpty(plainPassword)) {
                return Result.error("明文密码不能为空");
            }
            if (StringUtils.isEmpty(sysUsername)) {
                return Result.error("系统用户名不能为空");
            }
            
            // 查询系统用户
            System.out.println("🔍 查询系统用户: " + sysUsername);
            SysUser sysUser = sysUserService.getUserByName(sysUsername);
            if (sysUser == null) {
                System.out.println("❌ 系统用户不存在: " + sysUsername);
                return Result.error("系统用户不存在: " + sysUsername);
            }
            
            System.out.println("✅ 找到系统用户: " + sysUser.getUsername() + " (ID: " + sysUser.getId() + ")");
            
            // 生成新的盐值
            String newSalt = oConvertUtils.randomGen(8);
            System.out.println("🧂 生成新盐值: " + newSalt);
            
            // 使用系统用户名重新加密密码
            String newEncryptedPassword = PasswordUtil.encrypt(sysUsername, plainPassword, newSalt);
            System.out.println("🔐 使用系统用户名重新加密密码");
            System.out.println("🔐 加密公式: PasswordUtil.encrypt(\"" + sysUsername + "\", \"" + plainPassword + "\", \"" + newSalt + "\")");
            System.out.println("🔐 加密结果长度: " + newEncryptedPassword.length());
            System.out.println("🔐 加密结果前20字符: " + newEncryptedPassword.substring(0, Math.min(20, newEncryptedPassword.length())) + "...");
            
            // 更新系统用户密码
            sysUser.setPassword(newEncryptedPassword);
            sysUser.setSalt(newSalt);
            
            System.out.println("💾 更新系统用户密码...");
            sysUserService.updateById(sysUser);
            System.out.println("✅ 系统用户密码更新成功！");
            
            // 返回结果
            JSONObject result = new JSONObject();
            result.put("studentCode", studentCode);
            result.put("sysUsername", sysUsername);
            result.put("newSalt", newSalt);
            result.put("encryptedPassword", newEncryptedPassword);
            result.put("message", "密码同步成功！现在可以使用明文密码 '" + plainPassword + "' 登录系统用户 '" + sysUsername + "'");
            
            System.out.println("🎉 密码同步完成！");
            System.out.println("🎉 学号: " + studentCode + " → 系统用户: " + sysUsername);
            System.out.println("🎉 现在可以使用明文密码登录系统了！");
            System.out.println("========================================");
            
            log.info("密码同步成功 - 学号: {}, 系统用户: {}, 新盐值: {}", studentCode, sysUsername, newSalt);
            
            return Result.OK(result);
            
        } catch (Exception e) {
            System.out.println("❌ 密码同步异常: " + e.getMessage());
            e.printStackTrace();
            log.error("密码同步异常", e);
            return Result.error("密码同步失败: " + e.getMessage());
        }
    }
} 