package org.jeecg.modules.demo.app_students_exam.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: 考试课程
 * @Author: jeecg-boot
 * @Date: 2023-06-01
 * @Version: V1.0
 */
@Data
@TableName("app_course")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description = "学生课程")
public class AppStudentsCourse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
    
    /**
     * 课程名称
     */
    @Excel(name = "课程名称", width = 15)
    @TableField("course_name")
    @Schema(description = "课程名称")
    private String courseName;
    
    /**
     * 分类ID
     */
    @TableField("category_id")
    private String categoryId;
    
    /**
     * 课程描述
     */
    @Excel(name = "课程描述", width = 30)
    @Schema(description = "课程描述")
    private String description;
    
    /**
     * 创建教师ID
     */
    @TableField("teacher_id")
    private String teacherId;
    
    /**
     * 封面图片
     */
    @TableField("cover_image")
    @Schema(description = "封面图片")
    private String coverImage;
    
    /**
     * 学分
     */
    private java.math.BigDecimal credit;
    
    /**
     * 状态(1-正常,0-停用)
     */
    @Schema(description = "状态(0-禁用,1-正常)")
    @Dict(dicCode = "course_status")
    private Integer status;
    
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;
    
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
    
    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateBy;
    
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;

    /**
     * 题目数量（动态统计）
     */
    @TableField(exist = false)
    private Integer questionCount;
} 