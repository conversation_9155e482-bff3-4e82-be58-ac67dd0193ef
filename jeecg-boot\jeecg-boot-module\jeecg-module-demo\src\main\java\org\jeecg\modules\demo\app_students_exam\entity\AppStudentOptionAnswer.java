package org.jeecg.modules.demo.app_students_exam.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * Entity for app_student_option_answer table.
 */
@Data
@TableName("app_student_option_answer")
public class AppStudentOptionAnswer implements Serializable {
    @TableId
    private String id;

    private String answerId;
    private String optionId;
    private Integer isSelected;
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
} 