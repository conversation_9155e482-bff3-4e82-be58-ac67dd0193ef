package org.jeecg.modules.system.enhance;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: Teacher表密码加密增强Http-api
 * @Author: AI Assistant
 * @Date: 2025-06-13
 */
@Slf4j
@RestController
@RequestMapping("/api/enhance/teacher")
public class TeacherPasswordEnhanceController {

    @Autowired
    private ISysUserService sysUserService;

    // 在类加载时输出提示信息
    static {
        System.out.println("🚀🚀🚀 TeacherPasswordEnhanceController 类已加载！");
        System.out.println("🚀 接口地址: /api/enhance/teacher/encryptPassword");
        System.out.println("🚀 接口地址: /api/enhance/teacher/verifyPassword");
        System.out.println("🚀 接口地址: /api/enhance/teacher/syncToSysUser");
    }

    /**
     * Teacher表密码加密增强接口
     * 用于Online表单开发中的Teacher表密码字段加密处理
     * 
     * @param params 包含表单数据的JSON对象
     * @return Result 处理结果
     */
    @PostMapping("/encryptPassword")
    public Result<?> encryptTeacherPassword(@RequestBody JSONObject params) {
        System.out.println("========================================");
        System.out.println("🔥 Teacher密码加密增强接口被调用！");
        System.out.println("🔥 调用时间: " + new java.util.Date());
        System.out.println("🔥 接收参数: " + params.toJSONString());
        System.out.println("========================================");
        
        log.info("=== Teacher密码加密增强接口被调用 ===");
        log.info("接收参数: {}", params.toJSONString());
        
        try {
            // 获取表名和记录数据
            String tableName = params.getString("tableName");
            JSONObject record = params.getJSONObject("record");
            
            if (record == null) {
                log.error("record参数为空");
                return Result.error("记录数据不能为空");
            }
            
            System.out.println("📋 表名: " + tableName);
            System.out.println("📋 记录数据: " + record.toJSONString());
            
            log.info("表名: {}", tableName);
            log.info("记录数据: {}", record.toJSONString());
            
            // 检查是否为Teacher表
            if (!"app_teacher".equals(tableName)) {
                System.out.println("⚠️  表名不匹配，当前表名: " + tableName + ", 期望: app_teacher");
                log.warn("表名不匹配，当前表名: {}, 期望: app_teacher", tableName);
                // 对于非Teacher表，直接返回原数据，不做处理
                JSONObject result = new JSONObject();
                result.put("code", 1);
                result.put("record", record);
                System.out.println("🔄 返回原数据（非Teacher表）");
                return Result.OK(result);
            }
            
            // 获取密码字段
            String password = record.getString("password");
            String username = record.getString("username");
            String userId = record.getString("user_id");
            
            System.out.println("🔑 获取到的密码: " + (StringUtils.isEmpty(password) ? "【空】" : "【有值，长度:" + password.length() + "】"));
            System.out.println("👤 获取到的用户名: " + (StringUtils.isEmpty(username) ? "【空】" : username));
            System.out.println("🆔 获取到的user_id: " + (StringUtils.isEmpty(userId) ? "【空】" : userId));
            
            // 检查密码是否为空
            if (StringUtils.isEmpty(password)) {
                System.out.println("⚠️  密码字段为空，跳过加密处理");
                log.warn("密码字段为空，跳过加密处理");
                JSONObject result = new JSONObject();
                result.put("code", 1);
                result.put("record", record);
                System.out.println("🔄 返回原数据（密码为空）");
                return Result.OK(result);
            }
            
            // 检查用户名是否为空，如果为空则使用教师编号或email作为加密密钥
            if (StringUtils.isEmpty(username)) {
                String teacherCodeForUsername = record.getString("teacher_code");
                String email = record.getString("email");
                
                if (!StringUtils.isEmpty(teacherCodeForUsername)) {
                    username = teacherCodeForUsername;
                    record.put("username", teacherCodeForUsername); // 设置username为教师编号
                    log.info("使用教师编号作为用户名: {}", teacherCodeForUsername);
                    System.out.println("👨‍🏫 使用教师编号作为用户名: " + teacherCodeForUsername);
                } else if (!StringUtils.isEmpty(email)) {
                    username = email;
                    record.put("username", email); // 设置username为邮箱
                    log.info("使用邮箱作为用户名: {}", email);
                    System.out.println("📧 使用邮箱作为用户名: " + email);
                } else {
                    log.error("无法确定用户名：username、teacher_code、email 均为空");
                    return Result.error("密码加密失败：无法确定用户名，请确保username、teacher_code或email至少有一个不为空");
                }
            }
            
            // 重要：始终使用teacher_code作为密码加密的密钥，确保与登录系统一致
            String encryptionKey = record.getString("teacher_code");
            if (StringUtils.isEmpty(encryptionKey)) {
                encryptionKey = username; // 如果teacher_code为空，则使用username作为备选
                log.warn("teacher_code为空，使用username作为加密密钥: {}", username);
                System.out.println("⚠️ teacher_code为空，使用username作为加密密钥: " + username);
            } else {
                log.info("使用teacher_code作为加密密钥: {}", encryptionKey);
                System.out.println("🔑 使用teacher_code作为加密密钥: " + encryptionKey);
            }
            
            // 获取当前请求的用户信息（遵循JeecgBoot最佳实践）
            HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
            String currentUser = "system"; // 默认用户
            
            // 处理user_id字段 - 解决"Field 'user_id' doesn't have a default value"错误
            if (StringUtils.isEmpty(userId)) {
                // 如果user_id为空，生成一个唯一ID
                userId = java.util.UUID.randomUUID().toString().replace("-", "");
                record.put("user_id", userId);
                System.out.println("🆔 自动生成user_id: " + userId);
                log.info("自动生成user_id: {}", userId);
            }
            
            // 处理teacher_code字段 - 确保必填字段有值（重新获取最新值）
            String teacherCode = record.getString("teacher_code");
            if (StringUtils.isEmpty(teacherCode)) {
                // 如果teacher_code为空，直接使用username或生成默认值（不加T前缀）
                if (!StringUtils.isEmpty(username)) {
                    teacherCode = username;  // 直接使用username，保持一致性
                } else {
                    teacherCode = "teacher" + System.currentTimeMillis();
                }
                record.put("teacher_code", teacherCode);
                System.out.println("👨‍🏫 自动生成teacher_code: " + teacherCode);
                log.info("自动生成teacher_code: {}", teacherCode);
            }
            
            // 检查当前密码是否已经是加密格式（长度检查：加密后通常是32位16进制字符串）
            // 注意：不能仅仅检查salt字段，因为用户可能修改了密码但salt依然存在
            System.out.println("🔍 密码长度: " + password.length());
            System.out.println("🔍 密码内容: " + password);
            
            // 判断是否为加密密码：
            // 1. 长度通常为16位以上（与学生Controller保持一致）
            // 2. 全部为16进制字符
            boolean isAlreadyEncrypted = false;
            if (password.length() >= 16 && password.matches("^[a-fA-F0-9]+$")) {
                isAlreadyEncrypted = true;
                System.out.println("🔍 密码格式判断：已加密（16进制格式，长度:" + password.length() + "）");
            } else {
                System.out.println("🔍 密码格式判断：明文（包含非16进制字符或长度不符）");
            }
            
            if (isAlreadyEncrypted) {
                System.out.println("⚠️  密码已经是加密格式，跳过加密处理");
                log.info("密码已经是加密格式，跳过加密处理");
                JSONObject result = new JSONObject();
                result.put("code", 1);
                result.put("record", record);
                System.out.println("🔄 返回原数据（密码已加密）");
                return Result.OK(result);
            }
            
            // 生成盐值（使用JeecgBoot内置方法）
            String salt = oConvertUtils.randomGen(8);
            System.out.println("🧂 生成新盐值: " + salt);
            
            // 使用JeecgBoot内置密码加密方法
            // PasswordUtil.encrypt(username, password, salt) - 与LoginController保持一致
            String encryptedPassword = PasswordUtil.encrypt(encryptionKey, password, salt);
            System.out.println("🔐 密码加密成功!");
            System.out.println("🔐 原密码长度: " + password.length());
            System.out.println("🔐 加密后长度: " + encryptedPassword.length());
            System.out.println("🔐 加密后前20字符: " + encryptedPassword.substring(0, Math.min(20, encryptedPassword.length())) + "...");
            System.out.println("🔐 加密公式: PasswordUtil.encrypt(\"" + encryptionKey + "\", \"" + password + "\", \"" + salt + "\")");
            
            log.info("密码加密成功 - 加密密钥: {}, 盐值: {}", encryptionKey, salt);
            
            // 更新记录数据
            record.put("password", encryptedPassword);
            record.put("salt", salt);
            System.out.println("📝 已更新记录中的password和salt字段");
            
            // 返回处理结果（遵循JeecgBoot Online增强规范Demo格式）
            JSONObject result = new JSONObject();
            result.put("code", 1); // 1表示新增/修改数据
            result.put("record", record); // 返回修改后的记录
            
            System.out.println("✅ Teacher密码加密处理完成!");
            System.out.println("✅ 返回结果: " + result.toJSONString());
            System.out.println("🔑 重要：采用与Demo完全一致的返回格式（不包含success字段）");
            System.out.println("========================================");
            
            log.info("=== Teacher密码加密处理完成 ===");
            log.info("返回结果: {}", result.toJSONString());
            
            // 重要：按照JeecgBoot Online增强Demo格式，不设置success字段
            return Result.OK(result);
            
        } catch (Exception e) {
            System.out.println("❌ Teacher密码加密处理异常: " + e.getMessage());
            e.printStackTrace();
            log.error("Teacher密码加密处理异常", e);
            return Result.error("密码加密失败: " + e.getMessage());
        }
    }
    
    /**
     * Teacher表密码验证接口
     * 用于验证明文密码与加密密码是否匹配
     * 
     * @param params 包含验证参数的JSON对象
     * @return Result 验证结果
     */
    @PostMapping("/verifyPassword")
    public Result<?> verifyTeacherPassword(@RequestBody JSONObject params) {
        System.out.println("========================================");
        System.out.println("🔍 Teacher密码验证接口被调用！");
        System.out.println("🔍 调用时间: " + new java.util.Date());
        System.out.println("🔍 接收参数: " + params.toJSONString());
        System.out.println("========================================");
        
        log.info("=== Teacher密码验证接口被调用 ===");
        log.info("接收参数: {}", params.toJSONString());
        
        try {
            // 获取验证参数
            String username = params.getString("username");
            String teacherCode = params.getString("teacher_code"); // 添加teacher_code参数
            String password = params.getString("password");
            String salt = params.getString("salt");
            String encryptedPassword = params.getString("encryptedPassword");
            
            System.out.println("👤 用户名: " + username);
            System.out.println("👨‍🏫 教师编号: " + teacherCode);
            System.out.println("🔑 明文密码: " + (StringUtils.isEmpty(password) ? "【空】" : "【有值，长度:" + password.length() + "】"));
            System.out.println("🧂 盐值: " + salt);
            System.out.println("🔐 加密密码: " + (StringUtils.isEmpty(encryptedPassword) ? "【空】" : "【有值，长度:" + encryptedPassword.length() + "】"));
            
            // 参数验证
            if (StringUtils.isEmpty(password) || StringUtils.isEmpty(salt) || StringUtils.isEmpty(encryptedPassword)) {
                log.error("参数不完整");
                return Result.error("参数不完整：password、salt、encryptedPassword 均不能为空");
            }
            
            // 确定加密密钥
            String encryptionKey = teacherCode;
            if (StringUtils.isEmpty(encryptionKey)) {
                encryptionKey = username; // 如果teacher_code为空，则使用username作为备选
                log.warn("teacher_code为空，使用username作为加密密钥: {}", username);
                System.out.println("⚠️ teacher_code为空，使用username作为加密密钥: " + username);
            } else {
                log.info("使用teacher_code作为加密密钥: {}", encryptionKey);
                System.out.println("🔑 使用teacher_code作为加密密钥: " + encryptionKey);
            }
            
            if (StringUtils.isEmpty(encryptionKey)) {
                log.error("无法确定加密密钥");
                return Result.error("无法确定加密密钥：teacher_code和username均为空");
            }
            
            // 使用相同的加密方法验证密码
            String calculatedPassword = PasswordUtil.encrypt(encryptionKey, password, salt);
            System.out.println("🧮 计算得到的密码: " + calculatedPassword);
            System.out.println("🔐 加密公式: PasswordUtil.encrypt(\"" + encryptionKey + "\", \"" + password + "\", \"" + salt + "\")");
            System.out.println("🔍 密码比较结果: " + (calculatedPassword.equals(encryptedPassword) ? "✅ 匹配" : "❌ 不匹配"));
            
            boolean isValid = calculatedPassword.equals(encryptedPassword);
            
            // 返回验证结果
            JSONObject result = new JSONObject();
            result.put("valid", isValid);
            result.put("message", isValid ? "密码验证成功" : "密码验证失败");
            
            System.out.println("✅ Teacher密码验证完成!");
            System.out.println("✅ 验证结果: " + (isValid ? "成功" : "失败"));
            System.out.println("========================================");
            
            log.info("=== Teacher密码验证完成 ===");
            log.info("验证结果: {}", isValid ? "成功" : "失败");
            
            return Result.OK(result);
            
        } catch (Exception e) {
            System.out.println("❌ Teacher密码验证异常: " + e.getMessage());
            e.printStackTrace();
            log.error("Teacher密码验证异常", e);
            return Result.error("密码验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 同步Teacher密码到系统用户表
     * 当Teacher表的用户名和系统用户表不一致时，可以使用此接口重新同步密码
     * 
     * @param params 包含同步参数的JSON对象
     * @return Result 同步结果
     */
    @PostMapping("/syncToSysUser")
    public Result<?> syncTeacherPasswordToSysUser(@RequestBody JSONObject params) {
        System.out.println("========================================");
        System.out.println("🔄 Teacher密码同步接口被调用！");
        System.out.println("🔄 调用时间: " + new java.util.Date());
        System.out.println("🔄 接收参数: " + params.toJSONString());
        System.out.println("========================================");
        
        log.info("=== Teacher密码同步接口被调用 ===");
        log.info("接收参数: {}", params.toJSONString());
        
        try {
            // 获取参数
            String teacherCode = params.getString("teacherCode");
            String plainPassword = params.getString("plainPassword");
            
            System.out.println("👨‍🏫 教师编号: " + teacherCode);
            System.out.println("🔑 明文密码: " + (StringUtils.isEmpty(plainPassword) ? "【空】" : "【有值，长度:" + plainPassword.length() + "】"));
            
            if (StringUtils.isEmpty(teacherCode) || StringUtils.isEmpty(plainPassword)) {
                log.error("参数不完整");
                return Result.error("参数不完整：teacherCode和plainPassword均不能为空");
            }
            
            // 根据教师编号查找对应的系统用户名
            // 假设系统用户名就是教师编号，或者需要查询映射关系
            String sysUsername = teacherCode; // 或者从数据库查询映射关系
            
            System.out.println("🔍 查找系统用户: " + sysUsername);
            
            // 查询系统用户
            SysUser sysUser = sysUserService.getUserByName(sysUsername);
            
            if (sysUser == null) {
                System.out.println("❌ 系统用户不存在: " + sysUsername);
                return Result.error("系统用户不存在: " + sysUsername);
            }
            
            System.out.println("✅ 找到系统用户: " + sysUser.getUsername() + " (ID: " + sysUser.getId() + ")");
            
            // 生成新的盐值
            String newSalt = oConvertUtils.randomGen(8);
            System.out.println("🧂 生成新盐值: " + newSalt);
            
            // 重要：使用教师编号(teacherCode)作为加密密钥，而不是系统用户名
            // 这确保了与教师表中的密码加密逻辑一致
            String encryptionKey = teacherCode;
            System.out.println("🔑 使用教师编号作为加密密钥: " + encryptionKey);
            
            // 使用教师编号重新加密密码
            String newEncryptedPassword = PasswordUtil.encrypt(encryptionKey, plainPassword, newSalt);
            System.out.println("🔐 使用教师编号重新加密密码");
            System.out.println("🔐 加密公式: PasswordUtil.encrypt(\"" + encryptionKey + "\", \"" + plainPassword + "\", \"" + newSalt + "\")");
            System.out.println("🔐 加密结果长度: " + newEncryptedPassword.length());
            System.out.println("🔐 加密结果前20字符: " + newEncryptedPassword.substring(0, Math.min(20, newEncryptedPassword.length())) + "...");
            
            // 更新系统用户密码
            sysUser.setPassword(newEncryptedPassword);
            sysUser.setSalt(newSalt);
            
            System.out.println("💾 更新系统用户密码...");
            sysUserService.updateById(sysUser);
            System.out.println("✅ 系统用户密码更新成功！");
            
            // 返回结果
            JSONObject result = new JSONObject();
            result.put("teacherCode", teacherCode);
            result.put("sysUsername", sysUsername);
            result.put("newSalt", newSalt);
            result.put("encryptedPassword", newEncryptedPassword);
            result.put("message", "密码同步成功！现在可以使用明文密码 '" + plainPassword + "' 登录系统用户 '" + sysUsername + "'");
            
            System.out.println("✅ Teacher密码同步完成!");
            System.out.println("✅ 同步结果: " + result.toJSONString());
            System.out.println("========================================");
            
            log.info("=== Teacher密码同步完成 ===");
            log.info("同步结果: {}", result.toJSONString());
            
            return Result.OK(result);
            
        } catch (Exception e) {
            System.out.println("❌ Teacher密码同步异常: " + e.getMessage());
            e.printStackTrace();
            log.error("Teacher密码同步异常", e);
            return Result.error("密码同步失败: " + e.getMessage());
        }
    }
    
    /**
     * 健康检查接口
     * 用于测试Controller是否正常工作
     * 
     * @return Result 健康状态
     */
    @GetMapping("/health")
    public Result<?> health() {
        JSONObject result = new JSONObject();
        result.put("status", "UP");
        result.put("controller", "TeacherPasswordEnhanceController");
        result.put("timestamp", new java.util.Date());
        result.put("endpoints", new String[]{
            "/api/enhance/teacher/encryptPassword",
            "/api/enhance/teacher/verifyPassword",
            "/api/enhance/teacher/syncToSysUser",
            "/api/enhance/teacher/health"
        });
        
        System.out.println("💊 TeacherPasswordEnhanceController 健康检查 - 状态正常");
        return Result.OK(result);
    }
    
    /**
     * 测试接口
     * 用于测试接口是否可达
     * 
     * @return Result 测试结果
     */
    @GetMapping("/test")
    public Result<?> test() {
        return Result.OK("TeacherPasswordEnhanceController 测试接口调用成功！当前时间: " + new java.util.Date());
    }
} 