<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JScanify - Camera Document Scanner</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 5px;
            font-weight: 300;
        }

        .main-content {
            padding: 20px;
        }

        .camera-section {
            text-align: center;
            margin-bottom: 20px;
        }

        .video-container {
            position: relative;
            display: inline-block;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }

        #video {
            width: 100%;
            max-width: 640px;
            height: auto;
            display: block;
        }

        #canvas {
            display: none;
        }

        #overlayCanvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
            min-width: 120px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn.danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .btn.success {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
            box-shadow: 0 4px 15px rgba(81, 207, 102, 0.3);
        }

        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 10px;
            text-align: center;
            font-weight: 500;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .results-section {
            display: none;
            margin-top: 30px;
            text-align: center;
        }

        .result-canvas {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin: 10px 0;
        }



        @media (max-width: 768px) {
            .header h1 {
                font-size: 1.5rem;
            }
            
            .main-content {
                padding: 15px;
            }
            
            .controls {
                gap: 10px;
            }
            
            .btn {
                padding: 10px 20px;
                font-size: 0.9rem;
                min-width: 100px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📷 Camera Document Scanner</h1>
            <p>Point your camera at a document - edges will be highlighted when detected</p>
        </div>

        <div class="main-content">
            <div id="status" class="status info">Click "Start Camera" to begin scanning</div>

            <div class="camera-section">
                <div class="video-container">
                    <video id="video" autoplay muted playsinline></video>
                    <canvas id="canvas"></canvas>
                    <canvas id="overlayCanvas"></canvas>
                </div>

                <div class="controls">
                    <button class="btn" id="startBtn" onclick="startCamera()">📷 Start Camera</button>
                    <button class="btn danger" id="stopBtn" onclick="stopCamera()" disabled>⏹️ Stop Camera</button>
                    <button class="btn success" id="captureBtn" onclick="captureDocument()" disabled>📄 Capture Document</button>
                    <button class="btn" id="downloadBtn" onclick="downloadResult()" disabled style="display: none;">💾 Download</button>
                </div>
            </div>

            <div class="results-section" id="resultsSection">
                <h3>📄 Captured Document</h3>
                <canvas id="resultCanvas" class="result-canvas"></canvas>
            </div>
        </div>
    </div>

    <script>
        let video, canvas, overlayCanvas, resultCanvas;
        let canvasCtx, overlayCtx, resultCtx;
        let scanner;
        let isScanning = false;
        let stream;
        let capturedDocument;
        let animationId;

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            console.log(message);
        }

        // Initialize scanner
        function initializeScanner() {
            updateStatus('Loading scanner libraries...', 'info');
            
            // Load OpenCV
            const script = document.createElement('script');
            script.src = 'https://docs.opencv.org/4.7.0/opencv.js';
            script.async = true;
            
            script.onload = () => {
                const checkCV = () => {
                    if (typeof cv !== 'undefined' && cv.Mat) {
                        // Load JScanify
                        const jscanifyScript = document.createElement('script');
                        jscanifyScript.src = 'https://cdn.jsdelivr.net/gh/ColonelParrot/jscanify@master/src/jscanify.min.js';
                        jscanifyScript.onload = () => {
                            try {
                                scanner = new jscanify();
                                updateStatus('✅ Scanner ready! Click "Start Camera" to begin.', 'success');
                                initializeElements();
                            } catch (error) {
                                updateStatus('❌ Failed to initialize scanner: ' + error.message, 'error');
                            }
                        };
                        jscanifyScript.onerror = () => {
                            updateStatus('❌ Failed to load JScanify library', 'error');
                        };
                        document.head.appendChild(jscanifyScript);
                    } else {
                        setTimeout(checkCV, 100);
                    }
                };
                checkCV();
            };
            
            script.onerror = () => {
                updateStatus('❌ Failed to load OpenCV library', 'error');
            };
            
            document.head.appendChild(script);
        }

        function initializeElements() {
            video = document.getElementById('video');
            canvas = document.getElementById('canvas');
            overlayCanvas = document.getElementById('overlayCanvas');
            resultCanvas = document.getElementById('resultCanvas');

            canvasCtx = canvas.getContext('2d', { willReadFrequently: true });
            overlayCtx = overlayCanvas.getContext('2d');
            resultCtx = resultCanvas.getContext('2d');
        }

        async function startCamera() {
            try {
                updateStatus('🔄 Starting camera...', 'info');
                
                const constraints = {
                    video: {
                        facingMode: 'environment', // Use back camera on mobile
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                };

                stream = await navigator.mediaDevices.getUserMedia(constraints);
                video.srcObject = stream;
                
                video.onloadedmetadata = () => {
                    console.log('Video loaded:', video.videoWidth, 'x', video.videoHeight);

                    // Set canvas dimensions to match video
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    overlayCanvas.width = video.videoWidth;
                    overlayCanvas.height = video.videoHeight;

                    isScanning = true;
                    document.getElementById('startBtn').disabled = true;
                    document.getElementById('stopBtn').disabled = false;
                    document.getElementById('captureBtn').disabled = false;

                    updateStatus('✅ Camera active! Document edges will be highlighted when detected.', 'success');

                    // Start real-time processing
                    processFrame();
                };
                
            } catch (error) {
                console.error('Camera error:', error);
                updateStatus('❌ Failed to access camera. Please check permissions.', 'error');
            }
        }

        function stopCamera() {
            isScanning = false;
            
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
            
            video.srcObject = null;
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('captureBtn').disabled = true;
            
            updateStatus('📷 Camera stopped. Click "Start Camera" to resume.', 'info');
        }

        function processFrame() {
            if (!isScanning) {
                return;
            }

            try {
                // Draw current video frame to hidden canvas for processing
                canvasCtx.drawImage(video, 0, 0, canvas.width, canvas.height);

                // Clear overlay canvas
                overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);

                // Try document detection every few frames for performance
                if (scanner && Date.now() % 5 === 0) {
                    try {
                        // Get document detection result
                        const highlightedCanvas = scanner.highlightPaper(canvas);
                        if (highlightedCanvas && highlightedCanvas.width > 0) {
                            // Extract just the edge lines from the highlighted result
                            const tempCanvas = document.createElement('canvas');
                            tempCanvas.width = highlightedCanvas.width;
                            tempCanvas.height = highlightedCanvas.height;
                            const tempCtx = tempCanvas.getContext('2d');
                            tempCtx.drawImage(highlightedCanvas, 0, 0);

                            // Get image data to find the highlighted edges
                            const imageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
                            const data = imageData.data;

                            // Draw edge lines on overlay
                            overlayCtx.strokeStyle = '#00ff00';
                            overlayCtx.lineWidth = 3;
                            overlayCtx.shadowColor = '#000000';
                            overlayCtx.shadowBlur = 2;

                            // Simple edge detection - look for green pixels (document edges)
                            overlayCtx.beginPath();
                            for (let y = 0; y < tempCanvas.height; y += 5) {
                                for (let x = 0; x < tempCanvas.width; x += 5) {
                                    const index = (y * tempCanvas.width + x) * 4;
                                    const r = data[index];
                                    const g = data[index + 1];
                                    const b = data[index + 2];

                                    // Check if this is a green edge pixel
                                    if (g > 150 && g > r + 50 && g > b + 50) {
                                        overlayCtx.fillStyle = '#00ff00';
                                        overlayCtx.fillRect(x - 1, y - 1, 3, 3);
                                    }
                                }
                            }

                            // Add corner indicators if document detected
                            overlayCtx.fillStyle = '#ff0000';
                            overlayCtx.font = '16px Arial';
                            overlayCtx.fillText('📄 Document Detected - Click Capture', 10, 30);
                        }
                    } catch (error) {
                        // Detection failed, continue without overlay
                    }
                }

            } catch (error) {
                console.error('Frame processing error:', error);
            }

            // Continue processing
            animationId = requestAnimationFrame(processFrame);
        }

        function captureDocument() {
            if (!scanner || !isScanning) {
                updateStatus('❌ Scanner not ready or camera not active.', 'error');
                return;
            }

            try {
                updateStatus('🔄 Capturing and processing document...', 'info');

                // Capture current frame
                canvasCtx.drawImage(video, 0, 0, canvas.width, canvas.height);

                // Extract document
                const paperWidth = 800;
                const paperHeight = 1000;
                capturedDocument = scanner.extractPaper(canvas, paperWidth, paperHeight);

                // Display result
                resultCanvas.width = capturedDocument.width;
                resultCanvas.height = capturedDocument.height;
                resultCtx.drawImage(capturedDocument, 0, 0);

                // Show results section
                document.getElementById('resultsSection').style.display = 'block';
                document.getElementById('downloadBtn').style.display = 'inline-block';
                document.getElementById('downloadBtn').disabled = false;

                updateStatus('✅ Document captured successfully!', 'success');

                // Scroll to results
                document.getElementById('resultsSection').scrollIntoView({
                    behavior: 'smooth'
                });

            } catch (error) {
                console.error('Capture error:', error);
                updateStatus('❌ Failed to capture document. Make sure a document is visible and try again.', 'error');
            }
        }

        function downloadResult() {
            if (!capturedDocument) {
                updateStatus('❌ No captured document to download.', 'error');
                return;
            }

            try {
                const link = document.createElement('a');
                link.download = `scanned-document-${new Date().getTime()}.png`;
                link.href = capturedDocument.toDataURL();
                link.click();
                updateStatus('✅ Document downloaded successfully!', 'success');
            } catch (error) {
                console.error('Download error:', error);
                updateStatus('❌ Failed to download document.', 'error');
            }
        }

        // Initialize when page loads
        window.addEventListener('load', () => {
            console.log('Camera scanner page loaded');
            initializeScanner();
        });

        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && isScanning) {
                // Pause scanning when page is hidden to save resources
                if (animationId) {
                    cancelAnimationFrame(animationId);
                }
            } else if (!document.hidden && isScanning) {
                // Resume scanning when page becomes visible
                processFrame();
            }
        });

        // Handle orientation changes on mobile
        window.addEventListener('orientationchange', () => {
            if (isScanning) {
                setTimeout(() => {
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    highlightCanvas.width = video.videoWidth;
                    highlightCanvas.height = video.videoHeight;
                }, 500);
            }
        });
    </script>
</body>
</html>
