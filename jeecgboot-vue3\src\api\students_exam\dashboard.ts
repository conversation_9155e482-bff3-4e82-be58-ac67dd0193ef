import { defHttp } from '/@/utils/http/axios';

enum Api {
  // 学生Dashboard相关接口
  STUDENT_EXAM_STATS = '/student/dashboard/examStats',
  STUDENT_SCORE_TREND = '/student/dashboard/scoreTrend',
  STUDENT_UPCOMING_EXAMS = '/student/dashboard/upcomingExams',
  STUDENT_NOTICES = '/student/dashboard/notices',
}

/**
 * 获取学生考试统计数据
 */
export const getStudentExamStats = () => {
  // TODO: 后续对接真实API
  return Promise.resolve({
    success: true,
    result: [
      { title: '已参加考试', value: 5, color: '#52c41a' },
      { title: '未参加考试', value: 2, color: '#faad14' },
      { title: '待批改', value: 1, color: '#1890ff' },
      { title: '已批改', value: 4, color: '#722ed1' },
    ]
  });
  // return defHttp.get({ url: Api.STUDENT_EXAM_STATS });
};

/**
 * 获取学生成绩趋势数据
 */
export const getStudentScoreTrend = () => {
  // TODO: 后续对接真实API
  return Promise.resolve({
    success: true,
    result: {
      months: ['4月', '5月', '6月', '7月', '8月', '9月'],
      scores: [78, 85, 92, 88, 95, 89]
    }
  });
  // return defHttp.get({ url: Api.STUDENT_SCORE_TREND });
};

/**
 * 获取学生待参加考试列表
 */
export const getStudentUpcomingExams = () => {
  // TODO: 后续对接真实API
  return Promise.resolve({
    success: true,
    result: [
      { id: 1, name: '数学期中考试', startTime: '2025-06-20 09:00', endTime: '2025-06-20 11:00', status: '未开始' },
      { id: 2, name: '英语口语测验', startTime: '2025-06-25 14:00', endTime: '2025-06-25 16:00', status: '未开始' },
      { id: 3, name: '物理实验考试', startTime: '2025-06-28 10:00', endTime: '2025-06-28 12:00', status: '未开始' },
    ]
  });
  // return defHttp.get({ url: Api.STUDENT_UPCOMING_EXAMS });
};

/**
 * 获取学生通知公告
 */
export const getStudentNotices = () => {
  // TODO: 后续对接真实API
  return Promise.resolve({
    success: true,
    result: [
      { title: '期末考试安排通知', date: '2025-06-10', content: '期末考试将于6月30日举行，请同学们提前做好复习准备。' },
      { title: '成绩发布通知', date: '2025-06-05', content: '上月考试成绩已发布，请及时登录系统查阅。' },
      { title: '考试规则提醒', date: '2025-06-01', content: '请严格遵守考试纪律，诚信考试。' },
    ]
  });
  // return defHttp.get({ url: Api.STUDENT_NOTICES });
}; 