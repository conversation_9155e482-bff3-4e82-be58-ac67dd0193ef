package org.jeecg.modules.demo.app_students_exam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.demo.app_students_exam.entity.AppStudentPaper;
import org.jeecg.modules.demo.app_students_exam.entity.AppStudentAnswer;
import org.jeecg.modules.demo.app_students_exam.mapper.AppStudentPaperMapper;
import org.jeecg.modules.demo.app_students_exam.mapper.AppStudentAnswerMapper;
import org.jeecg.modules.demo.app_students_exam.service.ITeacherExamReviewService;
import org.jeecg.modules.demo.app_students_exam.vo.StudentPaperDetailVO;
import org.jeecg.modules.demo.app_students_exam.vo.StudentPaperVO;
import org.jeecg.modules.demo.app_students_exam.vo.StudentAnswerQuestionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * @Description: 教师考试批改服务实现
 * @Author: JeecgBoot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Service
@Slf4j
public class TeacherExamReviewServiceImpl extends ServiceImpl<AppStudentPaperMapper, AppStudentPaper> implements ITeacherExamReviewService {

	@Autowired
	private AppStudentPaperMapper studentPaperMapper;
	
	@Autowired
	private AppStudentAnswerMapper studentAnswerMapper;
	
	@Autowired
	private JdbcTemplate jdbcTemplate;

	@Override
	public JdbcTemplate getJdbcTemplate() {
		return jdbcTemplate;
	}
	
	/**
	 * 将Object转换为Date，处理LocalDateTime的情况
	 * @param obj 日期对象
	 * @return Date对象
	 */
	private Date toDate(Object obj) {
		if (obj == null) {
			return null;
		}
		if (obj instanceof Date) {
			return (Date) obj;
		}
		if (obj instanceof LocalDateTime) {
			LocalDateTime ldt = (LocalDateTime) obj;
			return Date.from(ldt.atZone(ZoneId.systemDefault()).toInstant());
		}
		return null;
	}

	@Override
	public List<StudentPaperVO> getStudentPapersByExamId(String examId) {
		log.info("🎯 获取考试的学生试卷列表, examId: {}", examId);
		
		// 获取当前登录用户
		HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
		String username = JwtUtil.getUserNameByToken(request);
		log.info("📝 当前登录用户: {}", username);
		
		// SQL查询学生试卷列表 - 根据考试和课程的创建者来过滤权限
		String sql = "SELECT " +
				"sp.id as paperId, " +
				"sp.student_id as studentId, " +
				"u.realname as studentName, " +
				"u.username as studentUsername, " +
				"sp.exam_id as examId, " +
				"e.exam_name as examName, " +
				"e.course_id as courseId, " +
				"c.course_name as courseName, " +
				"sp.start_time as startTime, " +
				"sp.submit_time as submitTime, " +
				"sp.status, " +
				"sp.total_score as totalScore, " +
				"e.total_score as maxScore, " +
				"CASE WHEN sp.status = 2 THEN 1 ELSE 0 END as isReviewed, " +
				"sp.update_time as reviewTime, " +
				"CASE WHEN sp.status = 2 THEN ru.realname ELSE NULL END as reviewerName, " +
				"CASE WHEN sp.submit_time IS NOT NULL AND sp.start_time IS NOT NULL " +
				"     THEN GREATEST(1, TIMESTAMPDIFF(MINUTE, sp.start_time, sp.submit_time)) " +
				"     ELSE NULL END as duration " +
				"FROM app_student_paper sp " +
				"LEFT JOIN app_student s ON sp.student_id = s.id " +
				"LEFT JOIN sys_user u ON s.user_id = u.id " +
				"LEFT JOIN app_exam e ON sp.exam_id = e.id " +
				"LEFT JOIN app_course c ON e.course_id = c.id " +
				"LEFT JOIN sys_user ru ON sp.update_by = ru.username " +
				"WHERE sp.exam_id = ? " +
				"AND (e.create_by = ? OR c.create_by = ?) " +  // 根据考试创建者或课程创建者来过滤权限
				"ORDER BY sp.submit_time DESC";
		
		List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, examId, username, username);
		
		List<StudentPaperVO> paperList = new ArrayList<>();
		for (Map<String, Object> row : results) {
			StudentPaperVO vo = new StudentPaperVO();
			vo.setPaperId((String) row.get("paperId"));
			vo.setStudentId((String) row.get("studentId"));
			vo.setStudentName((String) row.get("studentName"));
			vo.setStudentUsername((String) row.get("studentUsername"));
			vo.setExamId((String) row.get("examId"));
			vo.setExamName((String) row.get("examName"));
			vo.setCourseId((String) row.get("courseId"));
			vo.setCourseName((String) row.get("courseName"));
			vo.setStartTime(toDate(row.get("startTime")));
			vo.setSubmitTime(toDate(row.get("submitTime")));
			vo.setStatus(String.valueOf(row.get("status")));
			vo.setTotalScore(row.get("totalScore") != null ? ((Number) row.get("totalScore")).doubleValue() : null);
			vo.setMaxScore(row.get("maxScore") != null ? ((Number) row.get("maxScore")).doubleValue() : null);
			vo.setIsReviewed(((Number) row.get("isReviewed")).intValue() == 1);
			vo.setReviewTime(toDate(row.get("reviewTime")));
			vo.setReviewerName((String) row.get("reviewerName"));
			vo.setDuration(row.get("duration") != null ? ((Number) row.get("duration")).intValue() : null);
			
			// 计算得分率
			if (vo.getTotalScore() != null && vo.getMaxScore() != null && vo.getMaxScore() > 0) {
				vo.setScoreRate(vo.getTotalScore() / vo.getMaxScore() * 100);
			}
			
			paperList.add(vo);
		}
		
		log.info("✅ 获取到 {} 份学生试卷", paperList.size());
		return paperList;
	}

	@Override
	public StudentPaperDetailVO getStudentPaperDetail(String paperId) {
		log.info("🎯 获取学生试卷详情, paperId: {}", paperId);
		
		StudentPaperDetailVO detailVO = new StudentPaperDetailVO();
		
		// 获取试卷基本信息
		String paperSql = "SELECT " +
				"sp.id as paperId, " +
				"sp.student_id as studentId, " +
				"u.realname as studentName, " +
				"u.username as studentUsername, " +
				"sp.exam_id as examId, " +
				"e.exam_name as examName, " +
				"e.course_id as courseId, " +
				"c.course_name as courseName, " +
				"sp.start_time as startTime, " +
				"sp.submit_time as submitTime, " +
				"sp.status, " +
				"sp.total_score as totalScore, " +
				"e.total_score as maxScore, " +
				"CASE WHEN sp.submit_time IS NOT NULL AND sp.start_time IS NOT NULL " +
				"     THEN GREATEST(1, TIMESTAMPDIFF(MINUTE, sp.start_time, sp.submit_time)) " +
				"     ELSE NULL END as duration " +
				"FROM app_student_paper sp " +
				"LEFT JOIN app_student s ON sp.student_id = s.id " +
				"LEFT JOIN sys_user u ON s.user_id = u.id " +
				"LEFT JOIN app_exam e ON sp.exam_id = e.id " +
				"LEFT JOIN app_course c ON e.course_id = c.id " +
				"WHERE sp.id = ?";
		
		Map<String, Object> paperInfo = jdbcTemplate.queryForMap(paperSql, paperId);
		
		// 设置试卷信息
		StudentPaperVO paperInfoVO = new StudentPaperVO();
		paperInfoVO.setPaperId((String) paperInfo.get("paperId"));
		paperInfoVO.setStudentId((String) paperInfo.get("studentId"));
		paperInfoVO.setStudentName((String) paperInfo.get("studentName"));
		paperInfoVO.setStudentUsername((String) paperInfo.get("studentUsername"));
		paperInfoVO.setStartTime(toDate(paperInfo.get("startTime")));
		paperInfoVO.setSubmitTime(toDate(paperInfo.get("submitTime")));
		paperInfoVO.setStatus(String.valueOf(paperInfo.get("status")));
		paperInfoVO.setTotalScore(paperInfo.get("totalScore") != null ? ((Number) paperInfo.get("totalScore")).doubleValue() : null);
		paperInfoVO.setDuration(paperInfo.get("duration") != null ? ((Number) paperInfo.get("duration")).intValue() : null);
		detailVO.setPaperInfo(paperInfoVO);
		
		// 设置考试信息
		StudentPaperDetailVO.ExamInfoVO examInfoVO = new StudentPaperDetailVO.ExamInfoVO();
		examInfoVO.setExamName((String) paperInfo.get("examName"));
		examInfoVO.setCourseName((String) paperInfo.get("courseName"));
		examInfoVO.setTotalScore(paperInfo.get("maxScore") != null ? ((Number) paperInfo.get("maxScore")).doubleValue() : null);
		detailVO.setExamInfo(examInfoVO);
		
		// 获取题目和答案信息
		String questionSql = "SELECT " +
				"sa.id as answerId, " +
				"sa.question_id as questionId, " +
				"q.question_content as questionContent, " +
				"q.question_type as questionType, " +
				"q.question_answer as correctAnswer, " +
				"q.score as maxScore, " +
				"sa.answer_content as student_answer, " +
				"sa.is_correct, " +
				"sa.score, " +
				"sa.marking_type, " +
				"sa.marking_time, " +
				"sa.marking_comment as reviewComment " +
				"FROM app_student_answer sa " +
				"LEFT JOIN app_question q ON sa.question_id = q.id " +
				"WHERE sa.paper_id = ? " +
				"ORDER BY sa.create_time";
		
		List<Map<String, Object>> questionResults = jdbcTemplate.queryForList(questionSql, paperId);
		
		List<StudentAnswerQuestionVO> questions = new ArrayList<>();
		for (Map<String, Object> row : questionResults) {
			StudentAnswerQuestionVO questionVO = new StudentAnswerQuestionVO();
			questionVO.setId((String) row.get("answerId"));
			questionVO.setQuestionId((String) row.get("questionId"));
			questionVO.setQuestion_content((String) row.get("questionContent"));
			questionVO.setTitle((String) row.get("questionContent"));
			questionVO.setQuestion_type(String.valueOf(row.get("questionType")));
			
			// 处理选项 - 查询题目选项
			List<StudentAnswerQuestionVO.QuestionOptionVO> optionList = new ArrayList<>();
			String questionId = (String) row.get("questionId");
			if (questionId != null) {
				try {
					String optionSql = "SELECT option_code, option_content, is_correct FROM app_question_option WHERE question_id = ? ORDER BY option_code";
					List<Map<String, Object>> optionResults = jdbcTemplate.queryForList(optionSql, questionId);
					
					for (Map<String, Object> optionRow : optionResults) {
						StudentAnswerQuestionVO.QuestionOptionVO optionVO = new StudentAnswerQuestionVO.QuestionOptionVO();
						optionVO.setOption_code((String) optionRow.get("option_code"));
						optionVO.setOption_content((String) optionRow.get("option_content"));
						optionVO.setIs_correct(String.valueOf(optionRow.get("is_correct")));
						optionList.add(optionVO);
					}
				} catch (Exception e) {
					log.error("查询题目选项失败: {}", e.getMessage());
				}
			}
			questionVO.setOptions(optionList);
			
			questionVO.setCorrect_answer((String) row.get("correctAnswer"));
			questionVO.setMax_score(row.get("maxScore") != null ? ((Number) row.get("maxScore")).doubleValue() : 0);
			questionVO.setStudent_answer((String) row.get("student_answer"));
			questionVO.setIs_correct(row.get("is_correct") != null && ((Number) row.get("is_correct")).intValue() == 1);
			questionVO.setScore(row.get("score") != null ? ((Number) row.get("score")).doubleValue() : 0);
			questionVO.setReview_comment((String) row.get("reviewComment"));
			questionVO.setIs_reviewed(row.get("marking_time") != null);
			
			questions.add(questionVO);
		}
		
		detailVO.setQuestions(questions);
		
		// 设置题目数量
		if (examInfoVO.getQuestionCount() == null) {
			examInfoVO.setQuestionCount(questions.size());
		}
		
		log.info("✅ 获取试卷详情成功，包含 {} 道题目", questions.size());
		return detailVO;
	}

	@Override
	public void updateQuestionScore(String paperId, String questionId, double score, double maxScore, String reviewComment) {
		log.info("🎯 更新题目分数, paperId: {}, questionId: {}, score: {}, maxScore: {}", 
				paperId, questionId, score, maxScore);
		
		// 获取当前登录用户
		HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
		String username = JwtUtil.getUserNameByToken(request);
		
		// 更新答题记录的分数和批改信息
		String updateSql = "UPDATE app_student_answer SET " +
				"score = ?, " +
				"marking_type = 1, " + // 1表示人工批改
				"marking_time = NOW(), " +
				"marking_user = ?, " +
				"marking_comment = ?, " +
				"update_by = ?, " +
				"update_time = NOW() " +
				"WHERE paper_id = ? AND question_id = ?";
		
		int rows = jdbcTemplate.update(updateSql, score, username, reviewComment, username, paperId, questionId);
		
		log.info("✅ 更新题目分数成功, 影响行数: {}", rows);
		
		// 更新试卷总分
		updatePaperScore(paperId);
	}
	
	/**
	 * 更新试卷总分（内部方法）
	 * @param paperId 试卷ID
	 */
	private void updatePaperScore(String paperId) {
		log.info("🎯 更新试卷总分, paperId: {}", paperId);
		
		// 计算试卷总分
		String scoreSql = "SELECT SUM(score) as total_score FROM app_student_answer WHERE paper_id = ?";
		Double totalScore = jdbcTemplate.queryForObject(scoreSql, Double.class, paperId);
		
		if (totalScore == null) {
			totalScore = 0.0;
		}
		
		// 更新试卷总分
		String updateSql = "UPDATE app_student_paper SET total_score = ? WHERE id = ?";
		int rows = jdbcTemplate.update(updateSql, totalScore, paperId);
		
		log.info("✅ 更新试卷总分成功, 总分: {}, 影响行数: {}", totalScore, rows);
	}

	@Override
	public void updatePaperTotalScore(String paperId, double totalScore) {
		log.info("🎯 手动更新试卷总分, paperId: {}, totalScore: {}", paperId, totalScore);
		
		// 获取当前登录用户
		HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
		String username = JwtUtil.getUserNameByToken(request);
		
		// 更新试卷总分
		String updateSql = "UPDATE app_student_paper SET " +
				"total_score = ?, " +
				"update_by = ?, " +
				"update_time = NOW() " +
				"WHERE id = ?";
		
		int rows = jdbcTemplate.update(updateSql, totalScore, username, paperId);
		
		log.info("✅ 手动更新试卷总分成功, 影响行数: {}", rows);
	}

	@Override
	public void finishReview(String paperId) {
		log.info("🎯 完成试卷批改, paperId: {}", paperId);
		
		// 获取当前登录用户
		HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
		String username = JwtUtil.getUserNameByToken(request);
		
		// 更新试卷状态为已批改(2)
		String updateSql = "UPDATE app_student_paper SET " +
				"status = '2', " + // 2表示已批改
				"update_by = ?, " +
				"update_time = NOW() " +
				"WHERE id = ?";
		
		int rows = jdbcTemplate.update(updateSql, username, paperId);
		
		log.info("✅ 完成试卷批改成功, 影响行数: {}", rows);
	}

	@Override
	public List<StudentPaperVO> getReviewedPapers(String examId) {
		log.info("🎯 获取已批改的试卷列表, examId: {}", examId);
		
		// 获取当前登录用户
		HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
		String username = JwtUtil.getUserNameByToken(request);
		
		// SQL查询已批改的学生试卷列表
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT ")
				.append("sp.id as paperId, ")
				.append("sp.student_id as studentId, ")
				.append("u.realname as studentName, ")
				.append("u.username as studentUsername, ")
				.append("sp.exam_id as examId, ")
				.append("e.exam_name as examName, ")
				.append("e.course_id as courseId, ")
				.append("c.course_name as courseName, ")
				.append("sp.start_time as startTime, ")
				.append("sp.submit_time as submitTime, ")
				.append("sp.status, ")
				.append("sp.total_score as totalScore, ")
				.append("e.total_score as maxScore, ")
				.append("sp.update_time as reviewTime, ")
				.append("ru.realname as reviewerName, ")
				.append("CASE WHEN sp.submit_time IS NOT NULL AND sp.start_time IS NOT NULL ")
				.append("     THEN GREATEST(1, TIMESTAMPDIFF(MINUTE, sp.start_time, sp.submit_time)) ")
				.append("     ELSE NULL END as duration ")
				.append("FROM app_student_paper sp ")
				.append("LEFT JOIN app_student s ON sp.student_id = s.id ")
				.append("LEFT JOIN sys_user u ON s.user_id = u.id ")
				.append("LEFT JOIN app_exam e ON sp.exam_id = e.id ")
				.append("LEFT JOIN app_course c ON e.course_id = c.id ")
				.append("LEFT JOIN sys_user ru ON sp.update_by = ru.username ")
				.append("WHERE sp.status = '2' ") // 2表示已批改
				.append("AND sp.create_by = ? "); // 只查询分配给当前教师的试卷
		
		List<Object> params = new ArrayList<>();
		params.add(username);
		
		if (examId != null && !examId.isEmpty()) {
			sql.append("AND sp.exam_id = ? ");
			params.add(examId);
		}
		
		sql.append("ORDER BY sp.update_time DESC");
		
		List<Map<String, Object>> results = jdbcTemplate.queryForList(sql.toString(), params.toArray());
		
		List<StudentPaperVO> paperList = new ArrayList<>();
		for (Map<String, Object> row : results) {
			StudentPaperVO vo = new StudentPaperVO();
			vo.setPaperId((String) row.get("paperId"));
			vo.setStudentId((String) row.get("studentId"));
			vo.setStudentName((String) row.get("studentName"));
			vo.setStudentUsername((String) row.get("studentUsername"));
			vo.setExamId((String) row.get("examId"));
			vo.setExamName((String) row.get("examName"));
			vo.setCourseId((String) row.get("courseId"));
			vo.setCourseName((String) row.get("courseName"));
			vo.setStartTime(toDate(row.get("startTime")));
			vo.setSubmitTime(toDate(row.get("submitTime")));
			vo.setStatus(String.valueOf(row.get("status")));
			vo.setTotalScore(row.get("totalScore") != null ? ((Number) row.get("totalScore")).doubleValue() : null);
			vo.setMaxScore(row.get("maxScore") != null ? ((Number) row.get("maxScore")).doubleValue() : null);
			vo.setIsReviewed(true); // 已批改
			vo.setReviewTime(toDate(row.get("reviewTime")));
			vo.setReviewerName((String) row.get("reviewerName"));
			vo.setDuration(row.get("duration") != null ? ((Number) row.get("duration")).intValue() : null);
			
			// 计算得分率
			if (vo.getTotalScore() != null && vo.getMaxScore() != null && vo.getMaxScore() > 0) {
				vo.setScoreRate(vo.getTotalScore() / vo.getMaxScore() * 100);
			}
			
			paperList.add(vo);
		}
		
		log.info("✅ 获取到 {} 份已批改的学生试卷", paperList.size());
		return paperList;
	}

	@Override
	public int batchAutoReview(String examId) {
		log.info("🎯 批量自动批改考试, examId: {}", examId);
		
		// 获取当前登录用户
		HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
		String username = JwtUtil.getUserNameByToken(request);
		
		// 查询该考试下需要批改的试卷
		String paperSql = "SELECT id FROM app_student_paper WHERE exam_id = ? AND status = '1' AND create_by = ?";
		List<String> paperIds = jdbcTemplate.queryForList(paperSql, String.class, examId, username);
		
		log.info("📝 找到 {} 份待批改试卷", paperIds.size());
		
		int successCount = 0;
		for (String paperId : paperIds) {
			try {
				// 自动批改选择题和判断题
				String updateSql = "UPDATE app_student_answer sa " +
						"JOIN app_question q ON sa.question_id = q.id " +
						"SET sa.score = CASE WHEN sa.answer_content = q.question_answer THEN q.score ELSE 0 END, " +
						"sa.is_correct = CASE WHEN sa.answer_content = q.question_answer THEN 1 ELSE 0 END, " +
						"sa.update_by = ?, " +
						"sa.update_time = NOW() " +
						"WHERE sa.paper_id = ? " +
						"AND q.question_type IN ('1', '2')"; // 1:选择题, 2:判断题
				
				int rows = jdbcTemplate.update(updateSql, username, paperId);
				log.info("📝 自动批改试卷 {}, 批改了 {} 道题目", paperId, rows);
				
				// 更新试卷总分
				updatePaperScore(paperId);
				
				// 更新试卷状态为已批改
				finishReview(paperId);
				
				successCount++;
			} catch (Exception e) {
				log.error("💥 自动批改试卷 {} 失败: {}", paperId, e.getMessage(), e);
			}
		}
		
		log.info("✅ 批量自动批改完成, 成功批改 {} 份试卷", successCount);
		return successCount;
	}

	@Override
	public Map<String, Object> getExamStatistics(String examId, String teacherUsername) {
		log.info("🎯 获取考试统计信息, examId: {}, teacherUsername: {}", examId, teacherUsername);
		
		Map<String, Object> statistics = new HashMap<>();
		
		// 获取考试基本信息
		String examSql = "SELECT " +
				"e.exam_name, " +
				"e.total_score as max_score, " +
				"c.course_name " +
				"FROM app_exam e " +
				"LEFT JOIN app_course c ON e.course_id = c.id " +
				"WHERE e.id = ?";
		
		Map<String, Object> examInfo = jdbcTemplate.queryForMap(examSql, examId);
		statistics.put("examName", examInfo.get("exam_name"));
		statistics.put("courseName", examInfo.get("course_name"));
		statistics.put("maxScore", examInfo.get("max_score"));
		
		// 使用传入的教师用户名
		String username = teacherUsername;
		
		// 获取分配给当前教师的试卷数量
		String countSql = "SELECT " +
				"COUNT(*) as total_papers, " +
				"COUNT(CASE WHEN status = '2' THEN 1 END) as reviewed_papers " +
				"FROM app_student_paper " +
				"WHERE exam_id = ? AND create_by = ?";
		
		Map<String, Object> countInfo = jdbcTemplate.queryForMap(countSql, examId, username);
		statistics.put("totalPapers", countInfo.get("total_papers"));
		statistics.put("reviewedPapers", countInfo.get("reviewed_papers"));
		
		// 计算进度
		int totalPapers = ((Number) countInfo.get("total_papers")).intValue();
		int reviewedPapers = ((Number) countInfo.get("reviewed_papers")).intValue();
		double progress = totalPapers > 0 ? (double) reviewedPapers / totalPapers * 100 : 0;
		statistics.put("progress", Math.round(progress));
		
		// 获取分数统计
		String scoreSql = "SELECT " +
				"AVG(total_score) as avg_score, " +
				"MAX(total_score) as max_score, " +
				"MIN(total_score) as min_score, " +
				"COUNT(*) as count " +
				"FROM app_student_paper " +
				"WHERE exam_id = ? AND status = '2' AND create_by = ?";
		
		Map<String, Object> scoreInfo = jdbcTemplate.queryForMap(scoreSql, examId, username);
		statistics.put("avgScore", scoreInfo.get("avg_score"));
		statistics.put("highestScore", scoreInfo.get("max_score"));
		statistics.put("lowestScore", scoreInfo.get("min_score"));
		
		// 获取分数分布
		String distributionSql = "SELECT " +
				"CASE " +
				"  WHEN total_score >= 90 THEN '优秀(90-100)' " +
				"  WHEN total_score >= 80 THEN '良好(80-89)' " +
				"  WHEN total_score >= 70 THEN '中等(70-79)' " +
				"  WHEN total_score >= 60 THEN '及格(60-69)' " +
				"  ELSE '不及格(0-59)' " +
				"END as score_range, " +
				"COUNT(*) as count " +
				"FROM app_student_paper " +
				"WHERE exam_id = ? AND status = '2' AND create_by = ? " +
				"GROUP BY score_range " +
				"ORDER BY MIN(total_score)";
		
		List<Map<String, Object>> distribution = jdbcTemplate.queryForList(distributionSql, examId, username);
		statistics.put("scoreDistribution", distribution);
		
		log.info("✅ 获取考试统计信息成功");
		return statistics;
	}
} 