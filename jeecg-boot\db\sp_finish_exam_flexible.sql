-- 更灵活的完成考试存储过程
-- 允许重新提交已提交的考试

DROP PROCEDURE IF EXISTS sp_finish_exam_flexible;

CREATE PROCEDURE sp_finish_exam_flexible(
    IN p_student_username VARCHAR(100), 
    IN p_paper_id VARCHAR(32)
)
sp_finish_exam_flexible: BEGIN
    DECLARE v_student_id, v_exam_id VARCHAR(32);
    DECLARE v_paper_status INT;
    DECLARE v_end_time, v_current_time DATETIME;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION 
    BEGIN 
        SELECT -1 as result_code, '完成考试失败：数据库错误' as message;
        ROLLBACK;
    END;
    
    SET v_current_time = NOW();
    
    START TRANSACTION;
    
    -- 验证学生用户
    SELECT s.id INTO v_student_id 
    FROM sys_user u 
    INNER JOIN app_student s ON u.id = s.user_id
    INNER JOIN sys_user_role ur ON u.id = ur.user_id
    INNER JOIN sys_role r ON ur.role_id = r.id
    WHERE u.username = p_student_username 
    AND r.role_code = 'stu' 
    AND u.status = 1 AND s.status = 1
    LIMIT 1;
    
    IF v_student_id IS NULL THEN
        SELECT -2 as result_code, '权限验证失败：非学生用户' as message;
        ROLLBACK;
        LEAVE sp_finish_exam_flexible;
    END IF;
    
    -- 验证试卷
    SELECT sp.exam_id, sp.status, e.end_time 
    INTO v_exam_id, v_paper_status, v_end_time
    FROM app_student_paper sp
    JOIN app_exam e ON sp.exam_id = e.id
    WHERE sp.id = p_paper_id 
    AND sp.student_id = v_student_id
    LIMIT 1;
    
    IF v_exam_id IS NULL THEN
        SELECT -3 as result_code, '试卷不存在或不属于该学生' as message;
        ROLLBACK;
        LEAVE sp_finish_exam_flexible;
    END IF;
    
    -- 更灵活的状态检查：允许进行中(0)和已提交(1)的考试重新提交
    IF v_paper_status NOT IN (0, 1) THEN
        SELECT -4 as result_code, '考试状态错误，只能完成进行中或已提交的考试' as message;
        ROLLBACK;
        LEAVE sp_finish_exam_flexible;
    END IF;
    
    -- 更新试卷状态为已提交，更新提交时间
    UPDATE app_student_paper 
    SET status = 1, submit_time = v_current_time 
    WHERE id = p_paper_id AND student_id = v_student_id;
    
    -- 返回适当的消息
    IF v_paper_status = 1 THEN
        SELECT 0 as result_code, '考试重新提交成功' as message;
    ELSE
        SELECT 0 as result_code, '考试完成成功' as message;
    END IF;
    
    COMMIT;
END; 