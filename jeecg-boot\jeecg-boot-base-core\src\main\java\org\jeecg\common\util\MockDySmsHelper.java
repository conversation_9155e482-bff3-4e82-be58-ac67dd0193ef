package org.jeecg.common.util;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.constant.enums.DySmsEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Mock implementation of DySmsHelper for testing purposes.
 * Prints verification codes to console instead of sending real SMS.
 */
public class MockDySmsHelper {

    private static final Logger logger = LoggerFactory.getLogger(MockDySmsHelper.class);
    
    /**
     * Mock method to simulate sending SMS verification code
     * 
     * @param phone Phone number to send verification code to
     * @param templateParamJson JSON containing the verification code and other parameters
     * @param dySmsEnum SMS template enum
     * @return Always returns true to simulate successful sending
     */
    public static boolean sendSms(String phone, JSONObject templateParamJson, DySmsEnum dySmsEnum) {
        if (templateParamJson != null && templateParamJson.containsKey("code")) {
            String verificationCode = templateParamJson.getString("code");
            
            // Print verification code to console
            System.out.println("\n=== MOCK SMS VERIFICATION CODE ===");
            System.out.println("Phone: " + phone);
            System.out.println("Verification Code: " + verificationCode);
            System.out.println("Template: " + (dySmsEnum != null ? dySmsEnum.name() : "N/A"));
            System.out.println("==================================\n");
            
            // Log the action
            logger.info("Mock SMS sent to {} with code: {}", phone, verificationCode);
        } else {
            logger.warn("No verification code found in template parameters");
        }
        
        // Always return true to simulate successful sending
        return true;
    }
    
    // Mock other methods that might be called from the original class
    public static void setAccessKeyId(String accessKeyId) {
        // No-op for mock
    }
    
    public static void setAccessKeySecret(String accessKeySecret) {
        // No-op for mock
    }
}
