import type { AppRouteModule } from '/@/router/types';
import { LAYOUT } from '/@/router/constant';

const studentsExam: AppRouteModule = {
  path: '/students-exam',
  name: 'StudentsExam',
  component: LAYOUT,
  redirect: '/students-exam/my-exams',
  meta: {
    orderNo: 100,
    icon: 'ion:school-outline',
    title: '学生考试',
  },
  children: [
    {
      path: 'my-exams',
      name: 'MyExams',
      component: () => import('/@/views/students_exam/AppStudentsExamCardList.vue'),
      meta: {
        title: '我的考试',
        icon: 'ion:list-outline',
      },
    },
    {
      path: 'test-start',
      name: 'TestStartExam',
      component: () => import('/@/views/students_exam/test-start-exam.vue'),
      meta: {
        title: '测试开始考试',
        icon: 'ion:play-outline',
      },
    },
    {
      path: 'exam/drawer',
      name: 'ExamDrawer',
      component: () => import('/@/views/students_exam/AppStudentsExamDrawer.vue'),
      meta: {
        title: '考试页面',
        hideMenu: true,
      },
    },
  ],
};

export default studentsExam; 