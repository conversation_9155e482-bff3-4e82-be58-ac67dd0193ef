package org.jeecg.config;

import com.aliyuncs.exceptions.ClientException;
import org.jeecg.common.constant.enums.DySmsEnum;
import org.jeecg.common.util.CustomSmsHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 自定义短信服务配置
 * 仅在jeecg.sms-api.enabled=true时生效
 * 
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(name = "jeecg.sms-api.enabled", havingValue = "true")
public class CustomSmsConfig {

    /**
     * 自定义短信服务实现
     * 当配置文件中启用自定义短信API时，使用此实现
     */
    public static class CustomSmsServiceImpl implements MockSmsConfig.SmsService {
        @Override
        public boolean sendSms(String phone, com.alibaba.fastjson.JSONObject templateParamJson, 
                             DySmsEnum dySmsEnum) throws ClientException {
            return CustomSmsHelper.sendSms(phone, templateParamJson, dySmsEnum);
        }
    }

    /**
     * 自定义短信服务Bean
     * 仅在jeecg.sms-api.enabled=true时创建
     */
    @Bean("customSmsService")
    public MockSmsConfig.SmsService customSmsService() {
        return new CustomSmsServiceImpl();
    }
}
