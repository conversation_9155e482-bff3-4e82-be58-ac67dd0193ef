package org.jeecg.modules.demo.app_students_exam.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: app_students_my_exams_view
 * @Author: jeecg-boot
 * @Date:   2025-05-16
 * @Version: V1.0
 */
@Data
@TableName("app_students_my_exams_view")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="app_students_my_exams_view")
public class AppStudentsMyExamsView implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private java.lang.String id;

    // 让前端直接拿到paper_id
    @com.fasterxml.jackson.annotation.JsonProperty("paper_id")
    public String getPaperId() {
        return id;
    }
	/**考试ID*/
	@Excel(name = "考试ID", width = 15)
    @Schema(description = "考试ID")
    @com.baomidou.mybatisplus.annotation.TableField("exam_id")
    private java.lang.String examId;
	/**考试名称*/
	@Excel(name = "考试名称", width = 15)
    @Schema(description = "考试名称")
    private java.lang.String examName;
	/**考试课程*/
	@Excel(name = "考试课程", width = 15)
    @Schema(description = "考试课程")
    @com.baomidou.mybatisplus.annotation.TableField("course_id")
    private java.lang.String courseId;
	/**课程名称*/
	@Excel(name = "课程名称", width = 15)
    @Schema(description = "课程名称")
    private java.lang.String courseName;
	/**学生ID*/
	@Excel(name = "学生ID", width = 15)
    @Schema(description = "学生ID")
    @com.baomidou.mybatisplus.annotation.TableField("student_id")
    private java.lang.String studentId;
	/**真实姓名*/
	@Excel(name = "真实姓名", width = 15)
    @Schema(description = "真实姓名")
    private java.lang.String realname;
	/**登录账号*/
	@Excel(name = "登录账号", width = 15)
    @Schema(description = "登录账号")
    private java.lang.String stuUsername;
	/**开始答题时间*/
	@Excel(name = "开始答题时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始答题时间")
    private java.util.Date startTime;
	/**提交时间*/
	@Excel(name = "提交时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "提交时间")
    private java.util.Date submitTime;
	/**用时(分钟)*/
	@Excel(name = "用时(分钟)", width = 15)
    @Schema(description = "用时(分钟)")
    private java.lang.Integer usedTime;
	/**总得分*/
	@Excel(name = "总得分", width = 15)
    @Schema(description = "总得分")
    private java.math.BigDecimal totalScore;
	/**状态(0-未开始,1-进行中,2-已提交,3-已批改)*/
	@Excel(name = "状态(0-未开始,1-进行中,2-已提交,3-已批改)", width = 15)
    @Schema(description = "状态(0-未开始,1-进行中,2-已提交,3-已批改)")
    private java.lang.Integer status;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
    
    // 新增考试时间相关字段
    /**考试开始时间*/
    @Excel(name = "考试开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "考试开始时间")
    @com.baomidou.mybatisplus.annotation.TableField("exam_start_time")
    private java.util.Date examStartTime;
    
    /**考试结束时间*/
    @Excel(name = "考试结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "考试结束时间")
    @com.baomidou.mybatisplus.annotation.TableField("exam_end_time")
    private java.util.Date examEndTime;
    
    /**考试时长(分钟)*/
    @Excel(name = "考试时长(分钟)", width = 15)
    @Schema(description = "考试时长(分钟)")
    @com.baomidou.mybatisplus.annotation.TableField("exam_duration")
    private java.lang.Integer examDuration;
    
    /**题目数量*/
    @Excel(name = "题目数量", width = 15)
    @Schema(description = "题目数量")
    @com.baomidou.mybatisplus.annotation.TableField("question_count")
    private java.lang.Integer questionCount;
    
    /**考试总分*/
    @Excel(name = "考试总分", width = 15)
    @Schema(description = "考试总分")
    @com.baomidou.mybatisplus.annotation.TableField("exam_total_score")
    private java.math.BigDecimal examTotalScore;
    
    /**考试描述*/
    @Excel(name = "考试描述", width = 30)
    @Schema(description = "考试描述")
    @com.baomidou.mybatisplus.annotation.TableField("exam_description")
    private java.lang.String examDescription;
}
