claude code 平替版，rovo dev agent 免费白嫖中！每天送2000万TOKEN，吊打cursor，快来体验！
https://www.youtube.com/watch?v=sY9WvHIhBGI

https://community.atlassian.com/forums/************************articles/Introducing-Rovo-Dev-CLI-AI-Powered-Development-in-your-terminal/ba-p/3043623

https://id.atlassian.com/manage-profile/security

<EMAIL>

ATATT3xFfGF02_1ydPMnvosMCtqecS6jNkyAf8H5BsYkXiycYfm4M0GyUTe_VovagQ4sRdj3mWK_3J8dDu2qQ3tv2yd738hjpb5su0-EpFTJwL4zVuisPorgKgUmwlLsS-x2NbZaUtJnjewYR0i3Gt7AsFsFVPqOlTDuKbgK4pMjTSLYGyedH74=AAF8EB41



<EMAIL>

ATATT3xFfGF0jMvgjy8GuVVo8yANV8KL7XTDQzlMB98ifSlcThOz0uaJmYXfPN0ZNhJCVmxPmIRfZZkZE-DcLl_LnwFf8t5zDOeNuT3xv88sFE64sMkxVapDSBYf4H1NKo5zmOj_I02JDvzMTKLxFLqoQxOAScYyAud63STveGrSoZbORq17nn4=3B18D604  


<EMAIL>

ATATT3xFfGF0CVSzkFd0pT1GE2mrR6V_GgwJ6URzHhFQPj2zhwstznUQwx9c6k8xx6y0A-QIPdePjwSkpw5g26uhDz9LES-pF9I5twvp81vMcU_4_cFDBEVqR1KPLYuKAloTLCKo1YfH5FEy10pFUwuuBcRdGqt3cJnVCshMWUNdGGPSO3jdXTc=BA79A9B8



https://developer.atlassian.com/cloud/acli/guides/install-windows/

https://rovodevagents-beta.atlassian.net/wiki/external/Yzc2NzI4MTk3YTBhNDdiYjkzZDhhZTc3MjE0ZmE4Y2Q



登入下面的账户需要先安装agent

https://www.atlassian.com/try/cloud/signup?bundle=devai

 

先安装Windows下的acli命令行工具

Invoke-WebRequest -Uri  https://acli.atlassian.com/windows/latest/acli_windows_amd64/acli.exe -OutFile acli.exe

也可以直接下载 https://acli.atlassian.com/windows/latest/acli_windows_amd64/acli.exe  把acli.exe放到环境变量路径方便调用

Powershell切换到自己的项目目录

.\acli.exe --help

.\acli rovodev auth login

输入上面的电子邮箱和令牌，如果错误就重试一次

登录成功后 运行

.\acli rovodev run



现在可以对话了。 比如创建一个xxxx的项目网站

第一次会要求系统权限，选择始终允许就可以了