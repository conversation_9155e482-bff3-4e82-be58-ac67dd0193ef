package org.jeecg.modules.demo.app_students_exam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.demo.app_students_exam.entity.AppStudentsMyExamsView;
import org.jeecg.modules.demo.app_students_exam.mapper.AppStudentsMyExamsViewMapper;
import org.jeecg.modules.demo.app_students_exam.service.IAppStudentsMyExamsViewService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: app_students_my_exams_view
 * @Author: jeecg-boot
 * @Date:   2025-05-16
 * @Version: V1.0
 */
@Service
@Slf4j
public class AppStudentsMyExamsViewServiceImpl extends ServiceImpl<AppStudentsMyExamsViewMapper, AppStudentsMyExamsView> implements IAppStudentsMyExamsViewService {

    @Autowired
    private org.springframework.jdbc.core.JdbcTemplate jdbcTemplate;

    @Override
    public List<AppStudentsMyExamsView> getMyExams() {
        return this.list();
    }
    
    @Override
    public List<AppStudentsMyExamsView> getMyExamsByUsername(String username) {
        log.info("🔍 开始查询用户[{}]的考试列表", username);
        
        // 首先检查视图中的所有数据
        List<AppStudentsMyExamsView> allData = this.list();
        log.info("📊 视图中总共有{}条考试记录", allData.size());
        
        // 记录所有用户名
        allData.forEach(item -> {
            log.info("📋 视图记录: 用户={}, 考试={}, 课程={}", 
                item.getStuUsername(), item.getExamName(), item.getCourseName());
        });
        
        // 使用SQL查询来过滤可见的考试：已发布的考试(status=1) 或 当前时间已超过考试开始时间
        String sql = "SELECT " +
                "    COALESCE(sp.id, CONCAT('assignment_', ea.id)) AS id, " +
                "    e.id AS exam_id, " +
                "    e.exam_name AS exam_name, " +
                "    e.course_id AS course_id, " +
                "    c.course_name AS course_name, " +
                "    s.id AS student_id, " +
                "    s.first_name AS first_name, " +
                "    s.last_name AS last_name, " +
                "    COALESCE(u.realname, CONCAT(s.first_name, ' ', s.last_name)) AS realname, " +
                "    s.username AS stu_username, " +
                "    sp.start_time AS start_time, " +
                "    sp.submit_time AS submit_time, " +
                "    COALESCE(sp.used_time, 0) AS used_time, " +
                "    COALESCE(sp.total_score, 0.00) AS total_score, " +
                "    CASE WHEN sp.id IS NOT NULL THEN sp.status ELSE -1 END AS status, " +
                "    COALESCE(sp.create_by, ea.create_by) AS create_by, " +
                "    ea.id AS assignment_id, " +
                "    sp.id AS paper_id, " +
                "    e.start_time AS exam_start_time, " +
                "    e.end_time AS exam_end_time, " +
                "    e.duration AS exam_duration, " +
                "    e.question_count AS question_count, " +
                "    e.total_score AS exam_total_score, " +
                "    e.exam_description AS exam_description " +
                "FROM app_exam_assignment ea " +
                "JOIN app_exam e ON ea.exam_id = e.id " +
                "JOIN app_course c ON e.course_id = c.id " +
                "JOIN sys_user u ON ea.student_id = u.id " +
                "JOIN app_student s ON u.username = s.username " +
                "LEFT JOIN app_student_paper sp ON ea.exam_id = sp.exam_id AND s.id = sp.student_id " +
                "WHERE s.username = ? AND (e.status = 1 OR NOW() >= e.start_time)";  // 已发布的考试 或 当前时间已超过考试开始时间
        
        List<AppStudentsMyExamsView> result = jdbcTemplate.query(sql, 
            new Object[]{username},
            (rs, rowNum) -> {
                AppStudentsMyExamsView view = new AppStudentsMyExamsView();
                view.setId(rs.getString("id"));
                view.setExamId(rs.getString("exam_id"));
                view.setExamName(rs.getString("exam_name"));
                view.setCourseId(rs.getString("course_id"));
                view.setCourseName(rs.getString("course_name"));
                view.setStudentId(rs.getString("student_id"));
                view.setRealname(rs.getString("realname"));
                view.setStuUsername(rs.getString("stu_username"));
                view.setStartTime(rs.getTimestamp("start_time"));
                view.setSubmitTime(rs.getTimestamp("submit_time"));
                
                // 处理usedTime可能为null的情况
                Long usedTimeValue = rs.getLong("used_time");
                view.setUsedTime(rs.wasNull() ? 0 : usedTimeValue.intValue());
                
                view.setTotalScore(rs.getBigDecimal("total_score"));
                
                // 处理status可能为null的情况  
                Long statusValue = rs.getLong("status");
                view.setStatus(rs.wasNull() ? -1 : statusValue.intValue());
                
                view.setCreateBy(rs.getString("create_by"));
                view.setExamStartTime(rs.getTimestamp("exam_start_time"));
                view.setExamEndTime(rs.getTimestamp("exam_end_time"));
                view.setExamDuration(rs.getInt("exam_duration"));
                view.setQuestionCount(rs.getInt("question_count"));
                view.setExamTotalScore(rs.getBigDecimal("exam_total_score"));
                view.setExamDescription(rs.getString("exam_description"));
                return view;
            });
        
        log.info("🎯 过滤后用户[{}]的可见考试数量: {} (包括已发布和已开始的考试)", username, result.size());
        
        // 记录过滤后的结果
        result.forEach(item -> {
            log.info("✅ 过滤结果: 用户={}, 考试={}, 课程={}, ID={}", 
                item.getStuUsername(), item.getExamName(), item.getCourseName(), item.getId());
        });
        
        return result;
    }
}
