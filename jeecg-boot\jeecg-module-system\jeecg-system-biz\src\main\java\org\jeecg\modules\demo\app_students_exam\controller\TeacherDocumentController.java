package org.jeecg.modules.demo.app_students_exam.controller;

import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.cloudflare.CloudflareR2Util;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 教师文档扫描控制器
 * 
 * <AUTHOR>
 * @version V4.0
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/teacher/document")
@RequiresRoles("te")  // 需要teacher角色编码 te
public class TeacherDocumentController {

    @Value("${jeecg.path.upload}")
    private String uploadPath;
    
    @Value("${jeecg.uploadType}")
    private String uploadType;

    /**
     * 上传扫描的文档
     * 
     * @param file 上传的文件
     * @param type 文档类型
     * @param format 文件格式
     * @return 上传结果
     */
    @PostMapping("/upload")
    @AutoLog(value = "教师-文档扫描上传")
    public Result<Map<String, Object>> uploadDocument(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "type", defaultValue = "document_scan") String type,
            @RequestParam(value = "format", defaultValue = "jpg") String format) {
        
        try {
            HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
            
            // 验证文件
            if (file.isEmpty()) {
                return Result.error("上传文件不能为空");
            }
            
            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return Result.error("只支持图片文件上传");
            }
            
            // 验证文件大小 (最大20MB，扫描的图片可能比较大)
            long maxSize = 20 * 1024 * 1024;
            if (file.getSize() > maxSize) {
                return Result.error("文件大小不能超过20MB");
            }
            
            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = getFileExtension(originalFilename);
            String fileName = generateFileName(type, fileExtension);
            
            // 生成存储路径 - 针对文档扫描的专用路径
            String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            String bizPath = "teacher/documents/" + datePath;
            
            String fileUrl;
            
            // 使用配置的上传类型存储文件
            if (CommonConstant.UPLOAD_TYPE_CLOUDFLARE.equals(uploadType)) {
                try {
                    // 使用CloudflareR2存储
                    fileUrl = CloudflareR2Util.upload(file, bizPath);
                    log.info("文件上传到Cloudflare R2成功: {}", fileUrl);
                } catch (Exception e) {
                    log.error("Cloudflare R2上传失败，使用本地存储", e);
                    fileUrl = uploadToLocal(file, bizPath + "/" + fileName);
                }
            } else {
                // 使用通用上传工具
                try {
                    fileUrl = CommonUtils.upload(file, bizPath, uploadType);
                    log.info("文件上传成功 ({}): {}", uploadType, fileUrl);
                } catch (Exception e) {
                    log.error("上传失败，使用本地存储", e);
                    fileUrl = uploadToLocal(file, bizPath + "/" + fileName);
                }
            }
            
            // 构造返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("url", fileUrl);
            result.put("filename", fileName);
            result.put("originalName", originalFilename);
            result.put("size", file.getSize());
            result.put("type", contentType);
            result.put("bizPath", bizPath);
            result.put("uploadTime", LocalDateTime.now());
            result.put("uploadType", uploadType);
            
            log.info("教师文档扫描上传成功: 文件名={}, 大小={}, 上传类型={}, URL={}", 
                    fileName, file.getSize(), uploadType, fileUrl);
            
            return Result.OK("上传成功", result);
            
        } catch (Exception e) {
            log.error("文档上传失败", e);
            return Result.error("上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 本地文件上传 (备用方案)
     */
    private String uploadToLocal(MultipartFile file, String relativePath) throws IOException {
        // 确保上传目录存在
        String fullPath = uploadPath + File.separator + relativePath;
        File targetFile = new File(fullPath);
        
        if (!targetFile.getParentFile().exists()) {
            targetFile.getParentFile().mkdirs();
        }
        
        // 保存文件
        file.transferTo(targetFile);
        
        // 返回访问URL
        return "/sys/common/static/" + relativePath;
    }
    
    /**
     * 生成文件名
     */
    private String generateFileName(String type, String extension) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return String.format("%s_%s_%s%s", type, timestamp, uuid, extension);
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return ".jpg";
        }
        
        int dotIndex = filename.lastIndexOf('.');
        if (dotIndex > 0 && dotIndex < filename.length() - 1) {
            return filename.substring(dotIndex);
        }
        
        return ".jpg";
    }
    
    /**
     * 获取文档列表
     * 
     * @return 文档列表
     */
    @GetMapping("/list")
    @AutoLog(value = "教师-获取文档列表")
    public Result<Object> getDocumentList(
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        
        try {
            // 这里可以实现数据库查询逻辑
            // 暂时返回空列表
            Map<String, Object> result = new HashMap<>();
            result.put("records", new Object[0]);
            result.put("total", 0);
            result.put("current", pageNo);
            result.put("size", pageSize);
            
            return Result.OK(result);
            
        } catch (Exception e) {
            log.error("获取文档列表失败", e);
            return Result.error("获取文档列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除文档
     * 
     * @param id 文档ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{id}")
    @AutoLog(value = "教师-删除文档")
    public Result<String> deleteDocument(@PathVariable String id) {
        
        try {
            // 这里可以实现文档删除逻辑
            // 1. 从数据库删除记录
            // 2. 从存储删除文件
            
            log.info("删除文档: {}", id);
            
            return Result.OK("删除成功");
            
        } catch (Exception e) {
            log.error("删除文档失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }
} 