package org.jeecg.modules.demo.app_students_exam.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 学生试卷VO
 * @Author: JeecgBoot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
public class StudentPaperVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**试卷ID*/
    private String paperId;
    
    /**学生ID*/
    private String studentId;
    
    /**学生姓名*/
    private String studentName;
    
    /**学生用户名*/
    private String studentUsername;
    
    /**考试ID*/
    private String examId;
    
    /**考试名称*/
    private String examName;
    
    /**课程ID*/
    private String courseId;
    
    /**课程名称*/
    private String courseName;
    
    /**开始时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    
    /**提交时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date submitTime;
    
    /**试卷状态*/
    private String status;
    
    /**总分*/
    private Double totalScore;
    
    /**满分*/
    private Double maxScore;
    
    /**是否已批改*/
    private Boolean isReviewed;
    
    /**批改时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;
    
    /**批改人姓名*/
    private String reviewerName;
    
    /**答题时长（分钟）*/
    private Integer duration;
    
    /**题目数量*/
    private Integer questionCount;
    
    /**正确题目数量*/
    private Integer correctCount;
    
    /**答题完成度百分比*/
    private Double completionRate;
    
    /**分数百分比*/
    private Double scoreRate;
} 