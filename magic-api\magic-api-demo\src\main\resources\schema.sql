-- Magic API 接口存储表（如果你已有旧数据，这个表可能已存在）
CREATE TABLE IF NOT EXISTS `magic_api_file` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `name` varchar(64) NOT NULL COMMENT '文件名',
  `path` varchar(512) NOT NULL COMMENT '文件路径',
  `content` mediumtext COMMENT '文件内容',
  `create_date` bigint(13) NOT NULL COMMENT '创建时间',
  `update_date` bigint(13) NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_path` (`path`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='magic-api 接口信息表';

-- 创建用户表（测试数据）
CREATE TABLE IF NOT EXISTS `users` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    `email` VARCHAR(100) COMMENT '邮箱',
    `age` INT COMMENT '年龄',
    `created_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 创建产品表（测试数据）
CREATE TABLE IF NOT EXISTS `products` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(100) NOT NULL COMMENT '产品名称',
    `price` DECIMAL(10,2) COMMENT '价格',
    `description` TEXT COMMENT '描述',
    `category` VARCHAR(50) COMMENT '分类',
    `created_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品表'; 