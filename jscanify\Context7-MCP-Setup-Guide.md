# Context7 MCP Setup Guide for Windows 10

## What is Context7 MCP?

Context7 MCP is a Model Context Protocol server that provides up-to-date code documentation for LLMs and AI code editors. It helps AI assistants give you accurate, current information about libraries and frameworks instead of outdated or hallucinated information.

## Prerequisites ✅

- ✅ Node.js >= v18.0.0 (You have v22.14.0)
- ✅ Windows 10
- An AI coding assistant (<PERSON>ursor, VS Code with Co<PERSON><PERSON>, <PERSON>, etc.)

## Installation Options

### Option 1: Cursor IDE

1. **One-Click Installation (Recommended):**
   - Click this link: [Install Context7 in Cursor](https://cursor.com/install-mcp?name=context7&config=eyJjb21tYW5kIjoibnB4IC15IEB1cHN0YXNoL2NvbnRleHQ3LW1jcCJ9)

2. **Manual Installation:**
   - Go to: `Settings` → `Cursor Settings` → `MCP` → `Add new global MCP server`
   - Use the configuration from `cursor-mcp-config.json` in this folder

### Option 2: VS Code

1. **One-Click Installation:**
   - Click: [Install in VS Code](https://insiders.vscode.dev/redirect?url=vscode%3Amcp%2Finstall%3F%7B%22name%22%3A%22context7%22%2C%22command%22%3A%22npx%22%2C%22args%22%3A%5B%22-y%22%2C%22%40upstash%2Fcontext7-mcp%40latest%22%5D%7D)

2. **Manual Installation:**
   - Create `.vscode/mcp.json` in your workspace
   - Use the configuration from `vscode-mcp-config.json` in this folder

### Option 3: Claude Desktop

Add this to your `claude_desktop_config.json` file:

```json
{
  "mcpServers": {
    "Context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp"]
    }
  }
}
```

## How to Use Context7

Once installed, you can use Context7 in your AI assistant by:

1. **Adding "use context7" to your prompts:**
   ```
   Create a basic Next.js project with app router. use context7
   ```

2. **Asking for specific library documentation:**
   ```
   Show me how to use React hooks. use context7
   ```

3. **Getting up-to-date examples:**
   ```
   Create a PostgreSQL connection script. use context7
   ```

## Available Tools

Context7 provides these tools to AI assistants:

- `resolve-library-id`: Find the correct library ID for documentation
- `get-library-docs`: Fetch up-to-date documentation for specific libraries

## Troubleshooting

### Common Issues on Windows:

1. **Module Not Found Errors:**
   - Try using `bunx` instead of `npx` in the configuration

2. **Permission Issues:**
   - Run your terminal as Administrator
   - Or use the Windows-specific configuration with `cmd /c`

3. **ESM Resolution Issues:**
   - Add `--experimental-vm-modules` flag to Node.js options

### Test Installation:

Run this command to test if Context7 is working:
```cmd
npx -y @upstash/context7-mcp@latest --help
```

## Benefits

- ✅ No more outdated code examples
- ✅ No hallucinated APIs that don't exist  
- ✅ Current documentation for any library version
- ✅ Works with multiple AI coding assistants
- ✅ No tab-switching needed

## Next Steps

1. Choose your preferred AI coding assistant
2. Follow the installation steps above
3. Start using "use context7" in your prompts
4. Enjoy up-to-date, accurate code assistance!

## Support

- 📢 Follow [@contextai](https://x.com/contextai) on X
- 🌐 Visit [context7.com](https://context7.com)
- 💬 Join the [Discord Community](https://upstash.com/discord)
- 📚 Check the [GitHub Repository](https://github.com/upstash/context7)
