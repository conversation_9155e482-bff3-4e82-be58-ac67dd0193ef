package org.jeecg.config.cloudflare;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import org.jeecg.common.util.cloudflare.CloudflareR2Util;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.jeecg.common.util.oConvertUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.net.URI;

/**
 * Cloudflare R2 存储配置
 */
@Slf4j
@Configuration
@ConditionalOnClass(CloudflareR2Util.class)
@ConditionalOnProperty(prefix = "jeecg.cloudflare", name = "enabled", havingValue = "true")
public class CloudflareR2Configuration {

    @Value("${jeecg.cloudflare.access-key}")
    private String accessKey;
    
    @Value("${jeecg.cloudflare.secret-key}")
    private String secretKey;
    
    @Value("${jeecg.cloudflare.endpoint:}")
    private String endpoint;
    
    @Value("${jeecg.cloudflare.bucket-name}")
    private String bucketName;
    
    @Value("${jeecg.cloudflare.region:auto}")
    private String region;
    
    @Value("${jeecg.cloudflare.public-url:}")
    private String publicUrl;
    
    @Value("${jeecg.cloudflare.cdn-enabled:false}")
    private boolean cdnEnabled;
    
    @Value("${jeecg.cloudflare.cdn-url:}")
    private String cdnUrl;
    
    @Value("${jeecg.cloudflare.presigned-url-expiry:604800}")
    private int presignedUrlExpiry;
    
    @Value("${jeecg.cloudflare.account-id:}")
    private String accountId;

    @Bean
    @Primary
    public AmazonS3 s3Client() {
        try {
            // 如果endpoint未设置但accountId已设置，则构建默认的endpoint
            String effectiveEndpoint = endpoint;
            if (oConvertUtils.isEmpty(effectiveEndpoint) && oConvertUtils.isNotEmpty(accountId)) {
                effectiveEndpoint = String.format("https://%s.r2.cloudflarestorage.com", accountId);
                log.info("Using default Cloudflare R2 endpoint: {}", effectiveEndpoint);
            }

            if (oConvertUtils.isEmpty(effectiveEndpoint)) {
                throw new IllegalArgumentException("Cloudflare R2 endpoint is not configured. Please set 'jeecg.cloudflare.endpoint' or 'jeecg.cloudflare.account-id'");
            }

            if (oConvertUtils.isEmpty(accessKey) || oConvertUtils.isEmpty(secretKey)) {
                throw new IllegalStateException("Cloudflare R2 access key or secret key is not configured");
            }

            // 创建AWS凭证
            BasicAWSCredentials awsCreds = new BasicAWSCredentials(accessKey, secretKey);
            
            // 创建客户端配置
            ClientConfiguration clientConfig = new ClientConfiguration()
                    .withProtocol(Protocol.HTTPS)
                    .withConnectionTimeout(10000)
                    .withSocketTimeout(30000);
            
            // 创建S3客户端
            AmazonS3ClientBuilder builder = AmazonS3ClientBuilder.standard()
                    .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                    .withClientConfiguration(clientConfig)
                    .withPathStyleAccessEnabled(true)
                    .withEndpointConfiguration(
                            new AwsClientBuilder.EndpointConfiguration(effectiveEndpoint, region)
                    );
            
            AmazonS3 client = builder.build();
            
            // Test the connection
            try {
                client.listBuckets();
                log.info("Successfully connected to Cloudflare R2");
            } catch (Exception e) {
                log.warn("Failed to list buckets, but continuing with S3 client initialization. Error: {}", e.getMessage());
            }
            
            return client;
        } catch (Exception e) {
            log.error("Failed to initialize S3 client for Cloudflare R2", e);
            throw new IllegalStateException("Failed to initialize S3 client for Cloudflare R2: " + e.getMessage(), e);
        }
    }

    @Bean
    public AmazonS3 s3Presigner() {
        // 使用与s3Client相同的配置
        return s3Client();
    }

    @Bean
    public CloudflareR2Util cloudflareR2Util() {
        // 如果publicUrl未设置但endpoint已设置，则构建默认的publicUrl
        String effectivePublicUrl = publicUrl;
        if (oConvertUtils.isEmpty(effectivePublicUrl) && oConvertUtils.isNotEmpty(endpoint)) {
            effectivePublicUrl = String.format("%s/%s", endpoint, bucketName);
            log.info("Using default Cloudflare R2 public URL: {}", effectivePublicUrl);
        }
        
        // 创建实例
        CloudflareR2Util util = new CloudflareR2Util();
        
        // 设置属性
        util.setAccessKey(accessKey);
        util.setSecretKey(secretKey);
        util.setEndpoint(endpoint);
        util.setBucketName(bucketName);
        util.setRegion(region);
        util.setPublicUrl(effectivePublicUrl);
        util.setCdnEnabled(cdnEnabled);
        util.setCdnUrl(cdnUrl);
        util.setPresignedUrlExpiry(presignedUrlExpiry);
        util.setAccountId(accountId);
        
        log.info("Cloudflare R2 configuration initialized successfully");
        return util;
    }
}
