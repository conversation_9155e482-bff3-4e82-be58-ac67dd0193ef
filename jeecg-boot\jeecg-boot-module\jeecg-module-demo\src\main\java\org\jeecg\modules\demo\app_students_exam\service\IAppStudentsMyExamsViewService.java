package org.jeecg.modules.demo.app_students_exam.service;

import org.jeecg.modules.demo.app_students_exam.entity.AppStudentsMyExamsView;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: app_students_my_exams_view
 * @Author: jeecg-boot
 * @Date:   2025-05-16
 * @Version: V1.0
 */
public interface IAppStudentsMyExamsViewService extends IService<AppStudentsMyExamsView> {
    /**
     * Get all records from app_students_my_exams_view
     */
    List<AppStudentsMyExamsView> getMyExams();
    
    /**
     * 根据用户名获取该学生的考试列表
     * @param username 学生用户名
     * @return 该学生的考试列表
     */
    List<AppStudentsMyExamsView> getMyExamsByUsername(String username);
}
