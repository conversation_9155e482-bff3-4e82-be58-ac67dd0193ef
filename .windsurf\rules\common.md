---
trigger: always_on
---

**JeeCGBoot 项目 编程规范**

**一、JeeCGBoot 框架规范与最佳实践 (Framework & Best Practices)**

1.  **统一界面风格与组件复用 (Consistent UI & Component Reuse):**
    *   优先使用项目中已有的 UI 组件，保持界面风格统一。
    *   尽量不要删除或修改已有的 UI 组件，如果必须修改，需要征得相关负责人同意。
    *   添加新的代码时，要确保代码的可读性和可维护性。要避免代码冗余，避免代码重复。
    *   创建统一的 CSS 风格，确保整体界面主题一致。
    *   追求现代化、用户友好的界面设计。
2.  **遵循框架交互与功能 (Adhere to Framework Interactions & Features):**
    *   使用框架定义的统一前后端交互方式。
    *   优先使用框架自带的工具或功能函数：
        *   **前后端通用：** 分页组件、日期处理函数、API 返回格式等。
        *   **后端特定：** 参数校验、统一返回值、日志记录、邮件调用等。
3.  **优先复用项目已有功能 (Prioritize Reusing Existing Project Functionality):**
    *   在实现新需求前，充分调研项目是否已存在可复用的模块或服务，减少重复开发。
    *   不要用 JwtUtil.getToken()，而是用 SpringContextUtils.getHttpServletRequest() 获取 request，再用 JwtUtil.getUserNameByToken(request) 获取用户名。这样做和 JeecgBoot 其他模块保持一致，兼容性和安全性更好。


**二、通用编码原则 (General Coding Principles)**

1.  **优先简单方案 (Prioritize Simplicity):**
    *   始终优先选择简单且易于理解的方案，避免过度设计。 
    *   在实现新需求前，充分调研项目是否已存在可复用的模块或服务，减少重复开发。
    *   不要用 JwtUtil.getToken()，而是用 SpringContextUtils.getHttpServletRequest() 获取 request，再用 JwtUtil.getUserNameByToken(request) 获取用户名。这样做和 JeecgBoot 其他模块保持一致，兼容性和安全性更好。

**二、通用编码原则 (General Coding Principles)**

1.  **优先简单方案 (Prioritize Simplicity):**
    *   始终优先选择简单且易于理解的方案，避免过度设计。
2.  **避免代码重复 (DRY - Don't Repeat Yourself):**
    *   尽可能避免重复的代码逻辑，使用复用性高
    *   尽可能避免重复的代码逻辑，使用复用性高的结构。
3.  **检查相似实现 (Check for Similar Implementations):**
    *   修改代码前, 检查代码库中是否存在相似功能或逻辑，避免重复实现。
4.  **谨慎修改代码 (Modify Cautiously):**
    *   仅针对明确需求进行更改, 或确保修改内容与需求强相关且已被充分理解。
 *   不要删除或修改已有的代码功能，如果必须修改，需要征得相关负责人同意。
5.  ** 任何关于数据库的内容，请使用MCP来检查确保准确性


**三、代码质量与维护 (Code Quality & Maintenance)**

1.  **修复问题时避免引入新技术/模式 (Avoid New Tech for Fixes without Cleanup):**
    *   修复问题时, 优先彻底排查现有实现的可能性。若必须引入新方案, 需同步移除旧逻辑以避免冗余。
2.  **保持代码库整洁有序 (Keep Codebase Tidy):**
    *   避免在文件中编写一次性运行的脚本 (如临时数据迁移脚本)。
    *   确保代码库结构清晰，模块划分合理。
3.  **控制单文件代码行数 (Control File Length):**
    *   当文件代码行数超过 200-300 行时，应考虑进行重构或封装，保持文件简洁易懂。

**四、环境与配置管理 (Environment & Configuration Management)**

1.  **明确区分环境 (Distinguish Environments):**
    *   明确区分开发环境 (dev)、测试环境 (test) 和生产环境 (prod)，并确保各环境配置隔离且正确。
2.  **仅测试环境使用模拟数据 (Mock Data for Test Only):**
3.  **禁止擅自覆盖配置文件 (Prohibit Unauthorized Config Overwrites):**
    *   严禁擅自覆盖 `.env`、`application.yml` (或 `.yaml`) 等配置文件。任何修改前需确认并征得相关负责人同意。


## 项目结构

- [jeecg-boot](mdc:jeecg-boot) - 后端源码（Java，SpringBoot微服务架构）
- [jeecgboot-vue3](mdc:jeecgboot-vue3) - 前端源码（Vue3+Vite6+TS最新技术栈）
- 业务功能： 在线考试教学系统。 角色： 管理员，教师，学生

# JeecgBoot前端架构

JeecgBoot前端采用Vue3 + TypeScript + Vite6 + Ant Design Vue技术栈。

## 主要目录

- [src](mdc:jeecgboot-vue3/src) - 源代码目录
  - [api](mdc:jeecgboot-vue3/src/api) - API接口定义
  - [components](mdc:jeecgboot-vue3/src/components) - 公共组件
  - [layouts](mdc:jeecgboot-vue3/src/layouts) - 布局组件
  - [router](mdc:jeecgboot-vue3/src/router) - 路由配置
  - [store](mdc:jeecgboot-vue3/src/store) - 状态管理(Pinia)
  - [views](mdc:jeecgboot-vue3/src/views) - 页面视图
  - [utils](mdc:jeecgboot-vue3/src/utils) - 工具函数

## 配置文件

- [vite.config.ts](mdc:jeecgboot-vue3/vite.config.ts) - Vite配置
- [package.json](mdc:jeecgboot-vue3/package.json) - 依赖配置
- [tsconfig.json](mdc:jeecgboot-vue3/tsconfig.json) - TypeScript配置

## 技术栈

- 框架：Vue 3.x
- 构建工具：Vite 6.x
- UI组件库：Ant Design Vue 4.x
- 状态管理：Pinia
- 类型系统：TypeScript
- 表格组件：vxe-table
- CSS：UnoCSS
- 微前端：qiankun

# JeecgBoot后端架构

JeecgBoot后端采用SpringBoot + SpringCloud Alibaba微服务架构，支持单体和微服务两种模式。

## 主要模块

- [jeecg-boot-base-core](mdc:jeecg-boot/jeecg-boot-base-core) - 核心基础包
- [jeecg-module-system](mdc:jeecg-boot/jeecg-module-system) - 系统管理模块
  - [jeecg-system-biz](mdc:jeecg-boot/jeecg-module-system/jeecg-system-biz) - 系统业务实现
  - [jeecg-system-api](mdc:jeecg-boot/jeecg-module-system/jeecg-system-api) - 系统API接口
  - [jeecg-system-start](mdc:jeecg-boot/jeecg-module-system/jeecg-system-start) - 系统启动入口
- [jeecg-server-cloud](mdc:jeecg-boot/jeecg-server-cloud) - 微服务相关模块

## 技术栈

- 基础框架：Spring Boot 2.7.x
- 微服务框架：Spring Cloud Alibaba 2021.x
- 持久层框架：MybatisPlus 3.5.x
- 安全框架：Apache Shiro + Jwt
- 数据库：MySQL/Oracle/PostgreSQL/SQLServer/MariaDB/达梦/人大金仓
- AI大模型集成：支持ChatGPT、DeepSeek等

## 启动入口

- 单体模式启动入口：[jeecg-system-start](mdc:jeecg-boot/jeecg-module-system/jeecg-system-start)
- 微服务模式：参照[jeecg-server-cloud](mdc:jeecg-boot/jeecg-server-cloud)下各个服务