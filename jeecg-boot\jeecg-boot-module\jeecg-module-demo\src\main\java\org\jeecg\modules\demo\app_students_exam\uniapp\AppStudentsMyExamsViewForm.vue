<template>
    <view>
        <!--标题和返回-->
		<cu-custom :bgColor="NavBarColor" isBack :backRouterName="backRouteName">
			<block slot="backText">返回</block>
			<block slot="content">app_students_my_exams_view</block>
		</cu-custom>
		 <!--表单区域-->
		<view>
			<form>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">考试ID：</text></view>
                  <input  placeholder="请输入考试ID" v-model="model.examId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">考试名称：</text></view>
                  <input  placeholder="请输入考试名称" v-model="model.examName"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">考试课程：</text></view>
                  <input  placeholder="请输入考试课程" v-model="model.courseId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">课程名称：</text></view>
                  <input  placeholder="请输入课程名称" v-model="model.courseName"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">学生ID：</text></view>
                  <input  placeholder="请输入学生ID" v-model="model.studentId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">真实姓名：</text></view>
                  <input  placeholder="请输入真实姓名" v-model="model.realname"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">登录账号：</text></view>
                  <input  placeholder="请输入登录账号" v-model="model.stuUsername"/>
                </view>
              </view>
              <my-date label="开始答题时间：" v-model="model.startTime" placeholder="请输入开始答题时间"></my-date>
              <my-date label="提交时间：" v-model="model.submitTime" placeholder="请输入提交时间"></my-date>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">用时(分钟)：</text></view>
                  <input type="number" placeholder="请输入用时(分钟)" v-model="model.usedTime"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">总得分：</text></view>
                  <input type="number" placeholder="请输入总得分" v-model="model.totalScore"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">状态(0-未开始,1-进行中,2-已提交,3-已批改)：</text></view>
                  <input type="number" placeholder="请输入状态(0-未开始,1-进行中,2-已提交,3-已批改)" v-model="model.status"/>
                </view>
              </view>
				<view class="padding">
					<button class="cu-btn block bg-blue margin-tb-sm lg" @click="onSubmit">
						<text v-if="loading" class="cuIcon-loading2 cuIconfont-spin"></text>提交
					</button>
				</view>
			</form>
		</view>
    </view>
</template>

<script>
    import myDate from '@/components/my-componets/my-date.vue'

    export default {
        name: "AppStudentsMyExamsViewForm",
        components:{ myDate },
        props:{
          formData:{
              type:Object,
              default:()=>{},
              required:false
          }
        },
        data(){
            return {
				CustomBar: this.CustomBar,
				NavBarColor: this.NavBarColor,
				loading:false,
                model: {},
                backRouteName:'index',
                url: {
                  queryById: "/app_students_exam/appStudentsMyExamsView/queryById",
                  add: "/app_students_exam/appStudentsMyExamsView/add",
                  edit: "/app_students_exam/appStudentsMyExamsView/edit",
                },
            }
        },
        created(){
             this.initFormData();
        },
        methods:{
           initFormData(){
               if(this.formData){
                    let dataId = this.formData.dataId;
                    this.$http.get(this.url.queryById,{params:{id:dataId}}).then((res)=>{
                        if(res.data.success){
                            console.log("表单数据",res);
                            this.model = res.data.result;
                        }
                    })
                }
            },
            onSubmit() {
                let myForm = {...this.model};
                this.loading = true;
                let url = myForm.id?this.url.edit:this.url.add;
				this.$http.post(url,myForm).then(res=>{
				   console.log("res",res)
				   this.loading = false
				   this.$Router.push({name:this.backRouteName})
				}).catch(()=>{
					this.loading = false
				});
            }
        }
    }
</script>
