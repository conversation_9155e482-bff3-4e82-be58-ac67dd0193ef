package org.jeecg.modules.demo.app_students_exam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.demo.app_students_exam.entity.AppStudentPaper;
import org.jeecg.modules.demo.app_students_exam.vo.StudentPaperDetailVO;
import org.jeecg.modules.demo.app_students_exam.vo.StudentPaperVO;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Map;

/**
 * @Description: 教师考试批改服务接口
 * @Author: JeecgBoot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface ITeacherExamReviewService extends IService<AppStudentPaper> {
	
	/**
	 * 获取JdbcTemplate
	 * @return JdbcTemplate
	 */
	JdbcTemplate getJdbcTemplate();
	
	/**
	 * 获取指定考试的所有学生试卷列表
	 * @param examId 考试ID
	 * @return 学生试卷列表
	 */
	List<StudentPaperVO> getStudentPapersByExamId(String examId);
	
	/**
	 * 获取学生试卷详情（用于批改）
	 * @param paperId 试卷ID
	 * @return 学生试卷详情
	 */
	StudentPaperDetailVO getStudentPaperDetail(String paperId);
	
	/**
	 * 更新单题分数
	 * @param paperId 试卷ID
	 * @param questionId 题目ID
	 * @param score 得分
	 * @param maxScore 满分
	 * @param reviewComment 批改备注
	 */
	void updateQuestionScore(String paperId, String questionId, double score, double maxScore, String reviewComment);
	
	/**
	 * 更新试卷总分
	 * @param paperId 试卷ID
	 * @param totalScore 总分
	 */
	void updatePaperTotalScore(String paperId, double totalScore);
	
	/**
	 * 完成批改（标记试卷为已批改状态）
	 * @param paperId 试卷ID
	 */
	void finishReview(String paperId);
	
	/**
	 * 获取已批改的试卷列表
	 * @param examId 考试ID（可选）
	 * @return 已批改的试卷列表
	 */
	List<StudentPaperVO> getReviewedPapers(String examId);
	
	/**
	 * 批量自动批改（重新运行自动批改逻辑）
	 * @param examId 考试ID
	 * @return 处理的试卷数量
	 */
	int batchAutoReview(String examId);
	
	/**
	 * 获取考试统计信息（待批改、已批改等）
	 * @param examId 考试ID
	 * @param teacherUsername 教师用户名（用于过滤权限）
	 * @return 统计信息
	 */
	Map<String, Object> getExamStatistics(String examId, String teacherUsername);
} 