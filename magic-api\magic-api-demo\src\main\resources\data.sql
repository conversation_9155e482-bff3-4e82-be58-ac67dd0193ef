-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100),
    age INT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入测试用户数据（使用INSERT IGNORE避免重复插入）
INSERT IGNORE INTO `users` (`username`, `email`, `age`) VALUES 
('张三', 'z<PERSON><PERSON>@example.com', 25),
('李四', '<EMAIL>', 30),
('王五', '<EMAIL>', 28),
('赵六', 'zhao<PERSON><EMAIL>', 35);

-- 创建产品表
CREATE TABLE IF NOT EXISTS products (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    price DECIMAL(10,2),
    description TEXT,
    category VARCHAR(50),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入测试产品数据（使用INSERT IGNORE避免重复插入）
INSERT IGNORE INTO `products` (`name`, `price`, `description`, `category`) VALUES 
('苹果', 5.99, '新鲜苹果', '水果'),
('香蕉', 3.50, '香甜香蕉', '水果'),
('笔记本电脑', 4999.00, '高性能笔记本电脑', '电子产品'),
('手机', 2999.00, '智能手机', '电子产品'); 