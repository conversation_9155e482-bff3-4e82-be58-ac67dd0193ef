package org.jeecg.modules.message.handle.impl;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.api.dto.message.MessageDTO;
import org.jeecg.common.constant.enums.DySmsEnum;
import org.jeecg.common.util.CustomSmsHelper;
import org.jeecg.modules.message.handle.ISendMsgHandle;
import org.springframework.stereotype.Component;

/**
 * 自定义短信发送实现类
 * 集成第三方短信API服务
 * 
 * <AUTHOR>
 */
@Component("customSmsSendMsgHandle")
public class CustomSmsSendMsgHandle implements ISendMsgHandle {

    @Override
    public void sendMsg(String esReceiver, String esTitle, String esContent) {
        // 尝试发送简单短信
        JSONObject templateParam = new JSONObject();
        templateParam.put("code", esContent);
        CustomSmsHelper.sendSms(esReceiver, templateParam, DySmsEnum.FORGET_PASSWORD_TEMPLATE_CODE);
    }

    @Override
    public void sendMessage(MessageDTO messageDTO) {
        // 如果需要处理MessageDTO对象，可以在这里实现
        if (messageDTO != null && messageDTO.getToUser() != null) {
            JSONObject templateParam = new JSONObject();
            templateParam.put("code", messageDTO.getContent());
            CustomSmsHelper.sendSms(messageDTO.getToUser(), templateParam, DySmsEnum.FORGET_PASSWORD_TEMPLATE_CODE);
        }
    }

    /**
     * 发送验证码短信
     * 
     * @param phone 手机号
     * @param templateParamJson 模板参数，包含验证码等信息
     * @param dySmsEnum 短信模板枚举
     * @return 是否发送成功
     */
    public boolean sendSms(String phone, JSONObject templateParamJson, DySmsEnum dySmsEnum) {
        return CustomSmsHelper.sendSms(phone, templateParamJson, dySmsEnum);
    }
}
