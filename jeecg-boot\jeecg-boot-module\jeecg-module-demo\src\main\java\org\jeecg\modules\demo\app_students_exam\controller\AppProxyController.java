package org.jeecg.modules.demo.app_students_exam.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.demo.annotation.MagicApiProxy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;

/**
 * @Description: 代理API控制器
 * @Author: jeecg-boot
 * @Date: 2025-05-26
 * @Version: V1.0
 */
@Tag(name = "代理API")
@RestController
@RequestMapping("/appProxy")
@Slf4j
public class AppProxyController {

    /**
     * 演示代理Magic-API
     * GET: http://127.0.0.1:8080/jeecg-boot/appProxy/demo/1
     * 代理到: http://localhost:9999/demo/1
     *
     * @return 代理返回的数据
     */
    @AutoLog(value = "演示代理API")
    @Operation(summary = "演示代理API", description = "演示代理到Magic API")
    @GetMapping(value = "/demo/1")
    @MagicApiProxy("/demo/1")
    public Result<?> demoProxy1() {
        // This method will be handled by the MagicApiProxyAspect
        return null;
    }

    /**
     * 演示代理Magic-API 2
     * GET: http://127.0.0.1:8080/jeecg-boot/appProxy/demo/2
     * 代理到: http://localhost:9999/demo/2
     *
     * @return 代理返回的数据
     */
    @Operation(summary = "演示代理2", description = "演示代理到Magic API")
    @GetMapping(value = "/demo/2")
    @MagicApiProxy("/demo/2")
    public Result<?> demoProxy2() {
        // This method will be handled by the MagicApiProxyAspect
        return null;
    }

    /**
     * 通用的代理方法
     * 可以通过路径参数指定要代理的路径
     * 例如: /appProxy/any/demo/3 会代理到 /demo/3
     *
     * @param path 要代理的路径
     * @return 代理返回的数据
     */
    @AutoLog(value = "通用代理API")
    @Operation(summary = "通用代理API", description = "通过路径参数代理到任意Magic API")
    @GetMapping(value = "/any/**")
    public Result<?> anyProxy(HttpServletRequest request) {
        // 获取请求的路径
        String requestURI = request.getRequestURI();
        // 提取出 /any/ 之后的部分作为代理路径
        String proxyPath = requestURI.substring(requestURI.indexOf("/any/") + 5);
        log.info("Proxying request to: {}", proxyPath);
        
        // 添加 @MagicApiProxy 注解到方法上
        // 注意：由于注解值需要是编译时常量，这里使用一个标记接口方法
        return handleProxy(proxyPath);
    }
    
    /**
     * 处理代理请求的内部方法
     */
    @MagicApiProxy("")
    private Result<?> handleProxy(String path) {
        // 这个方法体不会被执行，实际请求会被 AOP 拦截并代理
        // 所有逻辑都在 MagicApiProxyAspect 中处理
        return null;
    }
}
