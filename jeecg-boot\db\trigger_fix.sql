-- 修复后的考试计分触发器
-- 用于在考试完成时自动计算总分

DROP TRIGGER IF EXISTS tr_update_exam_score;

CREATE TRIGGER tr_update_exam_score
AFTER UPDATE ON app_student_paper
FOR EACH ROW
BEGIN
    -- 只在考试状态从进行中(0)变为已完成(1)时触发计分
    IF OLD.status = 0 AND NEW.status = 1 THEN
        -- 计算正确答案的总分
        UPDATE app_student_paper asp
        SET 
            asp.total_score = (
                SELECT COALESCE(SUM(
                    CASE 
                        WHEN saa.answer_content = aq.question_answer THEN aq.score
                        ELSE 0 
                    END
                ), 0)
                FROM app_student_answer saa
                INNER JOIN app_question aq ON saa.question_id = aq.id
                WHERE saa.paper_id = NEW.id
            ),
            asp.auto_score = (
                SELECT COALESCE(SUM(
                    CASE 
                        WHEN saa.answer_content = aq.question_answer THEN aq.score
                        ELSE 0 
                    END
                ), 0)
                FROM app_student_answer saa
                INNER JOIN app_question aq ON saa.question_id = aq.id
                WHERE saa.paper_id = NEW.id
            )
        WHERE asp.id = NEW.id;
    END IF;
END;

-- 注意：
-- 1. 此触发器使用 question_answer 字段作为标准答案
-- 2. 只对精确匹配的答案给分
-- 3. 如果需要更复杂的计分逻辑（如部分给分、选择题选项匹配等），
--    建议在Java业务层实现 