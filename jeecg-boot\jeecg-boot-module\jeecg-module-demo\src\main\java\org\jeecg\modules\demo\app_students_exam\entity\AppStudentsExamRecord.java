package org.jeecg.modules.demo.app_students_exam.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 考试记录
 * @Author: jeecg-boot
 * @Date: 2023-06-01
 * @Version: V1.0
 */
@Data
@TableName("app_students_exam_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class AppStudentsExamRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 课程ID
     */
    private String courseId;
    
    /**
     * 用户名
     */
    @Excel(name = "用户名", width = 15)
    private String username;
    
    /**
     * 提交时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提交时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;
    
    /**
     * 答案（JSON格式）
     */
    private String answers;
    
    /**
     * 得分
     */
    @Excel(name = "得分", width = 10)
    private Integer score;
    
    /**
     * 状态（0-未批改，1-已批改）
     */
    @Excel(name = "状态", width = 5)
    private Integer status;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 设置答案
     */
    public void setAnswers(List<Object> answers) {
        // 这里需要根据实际情况实现List到JSON字符串的转换
        // 可以使用JSON工具类，如Jackson或Gson
    }
} 