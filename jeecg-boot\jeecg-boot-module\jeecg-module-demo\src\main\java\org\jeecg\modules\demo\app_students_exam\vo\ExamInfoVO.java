package org.jeecg.modules.demo.app_students_exam.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * @Description: 考试信息VO
 * @Author: jeecg-boot
 * @Date: 2023-06-01
 * @Version: V1.0
 */
@Data
public class ExamInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 考试标题
     */
    private String title;
    
    /**
     * 考题列表
     */
    private List<QuestionVO> questions;
    
    /**
     * 考试时长（分钟）
     */
    private Integer duration;
    
    /**
     * 总分
     */
    private Integer totalScore;
    
    /**
     * 考试开始时间
     */
    private java.sql.Timestamp startTime;
} 