package org.jeecg.modules.demo.app_students_exam.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 考题
 * @Author: jeecg-boot
 * @Date: 2023-06-01
 * @Version: V1.0
 */
@Data
@TableName("app_question")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class AppStudentsQuestion implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 课程ID
     */
    private String courseId;
    
    /**
     * 题目标题
     */
    @Excel(name = "题目标题", width = 30)
    private String title;
    
    /**
     * 题目类型（radio-单选题，checkbox-多选题，judge-判断题，text-简答题）
     */
    @Excel(name = "题目类型", width = 10)
    private String type;
    
    /**
     * 选项（JSON格式）
     */
    private String options;
    
    /**
     * 正确答案（JSON格式）
     */
    private String answer;
    
    /**
     * 分值
     */
    @Excel(name = "分值", width = 5)
    private Integer score;
    
    /**
     * 排序号
     */
    @Excel(name = "排序号", width = 5)
    private Integer sortNo;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 获取选项列表
     */
    public List<Object> getOptions() {
        // 将JSON字符串转换为List<Object>
        if (this.options != null && !this.options.isEmpty()) {
            return JSON.parseArray(this.options);
        }
        return null;
    }
} 