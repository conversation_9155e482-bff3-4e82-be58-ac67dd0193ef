# 学生考试流程的SQL详细实现

以下是学生考试流程中各个阶段的详细SQL实现，重点关注考试进行阶段和答题过程的数据库操作。

## 1. 考试进行阶段

### 1.1 学生登录系统，查看可参加的考试

```sql
-- 学生登录验证（假设已有登录系统）
-- 设置当前登录学生ID变量
SET @student_id = '当前登录学生ID';

-- 查询当前可参加的考试列表
-- 条件：
-- 1. 考试状态为有效(status=1)
-- 2. 当前时间在考试开始和结束时间之间
-- 3. 学生未参加过该考试(左连接查询结果为NULL)
-- 4. 学生有权限参加该考试(通过班级或课程关联)
SELECT 
    e.id AS exam_id,
    e.exam_name,
    e.course_id,
    e.start_time,
    e.end_time,
    e.duration,
    e.total_score,
    e.pass_score
FROM app_exam e
LEFT JOIN app_student_paper sp ON e.id = sp.exam_id AND sp.student_id = @student_id
LEFT JOIN app_student_course sc ON e.course_id = sc.course_id AND sc.student_id = @student_id
WHERE e.status = 1 
  AND NOW() BETWEEN e.start_time AND e.end_time
  AND sp.id IS NULL
  AND sc.id IS NOT NULL
ORDER BY e.start_time;
```

### 1.2 系统在app_student_paper表中创建学生试卷记录

```sql
-- 学生选择参加某个考试，创建试卷记录
-- 设置考试ID变量
SET @exam_id = '所选考试ID';

-- 生成唯一试卷ID
SET @student_paper_id = UUID();

-- 插入学生试卷记录
INSERT INTO app_student_paper (
  id, 
  student_id, 
  exam_id, 
  start_time, 
  status, 
  create_time, 
  create_by,
  ip_address,  -- 记录IP地址，用于防作弊
  device_info  -- 记录设备信息
) VALUES (
  @student_paper_id,
  @student_id,
  @exam_id,
  NOW(), -- 开始时间
  1,     -- 状态：1-进行中
  NOW(),
  @student_id,
  '客户端IP',
  '设备信息'
);

-- 如果使用自增ID，可以这样获取刚插入的试卷ID
-- SET @student_paper_id = LAST_INSERT_ID();
```

### 1.3 记录开始时间、结束时间等信息

```sql
-- 获取考试时长信息
SELECT 
    @exam_duration := duration,
    @exam_end_time := end_time
FROM app_exam 
WHERE id = @exam_id;

-- 计算学生试卷的预计结束时间（取考试结束时间和当前时间+考试时长的较小值）
SET @expected_end_time = LEAST(
    @exam_end_time, 
    DATE_ADD(NOW(), INTERVAL @exam_duration MINUTE)
);

-- 更新试卷记录，设置预计结束时间
UPDATE app_student_paper
SET expected_end_time = @expected_end_time
WHERE id = @student_paper_id;

-- 记录学生开始考试的日志
INSERT INTO app_exam_log (
  id,
  student_id,
  exam_id,
  student_paper_id,
  operation_type,
  operation_time,
  operation_desc
) VALUES (
  UUID(),
  @student_id,
  @exam_id,
  @student_paper_id,
  'START_EXAM',
  NOW(),
  CONCAT('学生开始参加考试，预计结束时间：', @expected_end_time)
);
```

## 2. 答题过程

### 2.1 学生作答题目，系统在app_student_answer表中记录每道题的答案

```sql
-- 设置当前作答的题目ID
SET @question_id = '当前题目ID';

-- 获取题目类型
SELECT @question_type := question_type 
FROM app_question 
WHERE id = @question_id;

-- 插入学生答案记录（通用部分）
INSERT INTO app_student_answer (
  id, 
  student_paper_id, 
  question_id, 
  answer_content, 
  answer_time, 
  score, 
  status,
  question_type
) VALUES (
  UUID(), 
  @student_paper_id,
  @question_id,
  '答案内容', -- 文本类答案直接存储
  NOW(),
  NULL, -- 得分，暂时为空
  1,    -- 状态：1-已作答
  @question_type
);

-- 获取刚插入的答案ID
SET @student_answer_id = LAST_INSERT_ID(); -- 如果使用自增ID
-- 或者使用UUID方式
-- SELECT @student_answer_id := id FROM app_student_answer 
-- WHERE student_paper_id = @student_paper_id AND question_id = @question_id LIMIT 1;
```

### 2.2 对于选择题，在app_student_option_answer表中记录选择的选项

```sql
-- 单选题记录选项答案
IF @question_type = 'SINGLE_CHOICE' THEN
  -- 设置选择的选项ID
  SET @selected_option_id = '选择的选项ID';
  
  -- 记录选择的选项
  INSERT INTO app_student_option_answer (
    id, 
    student_answer_id, 
    option_id
  ) VALUES (
    UUID(),
    @student_answer_id,
    @selected_option_id
  );
  
  -- 更新答案内容（可选，用于快速查询）
  UPDATE app_student_answer
  SET answer_content = @selected_option_id
  WHERE id = @student_answer_id;
END IF;

-- 多选题记录多个选项
IF @question_type = 'MULTIPLE_CHOICE' THEN
  -- 假设前端传来的多个选项ID，以逗号分隔
  SET @selected_options = 'optionId1,optionId2,optionId3';
  
  -- 使用存储过程或应用代码循环插入每个选项
  -- 这里用SQL模拟循环插入多个选项（实际应用中可能需要在应用层处理）
  
  -- 第一个选项
  INSERT INTO app_student_option_answer (id, student_answer_id, option_id)
  VALUES (UUID(), @student_answer_id, 'optionId1');
  
  -- 第二个选项
  INSERT INTO app_student_option_answer (id, student_answer_id, option_id)
  VALUES (UUID(), @student_answer_id, 'optionId2');
  
  -- 第三个选项
  INSERT INTO app_student_option_answer (id, student_answer_id, option_id)
  VALUES (UUID(), @student_answer_id, 'optionId3');
  
  -- 更新答案内容（存储所有选项ID，用逗号分隔）
  UPDATE app_student_answer
  SET answer_content = @selected_options
  WHERE id = @student_answer_id;
END IF;
```

### 2.3 系统记录答题时间

```sql
-- 更新学生最后答题时间
UPDATE app_student_answer
SET answer_time = NOW(),
    last_modified_time = NOW()
WHERE id = @student_answer_id;

-- 更新学生试卷的最后操作时间
UPDATE app_student_paper
SET last_active_time = NOW()
WHERE id = @student_paper_id;

-- 计算已用时间（秒）
UPDATE app_student_paper
SET used_time = TIMESTAMPDIFF(SECOND, start_time, NOW())
WHERE id = @student_paper_id;

-- 记录答题日志（可选，用于详细记录学生答题行为）
INSERT INTO app_exam_log (
  id,
  student_id,
  exam_id,
  student_paper_id,
  question_id,
  operation_type,
  operation_time,
  operation_desc
) VALUES (
  UUID(),
  @student_id,
  @exam_id,
  @student_paper_id,
  @question_id,
  'ANSWER_QUESTION',
  NOW(),
  CONCAT('学生回答了题目，答题时间：', NOW())
);
```

### 2.4 自动保存功能

```sql
-- 定期自动保存学生作答进度（可由前端触发或后端定时任务执行）

-- 更新学生试卷状态和已用时间
UPDATE app_student_paper
SET 
  last_save_time = NOW(),
  used_time = TIMESTAMPDIFF(SECOND, start_time, NOW()),
  last_active_time = NOW()
WHERE id = @student_paper_id;

-- 记录自动保存日志
INSERT INTO app_exam_log (
  id,
  student_id,
  exam_id,
  student_paper_id,
  operation_type,
  operation_time,
  operation_desc
) VALUES (
  UUID(),
  @student_id,
  @exam_id,
  @student_paper_id,
  'AUTO_SAVE',
  NOW(),
  '系统自动保存学生作答进度'
);
```

### 2.5 提交试卷，完成考试

```sql
-- 学生提交试卷，更新试卷状态和结束时间
UPDATE app_student_paper
SET 
  status = 2, -- 状态：2-已完成
  end_time = NOW(),
  used_time = TIMESTAMPDIFF(SECOND, start_time, NOW()),
  submit_type = 1 -- 提交类型：1-主动提交，2-自动提交（超时）
WHERE id = @student_paper_id;

-- 对于客观题（选择题等），可以立即计算得分

-- 单选题自动评分
UPDATE app_student_answer sa
JOIN app_question q ON sa.question_id = q.id
JOIN app_student_option_answer soa ON sa.id = soa.student_answer_id
JOIN app_question_option qo ON soa.option_id = qo.id AND qo.is_correct = 1
SET 
  sa.score = q.score,
  sa.is_correct = 1
WHERE 
  sa.student_paper_id = @student_paper_id
  AND q.question_type = 'SINGLE_CHOICE';

-- 多选题自动评分（完全正确才得分）
UPDATE app_student_answer sa
SET 
  sa.score = (
    -- 如果选择的选项数量等于正确选项数量，且所有选择都是正确的，则得满分
    SELECT 
      CASE 
        WHEN COUNT(DISTINCT soa.option_id) = COUNT(DISTINCT qo_correct.id)
        AND NOT EXISTS (
          SELECT 1 FROM app_student_option_answer soa2 
          WHERE soa2.student_answer_id = sa.id
          AND NOT EXISTS (
            SELECT 1 FROM app_question_option qo 
            WHERE qo.id = soa2.option_id AND qo.is_correct = 1
          )
        )
        THEN q.score
        ELSE 0
      END
    FROM app_student_answer sa2
    JOIN app_question q ON sa2.question_id = q.id
    JOIN app_student_option_answer soa ON sa2.id = soa.student_answer_id
    JOIN app_question_option qo_correct ON q.id = qo_correct.question_id AND qo_correct.is_correct = 1
    WHERE sa2.id = sa.id
    GROUP BY sa2.id
  ),
  sa.is_correct = (
    -- 判断是否完全正确
    SELECT 
      CASE 
        WHEN COUNT(DISTINCT soa.option_id) = COUNT(DISTINCT qo_correct.id)
        AND NOT EXISTS (
          SELECT 1 FROM app_student_option_answer soa2 
          WHERE soa2.student_answer_id = sa.id
          AND NOT EXISTS (
            SELECT 1 FROM app_question_option qo 
            WHERE qo.id = soa2.option_id AND qo.is_correct = 1
          )
        )
        THEN 1
        ELSE 0
      END
    FROM app_student_answer sa2
    JOIN app_question q ON sa2.question_id = q.id
    JOIN app_student_option_answer soa ON sa2.id = soa.student_answer_id
    JOIN app_question_option qo_correct ON q.id = qo_correct.question_id AND qo_correct.is_correct = 1
    WHERE sa2.id = sa.id
    GROUP BY sa2.id
  )
WHERE 
  sa.student_paper_id = @student_paper_id
  AND sa.question_type = 'MULTIPLE_CHOICE';

-- 计算总分并更新
UPDATE app_student_paper sp
SET 
  objective_score = (
    SELECT COALESCE(SUM(sa.score), 0)
    FROM app_student_answer sa
    JOIN app_question q ON sa.question_id = q.id
    WHERE sa.student_paper_id = sp.id
    AND q.question_type IN ('SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'TRUE_FALSE')
  ),
  total_score = (
    SELECT COALESCE(SUM(sa.score), 0)
    FROM app_student_answer sa
    WHERE sa.student_paper_id = sp.id
  )
WHERE sp.id = @student_paper_id;

-- 记录提交试卷日志
INSERT INTO app_exam_log (
  id,
  student_id,
  exam_id,
  student_paper_id,
  operation_type,
  operation_time,
  operation_desc
) VALUES (
  UUID(),
  @student_id,
  @exam_id,
  @student_paper_id,
  'SUBMIT_PAPER',
  NOW(),
  CONCAT('学生提交试卷，用时：', 
         (SELECT used_time FROM app_student_paper WHERE id = @student_paper_id), 
         '秒，客观题得分：',
         (SELECT objective_score FROM app_student_paper WHERE id = @student_paper_id))
);
```

## 3. 数据库表结构说明

### 3.1 主要表结构

- **app_exam**: 考试信息表
- **app_student_paper**: 学生试卷表
- **app_question**: 题目表
- **app_question_option**: 题目选项表
- **app_student_answer**: 学生答案表
- **app_student_option_answer**: 学生选择题选项答案表
- **app_exam_log**: 考试日志表

### 3.2 关键字段说明

- **app_student_paper.status**: 试卷状态（1-进行中，2-已完成，3-已批阅）
- **app_student_answer.is_correct**: 答案是否正确（0-错误，1-正确）
- **app_question.question_type**: 题目类型（SINGLE_CHOICE-单选题，MULTIPLE_CHOICE-多选题，TRUE_FALSE-判断题，SHORT_ANSWER-简答题等）
- **app_question_option.is_correct**: 选项是否正确（0-错误选项，1-正确选项）

以上SQL实现涵盖了学生考试流程中的主要数据库操作，包括查询可参加考试、创建试卷记录、记录答案和选项、更新答题时间以及提交试卷等关键步骤。