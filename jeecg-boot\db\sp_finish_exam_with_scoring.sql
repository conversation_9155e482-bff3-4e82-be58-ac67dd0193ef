DELIMITER ;;

DROP PROCEDURE IF EXISTS sp_finish_exam_with_scoring;;

CREATE DEFINER=`root`@`localhost` PROCEDURE `sp_finish_exam_with_scoring`(
    IN p_student_username VARCHAR(100), 
    IN p_paper_id VARCHAR(32)
)
sp_finish_exam: BEGIN
    DECLARE v_student_id, v_exam_id VARCHAR(32);
    DECLARE v_paper_status INT;
    DECLARE v_end_time, v_current_time DATETIME;
    DECLARE v_total_score DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_total_questions INT DEFAULT 0;
    DECLARE v_auto_graded_questions INT DEFAULT 0;
    DECLARE v_final_status INT DEFAULT 2; -- 默认为已提交状态
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION 
    BEGIN 
        SELECT -1 as result_code, '完成考试失败：数据库错误' as message;
        ROLLBACK;
    END;
    
    SET v_current_time = NOW();
    
    START TRANSACTION;
    
    -- 验证学生用户
    SELECT s.id INTO v_student_id 
    FROM sys_user u 
    INNER JOIN app_student s ON u.id = s.user_id
    INNER JOIN sys_user_role ur ON u.id = ur.user_id
    INNER JOIN sys_role r ON ur.role_id = r.id
    WHERE u.username = p_student_username 
    AND r.role_code = 'stu' 
    AND u.status = 1 AND s.status = 1
    LIMIT 1;
    
    IF v_student_id IS NULL THEN
        SELECT -2 as result_code, '权限验证失败：非学生用户' as message;
        ROLLBACK;
        LEAVE sp_finish_exam;
    END IF;
    
    -- 验证试卷
    SELECT sp.exam_id, sp.status, e.end_time 
    INTO v_exam_id, v_paper_status, v_end_time
    FROM app_student_paper sp
    JOIN app_exam e ON sp.exam_id = e.id
    WHERE sp.id = p_paper_id 
    AND sp.student_id = v_student_id
    LIMIT 1;
    
    IF v_exam_id IS NULL THEN
        SELECT -3 as result_code, '试卷不存在或不属于该学生' as message;
        ROLLBACK;
        LEAVE sp_finish_exam;
    END IF;
    
    IF v_paper_status != 0 THEN
        SELECT -4 as result_code, '考试状态错误，只能完成进行中的考试' as message;
        ROLLBACK;
        LEAVE sp_finish_exam;
    END IF;
    
    -- 自动评分逻辑（添加了对题目正确答案有效性的检查）
    UPDATE app_student_answer sa
    JOIN app_question q ON sa.question_id = q.id
    LEFT JOIN app_exam_question eq ON eq.exam_id = v_exam_id AND eq.course_id = q.id
    SET 
        sa.max_score = COALESCE(eq.exam_score, q.score, 5.00),
        sa.is_correct = CASE 
            -- 单选题和多选题：需要有有效答案才能自动批改
            WHEN q.question_type IN (1,2) AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' THEN
                CASE WHEN TRIM(LOWER(sa.answer_content)) = TRIM(LOWER(q.question_answer)) THEN 1 ELSE 0 END
            -- 判断题：需要有有效答案才能自动批改
            WHEN q.question_type = 3 AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' THEN
                CASE WHEN TRIM(LOWER(sa.answer_content)) = TRIM(LOWER(q.question_answer)) THEN 1 ELSE 0 END
            -- 填空题：需要有有效答案才能自动批改
            WHEN q.question_type = 4 AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' THEN
                CASE 
                    -- 回答是空白：错误
                    WHEN TRIM(COALESCE(sa.answer_content, '')) = '' THEN 0
                    -- 回答和答案一样（不区分大小写）：正确
                    WHEN TRIM(LOWER(sa.answer_content)) = TRIM(LOWER(q.question_answer)) THEN 1
                    -- 回答有内容但和答案不一样：保持为NULL，等待人工批改
                    ELSE NULL
                END
            -- 简答题：需要有有效答案才能自动批改
            WHEN q.question_type = 5 AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' THEN
                CASE 
                    -- 回答是空白：错误
                    WHEN TRIM(COALESCE(sa.answer_content, '')) = '' THEN 0
                    -- 回答和答案完全一样（不区分大小写）：正确
                    WHEN TRIM(LOWER(sa.answer_content)) = TRIM(LOWER(q.question_answer)) THEN 1
                    -- 回答有内容但和答案不完全一样：保持为NULL，等待人工批改
                    ELSE NULL
                END
            -- 没有有效答案的题目：不自动批改
            ELSE NULL
        END,
        sa.score = CASE 
            -- 单选题和多选题：需要有有效答案才能自动给分
            WHEN q.question_type IN (1,2) AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' THEN
                CASE WHEN TRIM(LOWER(sa.answer_content)) = TRIM(LOWER(q.question_answer)) 
                     THEN COALESCE(eq.exam_score, q.score, 5.00) 
                     ELSE 0.00 
                END
            -- 判断题：需要有有效答案才能自动给分
            WHEN q.question_type = 3 AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' THEN
                CASE WHEN TRIM(LOWER(sa.answer_content)) = TRIM(LOWER(q.question_answer)) 
                     THEN COALESCE(eq.exam_score, q.score, 5.00) 
                     ELSE 0.00 
                END
            -- 填空题：需要有有效答案才能自动给分
            WHEN q.question_type = 4 AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' THEN
                CASE 
                    -- 回答是空白：0分
                    WHEN TRIM(COALESCE(sa.answer_content, '')) = '' THEN 0.00
                    -- 回答和答案一样（不区分大小写）：满分
                    WHEN TRIM(LOWER(sa.answer_content)) = TRIM(LOWER(q.question_answer)) 
                         THEN COALESCE(eq.exam_score, q.score, 5.00)
                    -- 回答有内容但和答案不一样：保持为NULL，等待人工批改
                    ELSE NULL
                END
            -- 简答题：需要有有效答案才能自动给分
            WHEN q.question_type = 5 AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' THEN
                CASE 
                    -- 回答是空白：0分
                    WHEN TRIM(COALESCE(sa.answer_content, '')) = '' THEN 0.00
                    -- 回答和答案完全一样（不区分大小写）：满分
                    WHEN TRIM(LOWER(sa.answer_content)) = TRIM(LOWER(q.question_answer)) 
                         THEN COALESCE(eq.exam_score, q.score, 5.00)
                    -- 回答有内容但和答案不完全一样：保持为NULL，等待人工批改
                    ELSE NULL
                END
            -- 没有有效答案的题目：不给分，等待人工批改
            ELSE NULL
        END,
        sa.marking_type = CASE 
            -- 单选题和多选题：需要有有效答案才能自动评分
            WHEN q.question_type IN (1,2) AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' THEN 1
            -- 判断题：需要有有效答案才能自动评分
            WHEN q.question_type = 3 AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' THEN 1
            -- 填空题：根据情况决定
            WHEN q.question_type = 4 AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' THEN
                CASE 
                    -- 回答是空白 或 回答和答案完全一样：自动评分
                    WHEN TRIM(COALESCE(sa.answer_content, '')) = '' 
                         OR TRIM(LOWER(sa.answer_content)) = TRIM(LOWER(q.question_answer)) THEN 1
                    -- 回答有内容但和答案不一样：待人工评分
                    ELSE 0
                END
            -- 简答题：根据情况决定
            WHEN q.question_type = 5 AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' THEN
                CASE 
                    -- 回答是空白 或 回答和答案完全一样：自动评分
                    WHEN TRIM(COALESCE(sa.answer_content, '')) = '' 
                         OR TRIM(LOWER(sa.answer_content)) = TRIM(LOWER(q.question_answer)) THEN 1
                    -- 回答有内容但和答案不完全一样：待人工评分
                    ELSE 0
                END
            -- 没有有效答案的题目：待人工评分
            ELSE 0
        END,
        sa.marking_time = CASE 
            -- 单选题和多选题：需要有有效答案才能设置批改时间
            WHEN q.question_type IN (1,2) AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' THEN v_current_time
            -- 判断题：需要有有效答案才能设置批改时间
            WHEN q.question_type = 3 AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' THEN v_current_time
            -- 填空题：根据情况设置
            WHEN q.question_type = 4 AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' AND
                 (TRIM(COALESCE(sa.answer_content, '')) = '' 
                  OR TRIM(LOWER(sa.answer_content)) = TRIM(LOWER(q.question_answer))) THEN v_current_time
            -- 简答题：根据情况设置
            WHEN q.question_type = 5 AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' AND
                 (TRIM(COALESCE(sa.answer_content, '')) = '' 
                  OR TRIM(LOWER(sa.answer_content)) = TRIM(LOWER(q.question_answer))) THEN v_current_time
            -- 其他情况保持原值，等待人工批改
            ELSE sa.marking_time
        END,
        sa.marking_user = CASE 
            -- 单选题和多选题：需要有有效答案才能设置批改人
            WHEN q.question_type IN (1,2) AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' THEN 'system'
            -- 判断题：需要有有效答案才能设置批改人
            WHEN q.question_type = 3 AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' THEN 'system'
            -- 填空题：根据情况设置
            WHEN q.question_type = 4 AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' AND
                 (TRIM(COALESCE(sa.answer_content, '')) = '' 
                  OR TRIM(LOWER(sa.answer_content)) = TRIM(LOWER(q.question_answer))) THEN 'system'
            -- 简答题：根据情况设置
            WHEN q.question_type = 5 AND q.question_answer IS NOT NULL 
                 AND TRIM(q.question_answer) != '' AND TRIM(q.question_answer) != 'None' AND
                 (TRIM(COALESCE(sa.answer_content, '')) = '' 
                  OR TRIM(LOWER(sa.answer_content)) = TRIM(LOWER(q.question_answer))) THEN 'system'
            -- 其他情况保持原值
            ELSE sa.marking_user
        END,
        sa.update_time = v_current_time
    WHERE sa.paper_id = p_paper_id;
    
    -- 计算总分（只计算已批改的题目分数）
    SELECT COALESCE(SUM(CASE WHEN marking_type = 1 THEN score ELSE 0 END), 0.00) INTO v_total_score
    FROM app_student_answer 
    WHERE paper_id = p_paper_id;
    
    -- 统计题目自动批改情况
    SELECT 
        COUNT(*) as total_questions,
        COUNT(CASE WHEN marking_type = 1 THEN 1 END) as auto_graded_questions
    INTO v_total_questions, v_auto_graded_questions
    FROM app_student_answer 
    WHERE paper_id = p_paper_id;
    
    -- 判断最终状态：如果所有题目都已自动批改，则设为已批改状态
    IF v_total_questions > 0 AND v_auto_graded_questions = v_total_questions THEN
        SET v_final_status = 3; -- 已批改
    ELSE
        SET v_final_status = 2; -- 已提交，等待人工批改
    END IF;
    
    -- 更新试卷状态和总分
    UPDATE app_student_paper 
    SET 
        status = v_final_status, 
        submit_time = v_current_time,
        total_score = v_total_score,
        update_time = v_current_time
    WHERE id = p_paper_id AND student_id = v_student_id;
    
    -- 返回结果
    IF v_final_status = 3 THEN
        SELECT 0 as result_code, CONCAT('考试完成并自动批改成功，总分：', v_total_score, '分') as message, v_total_score as total_score, v_final_status as final_status;
    ELSE
        SELECT 0 as result_code, CONCAT('考试提交成功，已自动批改部分题目，总分：', v_total_score, '分，剩余题目等待人工批改') as message, v_total_score as total_score, v_final_status as final_status;
    END IF;
    
    COMMIT;
END ;;

DELIMITER ; 