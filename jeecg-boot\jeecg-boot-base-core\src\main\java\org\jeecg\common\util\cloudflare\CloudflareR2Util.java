package org.jeecg.common.util.cloudflare;

import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.amazonaws.util.IOUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;

/**
 * Cloudflare R2 存储工具类
 */
@Slf4j
public class CloudflareR2Util {

    private static volatile CloudflareR2Util instance;
    
    @Autowired
    private AmazonS3 s3Client;
    
    private static AmazonS3 staticS3Client;
    
    @PostConstruct
    public void init() {
        synchronized (CloudflareR2Util.class) {
            if (instance == null) {
                instance = this;
                staticS3Client = this.s3Client;
                log.info("CloudflareR2Util initialized with S3Client and S3Presigner");
            }
        }
    }

    // Configuration fields
    private static String accessKey;
    private static String secretKey;
    private static String endpoint;
    private static String bucketName;
    private static String region = "auto"; // Cloudflare R2 uses 'auto' as the default region
    private static String publicUrl;
    private static String cdnUrl;
    private static boolean cdnEnabled = false;
    private static int presignedUrlExpiry = 604800; // 7 days default
    private static String accountId;

    public static void setAccessKey(String accessKey) {
        CloudflareR2Util.accessKey = accessKey;
    }

    public static void setSecretKey(String secretKey) {
        CloudflareR2Util.secretKey = secretKey;
    }

    public static void setEndpoint(String endpoint) {
        CloudflareR2Util.endpoint = endpoint;
    }

    public static void setBucketName(String bucketName) {
        CloudflareR2Util.bucketName = bucketName;
    }

    public static void setRegion(String region) {
        CloudflareR2Util.region = region;
    }

    public static void setPublicUrl(String publicUrl) {
        CloudflareR2Util.publicUrl = publicUrl;
    }

    public static void setCdnUrl(String cdnUrl) {
        CloudflareR2Util.cdnUrl = cdnUrl;
    }

    public static void setCdnEnabled(boolean cdnEnabled) {
        CloudflareR2Util.cdnEnabled = cdnEnabled;
    }

    public static void setPresignedUrlExpiry(int presignedUrlExpiry) {
        CloudflareR2Util.presignedUrlExpiry = presignedUrlExpiry;
    }

    public static void setAccountId(String accountId) {
        CloudflareR2Util.accountId = accountId;
    }

    /**
     * 初始化 Cloudflare R2 客户端
     */
    private static void initS3Client() {
        if (staticS3Client == null) {
            throw new IllegalStateException("CloudflareR2Util not initialized. Make sure it's properly configured in the Spring context.");
        }
        
        if (oConvertUtils.isEmpty(bucketName)) {
            throw new RuntimeException("Cloudflare R2 bucket name is not configured. Please set 'jeecg.cloudflare.bucket-name' or 'CLOUDFLARE_R2_BUCKET' environment variable.");
        }
        
        // Skip bucket existence check as it might fail due to permissions
        // Instead, we'll try to access the bucket when performing actual operations
        log.info("Using Cloudflare R2 bucket: {}", bucketName);
    }

    /**
     * 获取文件字节数组
     *
     * @param filePath 文件路径
     * @return 文件字节数组
     */
    public static byte[] getFileBytes(String filePath) {
        try (S3Object object = staticS3Client.getObject(bucketName, filePath);
             S3ObjectInputStream inputStream = object.getObjectContent()) {
            return IOUtils.toByteArray(inputStream);
        } catch (IOException e) {
            log.error("获取文件字节数组失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取文件字节数组失败: " + e.getMessage(), e);
        }
    }

    /**
     * 上传文件
     *
     * @param file     文件
     * @param bizPath 业务路径
     * @return 文件访问URL
     */
    public static String upload(MultipartFile file, String bizPath) {
        try (InputStream inputStream = file.getInputStream()) {
            return upload(inputStream, bizPath, file.getOriginalFilename());
        } catch (IOException e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 上传文件
     *
     * @param inputStream 文件流
     * @param bizPathOrKey 业务路径或完整key
     * @param originalFilenameOrContentType 原始文件名或内容类型
     * @return 文件访问URL
     */
    public static String upload(InputStream inputStream, String bizPathOrKey, String originalFilenameOrContentType) {
        if (inputStream == null) {
            throw new IllegalArgumentException("Input stream cannot be null");
        }

        if (staticS3Client == null) {
            throw new IllegalStateException("CloudflareR2Util not initialized. Make sure it's properly configured in the Spring context.");
        }

        if (oConvertUtils.isEmpty(bucketName)) {
            throw new RuntimeException("Cloudflare R2 bucket name is not configured.");
        }

        try {
            // 如果bizPathOrKey是完整的key，则直接使用
            String fileKey;
            if (bizPathOrKey != null && (bizPathOrKey.contains(".") || bizPathOrKey.contains("/"))) {
                fileKey = bizPathOrKey;
            } else {
                // 否则构建key
                fileKey = buildFilePath(bizPathOrKey, 
                    originalFilenameOrContentType != null ? originalFilenameOrContentType : "file");
            }

            // 确保文件key不以/开头
            if (fileKey.startsWith("/")) {
                fileKey = fileKey.substring(1);
            }

            // 设置元数据
            ObjectMetadata metadata = new ObjectMetadata();
            
            // 尝试从输入流中获取内容类型
            String contentType = "application/octet-stream";
            if (originalFilenameOrContentType != null) {
                if (originalFilenameOrContentType.contains(".")) {
                    // 如果提供了原始文件名，尝试设置content-type
                    String extension = originalFilenameOrContentType.substring(originalFilenameOrContentType.lastIndexOf(".") + 1).toLowerCase();
                    contentType = getContentType(extension);
                } else if (originalFilenameOrContentType.contains("/")) {
                    // 可能是直接提供了content-type
                    contentType = originalFilenameOrContentType;
                }
            }
            metadata.setContentType(contentType);
            
            // 对于流上传，需要设置内容长度（如果可能）
            try {
                if (inputStream.available() > 0) {
                    metadata.setContentLength(inputStream.available());
                }
            } catch (IOException e) {
                // 如果不能获取流长度，忽略，让SDK处理分块上传
                log.debug("Could not determine input stream length: {}", e.getMessage());
            }

            log.info("Uploading file to Cloudflare R2: {}/{}, Content-Type: {}", bucketName, fileKey, contentType);
            
            // 上传文件
            staticS3Client.putObject(bucketName, fileKey, inputStream, metadata);
            log.info("File uploaded successfully to Cloudflare R2: {}/{}", bucketName, fileKey);

            // 返回文件访问URL
            String fileUrl = getFileUrl(fileKey);
            
            // 添加详细的日志输出
            log.info("=== Cloudflare R2 Upload Details ===");
            log.info("Bucket: {}", bucketName);
            log.info("File Key: {}", fileKey);
            log.info("Public URL: {}", publicUrl);
            log.info("CDN Enabled: {}", cdnEnabled);
            log.info("CDN URL: {}", cdnUrl);
            log.info("Generated URL: {}", fileUrl);
            log.info("==================================");
            
            // 输出到控制台以便于调试
            System.out.println("\n=== Cloudflare R2 Upload Details ===");
            System.out.println("Bucket: " + bucketName);
            System.out.println("File Key: " + fileKey);
            System.out.println("Public URL: " + publicUrl);
            System.out.println("CDN Enabled: " + cdnEnabled);
            System.out.println("CDN URL: " + cdnUrl);
            System.out.println("Generated URL: " + fileUrl);
            System.out.println("==================================\n");
            
            return fileUrl;
            
        } catch (Exception e) {
            log.error("Failed to upload file to Cloudflare R2: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to upload file to Cloudflare R2: " + e.getMessage(), e);
        } finally {
            // 确保输入流被关闭
            try {
                inputStream.close();
            } catch (IOException e) {
                log.warn("Failed to close input stream: {}", e.getMessage());
            }
        }
    }

    /**
     * 上传文件到指定路径
     *
     * @param file     文件
     * @param filePath 文件路径（完整路径）
     * @return 文件访问URL
     */
    public static String uploadToPath(MultipartFile file, String filePath) {
        try (InputStream inputStream = file.getInputStream()) {
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(file.getContentType());
            metadata.setContentLength(file.getSize());
            
            // 由于S3客户端会自动关闭输入流，我们需要使用ByteArrayInputStream
            byte[] bytes = IOUtils.toByteArray(inputStream);
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, filePath, 
                new ByteArrayInputStream(bytes), metadata);
                
            staticS3Client.putObject(putObjectRequest);
            return getFileUrl(filePath);
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取文件访问URL
     *
     * @param filePath 文件路径
     * @return 文件访问URL
        
        // 否则生成预签名URL
        if (oConvertUtils.isNotEmpty(cleanPath)) {
            return getPresignedUrl(cleanPath);
        }
        
        return "";
    }
    
    /**
     * 获取文件访问URL
     *
     * @param filePath 文件路径
     * @return 文件访问URL
     */
    public static String getFileUrl(String filePath) {
        if (oConvertUtils.isEmpty(filePath)) {
            log.warn("getFileUrl called with empty filePath");
            return "";
        }

        if (staticS3Client == null) {
            String errorMsg = "CloudflareR2Util not initialized. Make sure it's properly configured in the Spring context.";
            log.error(errorMsg);
            return "";
        }

        try {
            // 移除路径开头的斜杠（如果有）
            String cleanPath = filePath.startsWith("/") ? filePath.substring(1) : filePath;
            
            // 如果启用了CDN并且配置了CDN URL，则使用CDN URL
            if (cdnEnabled && oConvertUtils.isNotEmpty(cdnUrl)) {
                String cdnBase = cdnUrl.endsWith("/") ? cdnUrl.substring(0, cdnUrl.length() - 1) : cdnUrl;
                String url = String.format("%s/%s", cdnBase, cleanPath);
                log.debug("Using CDN URL: {}", url);
                return url;
            }
            
            // 如果配置了publicUrl，则使用publicUrl
            if (oConvertUtils.isNotEmpty(publicUrl)) {
                String baseUrl = publicUrl.endsWith("/") ? publicUrl.substring(0, publicUrl.length() - 1) : publicUrl;
                String url = String.format("%s/%s", baseUrl, cleanPath);
                log.debug("Using public URL: {}", url);
                return url;
            }
            
            // 否则生成预签名URL
            if (oConvertUtils.isNotEmpty(cleanPath)) {
                log.debug("Generating presigned URL for: {}", cleanPath);
                String presignedUrl = getPresignedUrl(cleanPath);
                log.debug("Generated presigned URL: {}", presignedUrl);
                return presignedUrl;
            }
            
            log.warn("No valid URL could be generated for file: {}", filePath);
            return "";
        } catch (Exception e) {
            String errorMsg = String.format("获取文件URL失败: %s (filePath: %s)", e.getMessage(), filePath);
            log.error(errorMsg, e);
            return "";
        }
    }
    
    /**
     * 获取预签名URL
     *
     * @param key 文件名
     * @return 预签名URL
     */
    public static String getPresignedUrl(String key) {
        return getPresignedUrl(key, presignedUrlExpiry);
    }

    public static String getPresignedUrl(String key, int expiryInSeconds) {
        if (staticS3Client == null) {
            throw new IllegalStateException("CloudflareR2Util not initialized. Make sure it's properly configured in the Spring context.");
        }
        
        try {
            Date expiration = new Date();
            long expTimeMillis = expiration.getTime() + (expiryInSeconds * 1000);
            expiration.setTime(expTimeMillis);
            
            GeneratePresignedUrlRequest generatePresignedUrlRequest = 
                new GeneratePresignedUrlRequest(bucketName, key)
                    .withMethod(HttpMethod.GET)
                    .withExpiration(expiration);
                    
            URL url = staticS3Client.generatePresignedUrl(generatePresignedUrlRequest);
            return url.toString();
        } catch (Exception e) {
            log.error("Failed to generate presigned URL: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to generate presigned URL: " + e.getMessage(), e);
        }
    }

    /**
     * 构建文件路径
     */
    /**
     * 根据文件扩展名获取对应的Content-Type
     *
     * @param extension 文件扩展名（不含点）
     * @return 对应的Content-Type
     */
    private static String getContentType(String extension) {
        if (extension == null || extension.isEmpty()) {
            return "application/octet-stream";
        }

        switch (extension.toLowerCase()) {
            // 图片
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "webp":
                return "image/webp";
            case "svg":
                return "image/svg+xml";
            case "ico":
                return "image/x-icon";
                
            // 文档
            case "pdf":
                return "application/pdf";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls":
                return "application/vnd.ms-excel";
            case "xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "ppt":
                return "application/vnd.ms-powerpoint";
            case "pptx":
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            case "txt":
                return "text/plain";
            case "csv":
                return "text/csv";
                
            // 压缩文件
            case "zip":
                return "application/zip";
            case "rar":
                return "application/x-rar-compressed";
            case "7z":
                return "application/x-7z-compressed";
                
            // 音频
            case "mp3":
                return "audio/mpeg";
            case "wav":
                return "audio/wav";
            case "ogg":
                return "audio/ogg";
                
            // 视频
            case "mp4":
                return "video/mp4";
            case "webm":
                return "video/webm";
            case "mov":
                return "video/quicktime";
                
            // 其他
            case "json":
                return "application/json";
            case "xml":
                return "application/xml";
            case "html":
                return "text/html";
            case "css":
                return "text/css";
            case "js":
                return "application/javascript";
                
            default:
                return "application/octet-stream";
        }
    }
    
    /**
     * 构建文件路径
     */
    private static String buildFilePath(String bizPath, String originalFilename) {
        String fileName = System.currentTimeMillis() + "_" + originalFilename;
        if (oConvertUtils.isNotEmpty(bizPath)) {
            return bizPath + "/" + fileName;
        }
        return fileName;
    }

    /**
     * 删除文件
     *
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    public static boolean delete(String filePath) {
        try {
            staticS3Client.deleteObject(bucketName, filePath);
            return true;
        } catch (Exception e) {
            log.error("删除文件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取文件流
     *
     * @param filePath 文件路径
     * @return 文件流
     */
    public static InputStream getFileStream(String filePath) {
        if (oConvertUtils.isEmpty(filePath)) {
            log.warn("File path is empty");
            return null;
        }
        
        try {
            S3Object object = staticS3Client.getObject(bucketName, filePath);
            return object.getObjectContent();
        } catch (Exception e) {
            log.error("获取文件流失败: {}", e.getMessage(), e);
            return null;
        }
    }
}
