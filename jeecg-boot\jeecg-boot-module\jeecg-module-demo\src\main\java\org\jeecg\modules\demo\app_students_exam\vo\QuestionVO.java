package org.jeecg.modules.demo.app_students_exam.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * @Description: 考题VO
 * @Author: jeecg-boot
 * @Date: 2023-06-01
 * @Version: V1.0
 */
@Data
public class QuestionVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private String id;
    
    /**
     * 题目标题
     */
    private String title;
    
    /**
     * 题目类型（radio-单选题，checkbox-多选题，judge-判断题，text-简答题）
     */
    private String type;
    
    /**
     * 选项
     */
    private List<Object> options;
    
    /**
     * 分值
     */
    private Integer score;

    /**
     * 题目内容
     */
    private String question_content;

    /**
     * 题目类型（冗余字段，便于前端直接用）
     */
    private String question_type;

    /**
     * 学生已保存的答案
     */
    private String studentAnswer;
} 