package org.jeecg.modules.demo.app_students_exam.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description: 学生试卷详情VO
 * @Author: JeecgBoot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
public class StudentPaperDetailVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**试卷基本信息*/
    private StudentPaperVO paperInfo;
    
    /**题目列表*/
    private List<StudentAnswerQuestionVO> questions;
    
    /**考试信息*/
    private ExamInfoVO examInfo;
    
    /**
     * 考试信息VO
     */
    @Data
    public static class ExamInfoVO implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**考试名称*/
        private String examName;
        
        /**课程名称*/
        private String courseName;
        
        /**总分*/
        private Double totalScore;
        
        /**题目数量*/
        private Integer questionCount;
        
        /**考试时长（分钟）*/
        private Integer duration;
        
        /**考试描述*/
        private String description;
    }
} 