# 学生考试流程的SQL实现示例

以下是学生考试流程中各个步骤的详细SQL实现，包括学生参加考试和答题过程的数据库操作。

## 1. 学生参加考试

### 1.1 查询可参加的考试

```sql
-- 查询当前可参加的考试列表
-- 条件：考试状态为有效、当前时间在考试开始和结束时间之间、学生未参加过该考试
SELECT e.* 
FROM app_exam e
LEFT JOIN app_student_paper sp ON e.id = sp.exam_id AND sp.student_id = '学生ID'
WHERE e.status = 1 
  AND NOW() BETWEEN e.start_time AND e.end_time
  AND sp.id IS NULL;
```

### 1.2 创建学生试卷记录

```sql
-- 学生开始考试，创建试卷记录
INSERT INTO app_student_paper (
  id, student_id, exam_id, start_time, status, create_time, create_by
) VALUES (
  UUID(), -- 或使用自增ID
  '学生ID',
  '考试ID',
  NOW(), -- 开始时间
  1, -- 状态：1-进行中
  NOW(),
  '学生ID'
);

-- 获取刚插入的试卷ID
SET @student_paper_id = LAST_INSERT_ID(); -- 如果使用自增ID
-- 或者使用UUID()方式
-- SELECT @student_paper_id := id FROM app_student_paper WHERE student_id = '学生ID' AND exam_id = '考试ID' ORDER BY create_time DESC LIMIT 1;
```

### 1.3 获取试卷题目

```sql
-- 获取该考试关联的课程
SELECT course_id 
FROM app_exam 
WHERE id = '考试ID';

-- 获取课程下的所有题目
SELECT q.* 
FROM app_question q
INNER JOIN app_course_question cq ON q.id = cq.question_id
WHERE cq.course_id = (SELECT course_id FROM app_exam WHERE id = '考试ID')
ORDER BY cq.question_order;

-- 对于选择题，获取选项
SELECT qo.*
FROM app_question_option qo
INNER JOIN app_question q ON qo.question_id = q.id
INNER JOIN app_course_question cq ON q.id = cq.question_id
WHERE cq.course_id = (SELECT course_id FROM app_exam WHERE id = '考试ID') 
AND q.question_type IN (1, 2) -- 1-单选题，2-多选题
ORDER BY cq.question_order, qo.option_order;
```

## 2. 答题过程

### 2.1 记录学生答案（普通题目）

```sql
-- 插入学生答案记录
INSERT INTO app_student_answer (
  id, student_paper_id, question_id, answer_content, answer_time, score, status
) VALUES (
  UUID(), -- 或使用自增ID
  @student_paper_id,
  '题目ID',
  '答案内容', -- 文本类答案直接存储
  NOW(),
  NULL, -- 得分，暂时为空
  1 -- 状态：1-已作答
);

-- 获取刚插入的答案ID
SET @student_answer_id = LAST_INSERT_ID(); -- 如果使用自增ID
```

### 2.2 记录选择题答案

```sql
-- 对于选择题，记录选项答案（单选题只有一条记录，多选题有多条记录）
INSERT INTO app_student_option_answer (
  id, student_answer_id, option_id
) VALUES (
  UUID(), -- 或使用自增ID
  @student_answer_id,
  '选项ID'
);

-- 多选题可能需要多次插入不同的选项
INSERT INTO app_student_option_answer (
  id, student_answer_id, option_id
) VALUES (
  UUID(), -- 或使用自增ID
  @student_answer_id,
  '另一个选项ID'
);
```

### 2.3 更新答题时间

```sql
-- 更新学生最后答题时间
UPDATE app_student_answer
SET answer_time = NOW()
WHERE id = @student_answer_id;
```

### 2.4 自动评分（选择题）

```sql
-- 单选题自动评分存储过程示例
DELIMITER //
CREATE PROCEDURE auto_grade_single_choice(IN p_student_answer_id VARCHAR(36))
BEGIN
    DECLARE v_correct INT DEFAULT 0;
    DECLARE v_question_id VARCHAR(36);
    DECLARE v_question_score DECIMAL(10,2);
    
    -- 获取题目ID和分值
    SELECT sa.question_id, q.score INTO v_question_id, v_question_score
    FROM app_student_answer sa
    JOIN app_question q ON sa.question_id = q.id
    WHERE sa.id = p_student_answer_id;
    
    -- 检查答案是否正确（单选题）
    SELECT COUNT(*) INTO v_correct
    FROM app_student_option_answer soa
    JOIN app_question_option qo ON soa.option_id = qo.id
    WHERE soa.student_answer_id = p_student_answer_id
    AND qo.is_correct = 1
    AND NOT EXISTS (
        SELECT 1 FROM app_student_option_answer soa2
        WHERE soa2.student_answer_id = p_student_answer_id
        AND NOT EXISTS (
            SELECT 1 FROM app_question_option qo2
            WHERE qo2.id = soa2.option_id AND qo2.is_correct = 1
        )
    );
    
    -- 更新分数
    IF v_correct > 0 THEN
        UPDATE app_student_answer
        SET score = v_question_score, status = 2 -- 2-已评分
        WHERE id = p_student_answer_id;
    ELSE
        UPDATE app_student_answer
        SET score = 0, status = 2 -- 2-已评分
        WHERE id = p_student_answer_id;
    END IF;
END //
DELIMITER ;
```

### 2.5 完成考试

```sql
-- 学生提交试卷，更新试卷状态和结束时间
UPDATE app_student_paper
SET 
  status = 2, -- 状态：2-已完成
  end_time = NOW(),
  used_time = TIMESTAMPDIFF(SECOND, start_time, NOW())
WHERE id = @student_paper_id;

-- 计算总分并更新
UPDATE app_student_paper sp
SET total_score = (
  SELECT COALESCE(SUM(sa.score), 0)
  FROM app_student_answer sa
  WHERE sa.student_paper_id = sp.id
)
WHERE sp.id = @student_paper_id;
```

## 3. 事务处理示例

```sql
-- 使用事务确保数据一致性
DELIMITER //
CREATE PROCEDURE start_exam(IN p_student_id VARCHAR(36), IN p_exam_id VARCHAR(36))
BEGIN
    DECLARE v_student_paper_id VARCHAR(36);
    DECLARE v_course_id VARCHAR(36);
    
    -- 开始事务
    START TRANSACTION;
    
    -- 获取考试关联的课程ID
    SELECT course_id INTO v_course_id FROM app_exam WHERE id = p_exam_id;
    
    -- 生成UUID
    SET v_student_paper_id = UUID();
    
    -- 创建试卷记录
    INSERT INTO app_student_paper (
      id, student_id, exam_id, start_time, status, create_time, create_by
    ) VALUES (
      v_student_paper_id,
      p_student_id,
      p_exam_id,
      NOW(),
      1,
      NOW(),
      p_student_id
    );
    
    -- 提交事务
    COMMIT;
    
    -- 返回试卷ID和课程ID
    SELECT v_student_paper_id AS student_paper_id, v_course_id AS course_id;
    
END //
DELIMITER ;
```