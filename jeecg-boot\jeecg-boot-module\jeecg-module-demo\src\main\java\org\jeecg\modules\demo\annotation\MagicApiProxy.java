package org.jeecg.modules.demo.annotation;

import java.lang.annotation.*;

/**
 * Annotation to mark a method as a proxy to Magic API
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface MagicApiProxy {
    /**
     * The API endpoint path (e.g., "/demo/1")
     */
    String value();
    
    /**
     * The HTTP method to use (GET, POST, etc.)
     */
    String method() default "GET";
}
