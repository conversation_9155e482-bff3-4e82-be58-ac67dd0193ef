package org.jeecg.modules.demo.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.demo.annotation.MagicApiProxy;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UrlPathHelper;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: Controller to proxy requests under /demo/** to Magic API for Teacher and Student roles.
 * @Author: <PERSON> (AI Assistant)
 * @Date: 2025-05-28
 * @Version: V1.0
 */
@RestController
@RequestMapping("/demo") // Handles all requests starting with /demo
@Tag(name = "Demo Proxy (Teacher/Student)", description = "Proxy for /demo/** for Teachers and Students. All subpaths under /demo will be proxied to Magic API.")
@Slf4j
public class DemoProxyController {

    @Operation(
        summary = "Proxy /demo/** to Magic API (Teacher/Student)",
        description = "Dynamically proxies requests under /demo/** to the corresponding path in Magic API. Requires 'teacher' OR 'student' role."
    )
    @RequestMapping(value = "/**", method = {
        RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, 
        RequestMethod.PATCH, RequestMethod.OPTIONS, RequestMethod.HEAD, RequestMethod.TRACE
    })
    @RequiresRoles(value = {"teacher", "student"}, logical = Logical.OR) // User must have at least one of these roles
    @AutoLog(value = "Demo Proxy Request")
    public Result<?> proxyRequests(HttpServletRequest request) {
        // Get the full path within the application context, e.g., /demo/some/path or /appname/demo/some/path
        String path = new UrlPathHelper().getPathWithinApplication(request);
        log.info("DemoProxyController: User with roles attempting to proxy path: {}. Forwarding to Magic API.", path);
        
        // The MagicApiProxyAspect is expected to use the 'targetPath' argument from doProxy 
        // as the specific path for the magic-api.
        // If magic-api is at http://host/magic-api-base, and request is /demo/service,
        // aspect will call http://host/magic-api-base/demo/service if path is /demo/service.
        return doProxy(path, request);
    }

    /**
     * Private helper method annotated with @MagicApiProxy.
     * The MagicApiProxyAspect intercepts calls to this method.
     * @param targetPath The path to be proxied to Magic API (e.g., /demo/some/endpoint).
     * @param originalRequest The original HttpServletRequest, allowing the aspect to forward details.
     * @return Null, as the aspect handles the response.
     */
    @MagicApiProxy("") // The Aspect should use the first String argument ('targetPath') as the path for magic-api.
    private Result<?> doProxy(String targetPath, HttpServletRequest originalRequest) {
        // This method's body is intentionally left empty or null.
        // The MagicApiProxyAspect intercepts calls to this method and handles the actual proxying logic.
        // It uses 'targetPath' to construct the URL for the magic-api
        // and 'originalRequest' to forward necessary details like headers, body, query parameters, and HTTP method.
        return null;
    }
}
