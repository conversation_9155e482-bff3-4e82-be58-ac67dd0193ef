package org.jeecg.modules.demo.app_students_exam.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 学生答题详情VO
 * @Author: JeecgBoot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
public class StudentAnswerQuestionVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**答题记录ID*/
    private String id;
    
    /**题目ID*/
    private String questionId;
    
    /**题目内容*/
    private String question_content;
    
    /**题目标题*/
    private String title;
    
    /**题目类型（数字）*/
    private String question_type;
    
    /**题目类型（字符串）*/
    private String type;
    
    /**选项列表*/
    private List<QuestionOptionVO> options;
    
    /**正确答案*/
    private String correct_answer;
    
    /**学生答案*/
    private String student_answer;
    
    /**满分*/
    private Double max_score;
    
    /**得分*/
    private Double score;
    
    /**是否正确*/
    private Boolean is_correct;
    
    /**是否已批改*/
    private Boolean is_reviewed;
    
    /**批改备注*/
    private String review_comment;
    
    /**题目分析*/
    private String question_analysis;
    
    /**
     * 题目选项VO
     */
    @Data
    public static class QuestionOptionVO implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**选项标签*/
        private String label;
        
        /**选项值*/
        private String value;
        
        /**选项编码*/
        private String option_code;
        
        /**选项内容*/
        private String option_content;
        
        /**是否正确答案*/
        private String is_correct;
    }
} 