package org.jeecg.common.util.cloudflare;

import com.amazonaws.services.s3.AmazonS3;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
public class CloudflareR2UtilTest {

    @Autowired
    private CloudflareR2Util cloudflareR2Util;

    @Test
    public void testConfigurationLoaded() {
        // This test verifies that the CloudflareR2Util bean is properly initialized
        assertNotNull(cloudflareR2Util, "CloudflareR2Util bean should be initialized");
        
        // The actual configuration values are set through Spring's @Value annotations
        // and should be verified through the application's behavior rather than direct field access
    }
}
