<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jeecg-module-system</artifactId>
        <groupId>org.jeecgframework.boot</groupId>
        <version>3.8.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jeecg-system-start</artifactId>
    
    <dependencies>
        <!-- SYSTEM 系统管理模块 -->
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-system-biz</artifactId>
            <version>${jeecgboot.version}</version>
        </dependency>
        <!-- DEMO 示例模块 -->
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-module-demo</artifactId>
            <version>${jeecgboot.version}</version>
        </dependency>
        
        <!-- AI大模型管理 -->
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-boot-module-airag</artifactId>
            <version>${jeecgboot.version}</version>
        </dependency>
        
        <!-- flyway 数据库自动升级 -->
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
        </dependency>

        <!-- Spring Boot DevTools -->
        <!--
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional>
            <scope>runtime</scope>
        </dependency>
        -->
        
        <!-- AWS SDK v1 for Cloudflare R2 -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-kms</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>