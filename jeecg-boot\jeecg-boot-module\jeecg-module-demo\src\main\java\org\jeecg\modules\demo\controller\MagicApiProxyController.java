package org.jeecg.modules.demo.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.demo.annotation.MagicApiProxy;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: Magic API代理控制器
 * @Author: jeecg-boot
 * @Date: 2023-06-03
 * @Version: V1.0
 */
@Tag(name = "Magic API代理")
@RestController
@RequestMapping("/magicapi")
@Slf4j
public class MagicApiProxyController {

    /**
     * 学生API代理
     * http://127.0.0.1:8080/jeecg-boot/magicapi/student/**
     * 代理到: http://localhost:9999/student/**
     * 需要学生角色
     * 
     * @return 代理返回的数据
     */
    @AutoLog(value = "Magic API学生代理")
    @Operation(summary = "Magic API学生代理", description = "代理到Magic API的学生接口")
    @RequiresRoles("stu")  // 需要学生角色
    @RequestMapping(value = "/student/**", method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE})
    @MagicApiProxy("/student")  // 代理到Magic API的/student路径
    public Result<?> proxyStudentApis() {
        // 由MagicApiProxyAspect处理
        return null;
    }

    /**
     * 教师API代理
     * http://127.0.0.1:8080/jeecg-boot/magicapi/teacher/**
     * 代理到: http://localhost:9999/teacher/**
     * 需要教师角色
     * 
     * @return 代理返回的数据
     */
    @AutoLog(value = "Magic API教师代理")
    @Operation(summary = "Magic API教师代理", description = "代理到Magic API的教师接口")
    @RequiresRoles("te")  // 需要教师角色编码 te
    @RequestMapping(value = "/teacher/**", method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE})
    @MagicApiProxy("/teacher")  // 代理到Magic API的/teacher路径
    public Result<?> proxyTeacherApis() {
        // 由MagicApiProxyAspect处理
        return null;
    }
} 